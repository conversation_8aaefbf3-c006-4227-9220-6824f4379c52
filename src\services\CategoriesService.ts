import ApiService from './ApiService'
import type {
    Category,
    CategoryDetail,
    CategoryTree,
} from '@/@types/categories'

const BASE_ROUTE = '/Categories'

// Get detailed information about a specific category
export async function getCategoryById(id: string | number, { ...params }) {
    return ApiService.get<CategoryDetail>(`${BASE_ROUTE}/${id}`, {
        params,
    })
}

// Update existing category information
export async function updateCategory(id: string | number, name: string) {
    return ApiService.put(`${BASE_ROUTE}/${id}`, { name })
}

// Delete a category (soft delete)
export async function deleteCategory(id: string | number) {
    return ApiService.delete(`${BASE_ROUTE}/${id}`)
}

// Get categories by status
export async function getCategoriesByStatus({ ...params }) {
    return ApiService.get<Category[]>(
        `${BASE_ROUTE}/by-status/${params.status}`,
        {
            params,
        },
    )
}

// Get approved leaf categories (no children)
export async function getLeafCategories() {
    return ApiService.get<Category[]>(`${BASE_ROUTE}/leaves`)
}

// Get category tree structure
export async function getCategoryTree({ ...params }) {
    return ApiService.get<CategoryTree[]>(`${BASE_ROUTE}/tree`, {
        params,
    })
}

// Create new category
export async function createCategory({
    name,
    parentId,
}: {
    name: string
    parentId?: number | null
}) {
    return ApiService.post(`${BASE_ROUTE}`, { name, parentId })
}

// Update category status
export async function updateCategoryStatus(
    id: string | number,
    newStatus: number,
) {
    return ApiService.patch(`${BASE_ROUTE}/${id}/status`, { newStatus })
}

// Move category to different parent
export async function moveCategoryToParent(
    id: string | number,
    parentId: string | number | null,
) {
    return ApiService.patch(`${BASE_ROUTE}/${id}/move`, { parentId })
}

// Export types for convenience
export type { Category, CategoryDetail, CategoryTree }
