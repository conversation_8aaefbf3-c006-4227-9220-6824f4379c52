import { useMemo } from 'react'
import { useSearchParams } from 'react-router-dom'
import DataTable from '@/components/shared/DataTable'
import type { ColumnDef } from '@/components/shared/DataTable'
import useTranslation from '@/utils/hooks/useTranslation'
import { File } from '@/@types/file'
import { useGetFilesByStation } from '@/hooks/files'
import { useGetMyDigitizationStations } from '@/hooks/digitization-station'
import Actions from './Actions'
import { FILE_STATUS_CONFIG, MEDIUM_TYPE_CONFIG } from '../types'

const DailyListTable = () => {
    const { t } = useTranslation()
    const [searchParams, setSearchParams] = useSearchParams()
    const { data: myStation = [] } = useGetMyDigitizationStations()
    const { Files, isLoading, pagination } = useGetFilesByStation({
        stationId: myStation?.[0]?.id || 0,
    })

    const handlePaginationChange = (page: number) => {
        const newSearchParams = new URLSearchParams(searchParams)
        newSearchParams.set('pageNumber', page.toString())
        setSearchParams(newSearchParams)
    }

    const handlePageSizeChange = (pageSize: number) => {
        const newSearchParams = new URLSearchParams(searchParams)
        newSearchParams.set('pageSize', pageSize.toString())
        newSearchParams.set('pageNumber', '1')
        setSearchParams(newSearchParams)
    }

    const tableColumns: ColumnDef<File>[] = useMemo(() => {
        const cols: ColumnDef<File>[] = [
            {
                header: 'ID',
                accessorKey: 'fileId',
                cell: ({ row }) => (
                    <div className="font-mono text-sm">
                        {row.original.fileId}
                    </div>
                ),
            },
            {
                header: 'title',
                accessorKey: 'fileTitle',
                cell: ({ row }) => (
                    <div className="font-medium">{row.original.fileTitle}</div>
                ),
            },

            {
                header: 'Documents',
                accessorKey: 'numberOfDocuments',
            },
            {
                header: 'pages',
                accessorKey: 'numberOfPages',
            },
            {
                header: 'Category',
                accessorKey: 'categoryName',
                cell: ({ row }) => (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border bg-blue-100 text-blue-800 border-blue-200">
                        {row.original.categoryName || 'No Category'}
                    </span>
                ),
                enableSorting: false,
            },
            {
                header: 'Type',
                accessorKey: 'mediumType',
                cell: ({ row }) => {
                    const mediumType =
                        MEDIUM_TYPE_CONFIG[
                            row.original
                                .mediumType as keyof typeof MEDIUM_TYPE_CONFIG
                        ]
                    return (
                        <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${mediumType?.className || 'bg-gray-100 text-gray-800 border-gray-200'}`}
                        >
                            {mediumType?.label || 'Unknown'}
                        </span>
                    )
                },
                enableSorting: false,
            },
            {
                header: 'Status',
                accessorKey: 'fileStatus',
                cell: ({ row }) => {
                    const status =
                        FILE_STATUS_CONFIG[
                            row.original
                                .fileStatus as keyof typeof FILE_STATUS_CONFIG
                        ]
                    return (
                        <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${status?.className || 'bg-gray-100 text-gray-800 border-gray-200'}`}
                        >
                            {status?.label || 'Unknown'}
                        </span>
                    )
                },
                enableSorting: false,
            },
            {
                header: t('nav.shared.actions'),
                accessorKey: 'action',
                cell: ({ row }) => <Actions fileData={row.original} />,
                enableSorting: false,
            },
        ]

        return cols
    }, [t])

    return (
        <>
            <DataTable
                selectable
                columns={tableColumns}
                data={Files}
                loading={isLoading}
                skeletonAvatarColumns={[0]}
                skeletonAvatarProps={{ width: 14, height: 14 }}
                cellBorder={true}
                pagingData={{
                    total: pagination?.totalCount || 0,
                    pageIndex: pagination?.pageNumber || 1,
                    pageSize: pagination?.pageSize || 10,
                }}
                onPaginationChange={handlePaginationChange}
                onSelectChange={handlePageSizeChange}
            />
        </>
    )
}

export default DailyListTable
