import { useState } from 'react'
import { useF<PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import Dialog from '@/components/ui/Dialog'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import { FormItem, Form } from '@/components/ui/Form'
import useTranslation from '@/utils/hooks/useTranslation'
import { TbCheck, TbStack } from 'react-icons/tb'

interface AddShelfModalProps {
    isOpen: boolean
    onClose: () => void
    onSubmit: (data: ShelfFormData) => void
}

interface ShelfFormData {
    numberOfShelves: number
    width: number
    height: number
    numberOfBoxes: number
}

const AddShelfModal = ({ isOpen, onClose, onSubmit }: AddShelfModalProps) => {
    const { t } = useTranslation()
    const [loading, setLoading] = useState(false)

    const shelfSchema = z.object({
        numberOfShelves: z
            .number()
            .min(1, { message: t('nav.warehouses.numberOfShelvesRequired') }),
        width: z
            .number()
            .min(0.1, { message: t('nav.warehouses.shelfWidthRequired') }),
        height: z
            .number()
            .min(0.1, { message: t('nav.warehouses.shelfHeightRequired') }),
        numberOfBoxes: z
            .number()
            .min(1, { message: t('nav.warehouses.numberOfBoxesRequired') }),
    })

    const {
        handleSubmit,
        control,
        reset,
        formState: { errors },
    } = useForm<ShelfFormData>({
        defaultValues: {
            numberOfShelves: 0,
            width: 0,
            height: 0,
            numberOfBoxes: 0,
        },
        resolver: zodResolver(shelfSchema),
    })

    const handleFormSubmit = async (data: ShelfFormData) => {
        setLoading(true)
        try {
            // Simulate API call
            await new Promise((resolve) => setTimeout(resolve, 1000))
            onSubmit(data)
            reset()
        } catch (error) {
            console.error('Error creating shelf:', error)
        } finally {
            setLoading(false)
        }
    }

    const handleClose = () => {
        reset()
        onClose()
    }

    return (
        <Dialog
            isOpen={isOpen}
            width={600}
            height={500}
            onClose={handleClose}
            onRequestClose={handleClose}
        >
            <div className="flex flex-col h-full max-w-4xl mx-auto">
                {/* Header */}
                <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                    <div className="flex items-center gap-3">
                        <div className="flex items-center justify-center w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg">
                            <TbStack className="w-5 h-5 text-green-600 dark:text-green-400" />
                        </div>
                        <div>
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                {t('nav.warehouses.configureShelves')}
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                                {t('nav.warehouses.configureShelvesDesc')}
                            </p>
                        </div>
                    </div>
                </div>

                {/* Form */}
                <div className="flex-1 overflow-y-auto p-6">
                    <Form onSubmit={handleSubmit(handleFormSubmit)}>
                        <div className="space-y-6">
                            {/* Shelves Section */}
                            <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                                <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-4">
                                    {t('nav.warehouses.shelvesConfiguration')}
                                </h4>
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <FormItem
                                        label={`${t('nav.warehouses.numberOfShelves')} *`}
                                        invalid={!!errors.numberOfShelves}
                                        errorMessage={
                                            errors.numberOfShelves?.message
                                        }
                                    >
                                        <Controller
                                            name="numberOfShelves"
                                            control={control}
                                            render={({ field }) => (
                                                <Input
                                                    {...field}
                                                    type="number"
                                                    min="1"
                                                    placeholder="0"
                                                    onChange={(e) =>
                                                        field.onChange(
                                                            parseInt(
                                                                e.target.value,
                                                            ) || 0,
                                                        )
                                                    }
                                                />
                                            )}
                                        />
                                    </FormItem>
                                    <FormItem
                                        label={`${t('nav.warehouses.numberOfBoxes')} *`}
                                        invalid={!!errors.numberOfBoxes}
                                        errorMessage={
                                            errors.numberOfBoxes?.message
                                        }
                                    >
                                        <Controller
                                            name="numberOfBoxes"
                                            control={control}
                                            render={({ field }) => (
                                                <Input
                                                    {...field}
                                                    type="number"
                                                    min="1"
                                                    placeholder="0"
                                                    onChange={(e) =>
                                                        field.onChange(
                                                            parseInt(
                                                                e.target.value,
                                                            ) || 0,
                                                        )
                                                    }
                                                />
                                            )}
                                        />
                                    </FormItem>
                                    <FormItem
                                        label={`${t('nav.warehouses.shelfWidth')} *`}
                                        invalid={!!errors.width}
                                        errorMessage={errors.width?.message}
                                    >
                                        <Controller
                                            name="width"
                                            control={control}
                                            render={({ field }) => (
                                                <Input
                                                    {...field}
                                                    type="number"
                                                    min="0.1"
                                                    step="0.1"
                                                    placeholder="0.0"
                                                    onChange={(e) =>
                                                        field.onChange(
                                                            parseFloat(
                                                                e.target.value,
                                                            ) || 0,
                                                        )
                                                    }
                                                />
                                            )}
                                        />
                                    </FormItem>
                                    <FormItem
                                        label={`${t('nav.warehouses.shelfHeight')} *`}
                                        invalid={!!errors.height}
                                        errorMessage={errors.height?.message}
                                    >
                                        <Controller
                                            name="height"
                                            control={control}
                                            render={({ field }) => (
                                                <Input
                                                    {...field}
                                                    type="number"
                                                    min="0.1"
                                                    step="0.1"
                                                    placeholder="0.0"
                                                    onChange={(e) =>
                                                        field.onChange(
                                                            parseFloat(
                                                                e.target.value,
                                                            ) || 0,
                                                        )
                                                    }
                                                />
                                            )}
                                        />
                                    </FormItem>
                                </div>
                            </div>
                        </div>
                    </Form>
                </div>

                {/* Action Buttons - Fixed at bottom */}
                <div className="flex justify-end gap-3 p-6 pt-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
                    <Button
                        type="button"
                        variant="plain"
                        disabled={loading}
                        className="min-w-[100px]"
                        onClick={handleClose}
                    >
                        {t('nav.shared.cancel')}
                    </Button>
                    <Button
                        type="submit"
                        variant="solid"
                        icon={<TbCheck />}
                        loading={loading}
                        className="min-w-[100px] bg-green-600 hover:bg-green-700"
                        onClick={handleSubmit(handleFormSubmit)}
                    >
                        {t('nav.shared.create')}
                    </Button>
                </div>
            </div>
        </Dialog>
    )
}

export default AddShelfModal
