import React from 'react'
import { useTranslation } from 'react-i18next'
import { DocumentDetails } from '@/@types/document'
import { Card } from '@/components/ui'
import { TbCalendar, TbFile, TbFolder, TbTag, TbUser } from 'react-icons/tb'

interface DocumentMetadataProps {
    document: DocumentDetails | null
    loading?: boolean
}

export default function DocumentMetadata({
    document,
    loading = false,
}: DocumentMetadataProps) {
    const { t } = useTranslation()

    if (loading) {
        return (
            <Card className="p-6">
                <div className="animate-pulse">
                    <div className="h-6 bg-gray-200 rounded mb-4 w-1/3"></div>
                    <div className="space-y-3">
                        {[...Array(6)].map((_, i) => (
                            <div
                                key={i}
                                className="flex items-center space-x-3"
                            >
                                <div className="w-5 h-5 bg-gray-200 rounded"></div>
                                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                            </div>
                        ))}
                    </div>
                </div>
            </Card>
        )
    }

    if (!document) {
        return (
            <Card className="p-6">
                <div className="text-center text-gray-500">
                    <TbFile className="mx-auto text-4xl mb-2" />
                    <p>{t('documents.noDocumentSelected')}</p>
                </div>
            </Card>
        )
    }

    const formatDate = (dateString: string) => {
        if (!dateString) return '-'
        return new Date(dateString).toLocaleDateString()
    }

    const getStatusBadge = (status: number) => {
        const statusMap = {
            0: { label: 'Draft', color: 'bg-yellow-100 text-yellow-800' },
            1: { label: 'Active', color: 'bg-green-100 text-green-800' },
        }
        const statusInfo =
            statusMap[status as keyof typeof statusMap] || statusMap[0]
        return (
            <span
                className={`px-2 py-1 rounded-full text-xs font-medium ${statusInfo.color}`}
            >
                {statusInfo.label}
            </span>
        )
    }

    const getConfidentialityBadge = (level: number) => {
        const confidentialityMap = {
            0: { label: 'Public', color: 'bg-blue-100 text-blue-800' },
            1: { label: 'Internal', color: 'bg-orange-100 text-orange-800' },
            2: { label: 'Confidential', color: 'bg-red-100 text-red-800' },
        }
        const confInfo =
            confidentialityMap[level as keyof typeof confidentialityMap] ||
            confidentialityMap[0]
        return (
            <span
                className={`px-2 py-1 rounded-full text-xs font-medium ${confInfo.color}`}
            >
                {confInfo.label}
            </span>
        )
    }

    return (
        <Card className="p-6">
            <div className="mb-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-2">
                    Document Data
                </h2>
                <div className="flex items-center gap-2">
                    {getStatusBadge(document.status)}
                    {getConfidentialityBadge(document.fileConfidentiality)}
                </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                    <div className="flex items-start space-x-3">
                        <TbFile className="text-gray-400 mt-1 flex-shrink-0" />
                        <div>
                            <p className="text-sm font-medium text-gray-500">
                                Title
                            </p>
                            <p className="text-gray-900">
                                {document.title || '-'}
                            </p>
                        </div>
                    </div>

                    <div className="flex items-start space-x-3">
                        <TbFolder className="text-gray-400 mt-1 flex-shrink-0" />
                        <div>
                            <p className="text-sm font-medium text-gray-500">
                                Category
                            </p>
                            <p className="text-gray-900">
                                {document.generalCategoryName || '-'}
                            </p>
                            <p className="text-sm text-gray-500">
                                {document.fileCategoryName || '-'}
                            </p>
                        </div>
                    </div>

                    <div className="flex items-start space-x-3">
                        <TbTag className="text-gray-400 mt-1 flex-shrink-0" />
                        <div>
                            <p className="text-sm font-medium text-gray-500">
                                Document Type
                            </p>
                            <p className="text-gray-900">
                                {document.documentTypeName || '-'}
                            </p>
                        </div>
                    </div>
                </div>

                <div className="space-y-4">
                    <div className="flex items-start space-x-3">
                        <TbCalendar className="text-gray-400 mt-1 flex-shrink-0" />
                        <div>
                            <p className="text-sm font-medium text-gray-500">
                                Date
                            </p>
                            <p className="text-gray-900">
                                {formatDate(document.date)}
                            </p>
                        </div>
                    </div>

                    <div className="flex items-start space-x-3">
                        <TbCalendar className="text-gray-400 mt-1 flex-shrink-0" />
                        <div>
                            <p className="text-sm font-medium text-gray-500">
                                Follow Up Date
                            </p>
                            <p className="text-gray-900">
                                {formatDate(document.followUpDate)}
                            </p>
                        </div>
                    </div>

                    <div className="flex items-start space-x-3">
                        <TbUser className="text-gray-400 mt-1 flex-shrink-0" />
                        <div>
                            <p className="text-sm font-medium text-gray-500">
                                Organization
                            </p>
                            <p className="text-gray-900">
                                {document.organizationalNodeName || '-'}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            {document.notes && (
                <div className="mt-6 pt-6 border-t border-gray-200">
                    <p className="text-sm font-medium text-gray-500 mb-2">
                        Notes
                    </p>
                    <p className="text-gray-900 whitespace-pre-wrap">
                        {document.notes}
                    </p>
                </div>
            )}

            {document.keywords && document.keywords.length > 0 && (
                <div className="mt-6 pt-6 border-t border-gray-200">
                    <p className="text-sm font-medium text-gray-500 mb-3">
                        Keywords
                    </p>
                    <div className="flex flex-wrap gap-2">
                        {document.keywords.map((keyword) => (
                            <span
                                key={keyword.id}
                                className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm"
                            >
                                {keyword.value}
                            </span>
                        ))}
                    </div>
                </div>
            )}

            <div className="mt-6 pt-6 border-t border-gray-200 grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div>
                    <p className="text-2xl font-bold text-blue-600">
                        {document.pagesCount}
                    </p>
                    <p className="text-sm text-gray-500">Pages</p>
                </div>
                <div>
                    <p className="text-2xl font-bold text-green-600">
                        {document.attachments?.length || 0}
                    </p>
                    <p className="text-sm text-gray-500">Attachments</p>
                </div>
                <div>
                    <p className="text-lg font-semibold text-purple-600">
                        {document.boxCode}
                    </p>
                    <p className="text-sm text-gray-500">Box Code</p>
                </div>
                <div>
                    <p className="text-lg font-semibold text-orange-600">
                        {document.fileId}
                    </p>
                    <p className="text-sm text-gray-500">File ID</p>
                </div>
            </div>
        </Card>
    )
}
