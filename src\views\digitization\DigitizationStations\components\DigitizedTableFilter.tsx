import { useState } from 'react'
import Button from '@/components/ui/Button'
import Drawer from '@/components/ui/Drawer'
import useTranslation from '@/utils/hooks/useTranslation'
import { TbFilter } from 'react-icons/tb'

const DigitizedTableFilter = () => {
    const { t } = useTranslation()
    const [filterIsOpen, setFilterIsOpen] = useState(false)

    return (
        <>
            <Button
                icon={<TbFilter />}
                variant="default"
                size="sm"
                className="shadow-sm hover:shadow-md transition-all duration-200 border-gray-300 hover:border-gray-400"
                onClick={() => setFilterIsOpen(true)}
            >
                {t('nav.shared.customizeColumns')}
            </Button>
            <Drawer
                title={t('nav.shared.customizeColumns')}
                isOpen={filterIsOpen}
                footer={
                    <div className="flex justify-around items-center w-full">
                        <Button variant="solid" onClick={() => ''}>
                            {t('nav.shared.confirm')}
                        </Button>
                    </div>
                }
                onClose={() => setFilterIsOpen(false)}
                onRequestClose={() => setFilterIsOpen(false)}
            ></Drawer>
        </>
    )
}

export default DigitizedTableFilter
