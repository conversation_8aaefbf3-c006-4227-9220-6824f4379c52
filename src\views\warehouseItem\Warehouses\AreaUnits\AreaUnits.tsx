/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import Container from '@/components/shared/Container'
import AdaptiveCard from '@/components/shared/AdaptiveCard'
import Button from '@/components/ui/Button'
import useTranslation from '@/utils/hooks/useTranslation'
import {
    TbArrowLeft,
    TbPlus,
    TbBox,
    TbStack3,
    TbChevronRight,
    TbHome,
} from 'react-icons/tb'
import UnitCard from './components/UnitCard'
import AddUnitModal from '../WarehouseUnits/components/AddUnitModal'
import EditUnitModal from '../WarehouseUnits/components/EditUnitModal'
import { addUnitsToArea } from '@/services/Warehouses'
import {
    useDeleteWarehouseUnit,
    useGetWarehouseArea,
    useUpdateWarehouseUnit,
} from '@/hooks/warehouses'
import { Unit } from '@/@types/warehouse'

const AreaUnits = () => {
    const { t } = useTranslation()
    const navigate = useNavigate()
    const { warehouseId, areaId } = useParams<{
        warehouseId: string
        areaId: string
    }>()

    const { data: area, isLoading } = useGetWarehouseArea(warehouseId!, areaId!)

    const { mutate: updateWarehouseUnit } = useUpdateWarehouseUnit()
    const { mutate: deleteWarehouseUnit } = useDeleteWarehouseUnit()

    const [addModalOpen, setAddModalOpen] = useState(false)
    const [editModalOpen, setEditModalOpen] = useState(false)
    const [selectedUnit, setSelectedUnit] = useState<Unit | null>(null)

    const handleBack = () => {
        navigate(`/warehouses/${warehouseId}/areas`)
    }

    const handleAddUnit = async (data: any) => {
        if (warehouseId && areaId) {
            try {
                await addUnitsToArea(warehouseId, areaId, data)
                setAddModalOpen(false)
            } catch (error) {
                console.error('Error adding unit:', error)
            }
        }
    }

    const handleEditUnit = (unitId: string) => {
        const unit = area?.units?.find((u) => u.id === unitId)
        if (unit) {
            setSelectedUnit(unit)
            setEditModalOpen(true)
        }
    }

    const handleUpdateUnit = async (data: any) => {
        if (warehouseId && areaId && selectedUnit) {
            try {
                await updateWarehouseUnit({
                    warehouseId,
                    areaId,
                    unitId: selectedUnit.id,
                    ...data,
                })
                setEditModalOpen(false)
                setSelectedUnit(null)
            } catch (error) {
                console.error('Error updating unit:', error)
            }
        }
    }

    const handleDeleteUnit = async (unitId: string) => {
        if (warehouseId && areaId) {
            try {
                await deleteWarehouseUnit({ warehouseId, areaId, unitId })
            } catch (error) {
                console.error('Error deleting unit:', error)
            }
        }
    }

    const handleManageShelves = (unitId: string) => {
        navigate(
            `/warehouses/${warehouseId}/areas/${areaId}/units/${unitId}/shelves`,
        )
    }

    // Calculate totals from area data
    const units = area?.units || []
    const totalUnits = area?.numberOfUnits || units.length
    const totalShelves =
        area?.numberOfShelves ||
        units.reduce((sum, unit) => sum + unit.numberOfShelves, 0)
    const totalBoxes = area?.numberOfBoxes || 0
    const availableBoxes = area?.availableBoxes || 0

    if (isLoading) {
        return (
            <Container>
                <div className="flex items-center justify-center h-96">
                    <div className="text-center">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
                        <p className="text-gray-600 dark:text-gray-400">
                            {t('nav.shared.loading')}
                        </p>
                    </div>
                </div>
            </Container>
        )
    }

    return (
        <Container>
            {/* Beautiful Breadcrumb Navigation */}
            <div className="mb-8">
                {/* Breadcrumb */}
                <div className="flex items-center gap-2 mb-6">
                    <button
                        type="button"
                        className="flex items-center gap-2 text-sm font-medium text-gray-600 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400 transition-colors duration-200 group"
                        onClick={() => navigate('/warehouses/warehouses')}
                    >
                        <TbArrowLeft className="w-4 h-4 transition-transform duration-200 group-hover:-translate-x-1" />
                        <span className="hover:underline">
                            {t('nav.warehouses.warehouses')}
                        </span>
                    </button>
                    <TbChevronRight className="w-4 h-4 text-gray-400" />
                    <button
                        type="button"
                        className="text-sm font-medium text-gray-600 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400 transition-colors duration-200"
                        onClick={handleBack}
                    >
                        <span className="hover:underline">{warehouseId}</span>
                    </button>
                    <TbChevronRight className="w-4 h-4 text-gray-400" />
                    <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        {areaId}
                    </span>
                    <TbChevronRight className="w-4 h-4 text-gray-400" />
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {t('nav.warehouses.units')}
                    </span>
                </div>

                {/* Enhanced Header with Stats */}
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-6 mb-6 border border-blue-200 dark:border-blue-800">
                    <div className="flex items-center justify-between mb-4">
                        <div>
                            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                                {areaId}
                            </h1>
                            <p className="text-gray-600 dark:text-gray-400">
                                {t('nav.warehouses.manageUnitsInArea')}
                            </p>
                        </div>
                        <Button
                            variant="solid"
                            size="sm"
                            icon={<TbPlus />}
                            onClick={() => setAddModalOpen(true)}
                            className="bg-blue-600 hover:bg-blue-700 text-white"
                        >
                            {t('nav.warehouses.addUnit')}
                        </Button>
                    </div>

                    {/* Stats Grid */}
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <div className="flex items-center gap-3">
                                <div className="flex items-center justify-center w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                                    <TbBox className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                                </div>
                                <div>
                                    <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                                        {totalUnits}
                                    </div>
                                    <div className="text-sm text-gray-600 dark:text-gray-400">
                                        {t('nav.warehouses.totalUnits')}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <div className="flex items-center gap-3">
                                <div className="flex items-center justify-center w-10 h-10 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                                    <TbStack3 className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                                </div>
                                <div>
                                    <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                                        {totalShelves}
                                    </div>
                                    <div className="text-sm text-gray-600 dark:text-gray-400">
                                        {t('nav.warehouses.totalShelves')}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <div className="flex items-center gap-3">
                                <div className="flex items-center justify-center w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg">
                                    <TbBox className="w-5 h-5 text-green-600 dark:text-green-400" />
                                </div>
                                <div>
                                    <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                                        {totalBoxes}
                                    </div>
                                    <div className="text-sm text-gray-600 dark:text-gray-400">
                                        {t('nav.warehouses.totalBoxes')}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <div className="flex items-center gap-3">
                                <div className="flex items-center justify-center w-10 h-10 bg-orange-100 dark:bg-orange-900/30 rounded-lg">
                                    <TbBox className="w-5 h-5 text-orange-600 dark:text-orange-400" />
                                </div>
                                <div>
                                    <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                                        {availableBoxes}
                                    </div>
                                    <div className="text-sm text-gray-600 dark:text-gray-400">
                                        {t('nav.warehouses.availableBoxes')}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Units Grid */}
            {units.length === 0 ? (
                <AdaptiveCard className="text-center py-12">
                    <div className="flex flex-col items-center gap-4">
                        <div className="flex items-center justify-center w-20 h-20 bg-gray-100 dark:bg-gray-700 rounded-full">
                            <TbHome className="w-10 h-10 text-gray-400" />
                        </div>
                        <div>
                            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                                {t('nav.warehouses.noUnits')}
                            </h3>
                            <p className="text-gray-600 dark:text-gray-400 mb-4">
                                {t('nav.warehouses.noUnitsDesc')}
                            </p>
                            <Button
                                variant="solid"
                                icon={<TbPlus />}
                                className="bg-purple-600 hover:bg-purple-700"
                                onClick={() => setAddModalOpen(true)}
                            >
                                {t('nav.warehouses.addFirstUnit')}
                            </Button>
                        </div>
                    </div>
                </AdaptiveCard>
            ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {units.map((unit) => (
                        <UnitCard
                            key={unit.id}
                            unit={unit}
                            warehouseId={warehouseId!}
                            areaId={areaId!}
                            onEdit={handleEditUnit}
                            onDelete={handleDeleteUnit}
                            onManageShelves={handleManageShelves}
                        />
                    ))}
                </div>
            )}

            {/* Add Unit Modal */}
            <AddUnitModal
                isOpen={addModalOpen}
                onClose={() => setAddModalOpen(false)}
                onSubmit={handleAddUnit}
            />

            {/* Edit Unit Modal */}
            <EditUnitModal
                isOpen={editModalOpen}
                onClose={() => {
                    setEditModalOpen(false)
                    setSelectedUnit(null)
                }}
                onSubmit={handleUpdateUnit}
                initialData={
                    selectedUnit
                        ? {
                              length: selectedUnit.height,
                              width: selectedUnit.width,
                          }
                        : null
                }
            />
        </Container>
    )
}

export default AreaUnits
