import { use<PERSON><PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import Select from '@/components/ui/Select'
import { FormItem, Form } from '@/components/ui/Form'
import useTranslation from '@/utils/hooks/useTranslation'
import type { FileForm } from '@/@types/file'
import { Confidentiality, MediumType } from '@/@types/file'
import { TbCheck, TbX } from 'react-icons/tb'
import { useGetMyDigitizationStations } from '@/hooks/digitization-station'
import { useGetLeafCategories } from '@/hooks/categories'
import {
    fileCreationValidationSchema,
    defaultFileFormValues,
    type PartialFileForm,
} from '../validation'

interface DailyFormProps {
    onSubmit: (data: FileForm) => Promise<void>
    onCancel: () => void
    isSubmitting: boolean
    isLoading?: boolean
}

const DailyForm = ({
    onSubmit,
    onCancel,
    isSubmitting,
    isLoading = false,
}: DailyFormProps) => {
    const { t } = useTranslation()

    const { data: leafCategories } = useGetLeafCategories()
    const { data: myStation } = useGetMyDigitizationStations()

    // User options from myStation users
    const userOptions =
        myStation?.[0]?.users?.map((user) => ({
            value: user.userId,
            label: user.fullName,
        })) || []

    // Set all users as selected by default
    const defaultValues = {
        ...defaultFileFormValues,
        userIdsWithAccess: userOptions.map((u) => u.value),
    }

    const {
        handleSubmit,
        control,
        formState: { errors },
    } = useForm<PartialFileForm>({
        defaultValues,
        resolver: zodResolver(fileCreationValidationSchema),
    })

    const confidentialityOptions = [
        {
            value: Confidentiality.NonConfidential,
            label: t('nav.File.confidentiality.nonConfidential'),
        },
        {
            value: Confidentiality.Confidential,
            label: t('nav.File.confidentiality.confidential'),
        },
        {
            value: Confidentiality.HighlyConfidential,
            label: t('nav.File.confidentiality.highlyConfidential'),
        },
    ]

    const mediumTypeOptions = [
        {
            value: MediumType.PaperBased,
            label: t('nav.File.mediumType.paperBased'),
        },
        {
            value: MediumType.ElectronicWithoutPhysicalCopy,
            label: t('nav.File.mediumType.electronicWithoutPhysical'),
        },
        {
            value: MediumType.ElectronicWithPhysicalCopy,
            label: t('nav.File.mediumType.electronicWithPhysical'),
        },
    ]

    // Category options from leafCategories
    const categoryOptions = leafCategories?.map((category) => ({
        value: category.id,
        label: category.name,
    }))

    const onFormSubmit = async (data: PartialFileForm) => {
        try {
            // Get stationId and orgCode from localStorage
            const stationId = localStorage.getItem('digitizationStationId')
            const orgCode = localStorage.getItem('organizationCode')

            // Combine form data with localStorage data
            const completeFormData: FileForm = {
                ...data,
                digitizationStationId: stationId ? parseInt(stationId) : 1,
                organizationalNodeId: orgCode || '',
                // userIdsWithAccess comes from form data, not localStorage
            }

            await onSubmit(completeFormData)
        } catch (error) {
            console.error('Form submission error:', error)
        }
    }

    if (isLoading) {
        return (
            <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
        )
    }

    return (
        <Form>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormItem
                    label={t('nav.File.fileTitle')}
                    invalid={!!errors.fileTitle}
                    errorMessage={errors.fileTitle?.message}
                >
                    <Controller
                        name="fileTitle"
                        control={control}
                        render={({ field }) => (
                            <Input
                                {...field}
                                placeholder={t('nav.File.fileTitle')}
                                disabled={isSubmitting}
                            />
                        )}
                    />
                </FormItem>

                <FormItem
                    label={t('nav.File.Confidentiality')}
                    invalid={!!errors.confidentiality}
                    errorMessage={errors.confidentiality?.message}
                >
                    <Controller
                        name="confidentiality"
                        control={control}
                        render={({ field }) => (
                            <Select
                                value={confidentialityOptions.find(
                                    (option) => option.value === field.value,
                                )}
                                options={confidentialityOptions}
                                placeholder={'...'}
                                isDisabled={isSubmitting}
                                menuPlacement="auto"
                                onChange={(option) =>
                                    field.onChange(option?.value)
                                }
                            />
                        )}
                    />
                </FormItem>

                <FormItem
                    label={t('nav.File.category')}
                    invalid={!!errors.categoryId}
                    errorMessage={errors.categoryId?.message}
                >
                    <Controller
                        name="categoryId"
                        control={control}
                        render={({ field }) => (
                            <Select
                                value={categoryOptions?.find(
                                    (option) => option.value === field.value,
                                )}
                                options={categoryOptions}
                                placeholder={'...'}
                                isDisabled={isSubmitting}
                                menuPlacement="auto"
                                onChange={(option) =>
                                    field.onChange(option?.value)
                                }
                            />
                        )}
                    />
                </FormItem>

                <FormItem
                    label={t('nav.File.MediumType')}
                    invalid={!!errors.mediumType}
                    errorMessage={errors.mediumType?.message}
                >
                    <Controller
                        name="mediumType"
                        control={control}
                        render={({ field }) => (
                            <Select
                                value={mediumTypeOptions.find(
                                    (option) => option.value === field.value,
                                )}
                                options={mediumTypeOptions}
                                placeholder={'...'}
                                isDisabled={isSubmitting}
                                menuPlacement="auto"
                                onChange={(option) =>
                                    field.onChange(option?.value)
                                }
                            />
                        )}
                    />
                </FormItem>
            </div>

            <FormItem
                label={t('nav.File.usersWithAccess')}
                invalid={!!errors.userIdsWithAccess}
                errorMessage={errors.userIdsWithAccess?.message}
            >
                <Controller
                    name="userIdsWithAccess"
                    control={control}
                    render={({ field }) => (
                        <Select
                            isMulti
                            value={userOptions.filter(
                                (option: { value: string }) =>
                                    field.value?.includes(option.value),
                            )}
                            options={userOptions}
                            placeholder={t('nav.shared.selectUsers')}
                            isDisabled={isSubmitting}
                            menuPlacement="auto"
                            onChange={(options) =>
                                field.onChange(
                                    Array.isArray(options)
                                        ? options.map((opt) => opt.value)
                                        : [],
                                )
                            }
                        />
                    )}
                />
            </FormItem>

            <div className="flex items-center justify-end gap-3 pt-4 border-t border-gray-200 sticky bottom-0 bg-white">
                <Button
                    type="button"
                    variant="plain"
                    disabled={isSubmitting}
                    className="flex items-center gap-2"
                    onClick={onCancel}
                >
                    <TbX className="text-lg" />
                    {t('nav.shared.cancel')}
                </Button>
                <Button
                    type="submit"
                    variant="solid"
                    loading={isSubmitting}
                    className="flex items-center gap-2"
                    onClick={handleSubmit(onFormSubmit)}
                >
                    <TbCheck className="text-lg" />
                    {t('nav.shared.add')}
                </Button>
            </div>
        </Form>
    )
}

export default DailyForm
