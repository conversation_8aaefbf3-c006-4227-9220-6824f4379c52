import { useState } from 'react'
import Button from '@/components/ui/Button'
import Dropdown from '@/components/ui/Dropdown'
import ConfirmDialog from '@/components/shared/ConfirmDialog'
import useTranslation from '@/utils/hooks/useTranslation'
import type { DailySelectionFooterProps } from '../types'
import { TbTrash, TbCheck, TbX, TbChevronUp } from 'react-icons/tb'

const DailySelectionFooter = ({
    selectedCount,
    onDeleteSelected,
    onClearSelection,
    onStatusUpdate,
}: DailySelectionFooterProps) => {
    const { t } = useTranslation()
    const [deleteConfirmation, setDeleteConfirmation] = useState(false)

    const handleDeleteClick = () => {
        setDeleteConfirmation(true)
    }

    const handleConfirmDelete = () => {
        onDeleteSelected()
        setDeleteConfirmation(false)
    }

    const handleCancelDelete = () => {
        setDeleteConfirmation(false)
    }

    const statusUpdateItems = [
        {
            key: 'activate',
            name: t('nav.shared.activate'),
            icon: <TbCheck className="text-green-600" />,
            onClick: () => onStatusUpdate(1),
        },
        {
            key: 'deactivate',
            name: t('nav.shared.deactivate'),
            icon: <TbX className="text-red-600" />,
            onClick: () => onStatusUpdate(3),
        },
        {
            key: 'pending',
            name: t('nav.shared.setPending'),
            icon: <TbChevronUp className="text-amber-600" />,
            onClick: () => onStatusUpdate(2),
        },
    ]

    if (selectedCount === 0) {
        return null
    }

    return (
        <>
            <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-50">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex items-center justify-between py-4">
                        {/* Left side - Selection info */}
                        <div className="flex items-center gap-4">
                            <div className="text-sm text-gray-600">
                                {selectedCount}{' '}
                                {selectedCount === 1
                                    ? t('nav.File.file')
                                    : t('nav.File.files')}{' '}
                                {t('nav.shared.selected')}
                            </div>
                            <Button
                                variant="plain"
                                size="sm"
                                onClick={onClearSelection}
                                className="text-sm"
                            >
                                {t('nav.shared.clearSelection')}
                            </Button>
                        </div>

                        {/* Right side - Actions */}
                        <div className="flex items-center gap-3">
                            <Dropdown
                                placement="top-end"
                                renderTitle={
                                    <Button
                                        variant="solid"
                                        size="sm"
                                        className="flex items-center gap-2"
                                    >
                                        {t('nav.shared.updateStatus')}
                                        <TbChevronUp />
                                    </Button>
                                }
                            >
                                {statusUpdateItems.map((item) => (
                                    <Dropdown.Item
                                        key={item.key}
                                        eventKey={item.key}
                                        onClick={item.onClick}
                                    >
                                        <div className="flex items-center gap-2">
                                            {item.icon}
                                            <span>{item.name}</span>
                                        </div>
                                    </Dropdown.Item>
                                ))}
                            </Dropdown>

                            <Button
                                variant="solid"
                                color="red-600"
                                size="sm"
                                icon={<TbTrash />}
                                onClick={handleDeleteClick}
                                className="flex items-center gap-2"
                            >
                                {t('nav.shared.delete')}
                            </Button>
                        </div>
                    </div>
                </div>
            </div>

            {/* Delete Confirmation Dialog */}
            <ConfirmDialog
                isOpen={deleteConfirmation}
                type="danger"
                title={t('nav.shared.deleteConfirmation')}
                confirmButtonColor="red-600"
                confirmText={t('nav.shared.delete')}
                cancelText={t('nav.shared.cancel')}
                onClose={handleCancelDelete}
                onRequestClose={handleCancelDelete}
                onCancel={handleCancelDelete}
                onConfirm={handleConfirmDelete}
            >
                <p>
                    {t('nav.shared.deleteSelectedConfirmationMessage', {
                        count: selectedCount,
                        item: selectedCount === 1 ? t('nav.File.file') : t('nav.File.files'),
                    })}
                </p>
            </ConfirmDialog>
        </>
    )
}

export default DailySelectionFooter
