import { useState } from 'react'
import StickyFooter from '@/components/shared/StickyFooter'
import Button from '@/components/ui/Button'
import ConfirmDialog from '@/components/shared/ConfirmDialog'
import { TbChecks } from 'react-icons/tb'
import useTranslation from '@/utils/hooks/useTranslation'

const ConsumableListSelected = () => {
    const { t } = useTranslation()

    const [deleteConfirmationOpen, setDeleteConfirmationOpen] = useState(false)
    const [isDeleting, setIsDeleting] = useState(false)

    const handleDelete = () => {
        setDeleteConfirmationOpen(true)
    }

    const handleCancel = () => {
        setDeleteConfirmationOpen(false)
    }

    const handleConfirmDelete = async () => {
        if (displayData.length === 0) return

        setIsDeleting(true)
        try {
            // Delete all selected consumables
            const deletePromises = displayData.map((item) =>
                deleteConsumable(item.id),
            )
            await Promise.all(deletePromises)

            // Clear selection and refresh data
            clearSelection()
            getConsumables()
            setDeleteConfirmationOpen(false)
        } catch (error) {
            console.error('Error deleting consumables:', error)
        } finally {
            setIsDeleting(false)
        }
    }

    return (
        <>
            {displayData.length > 0 && (
                <StickyFooter
                    className="flex items-center justify-between py-4 bg-white dark:bg-gray-800"
                    stickyClass="-mx-4 sm:-mx-8 border-t border-gray-200 dark:border-gray-700 px-8"
                    defaultClass="container mx-auto px-8 rounded-xl border border-gray-200 dark:border-gray-600 mt-4"
                >
                    <div className="container mx-auto">
                        <div className="flex items-center justify-between">
                            <span>
                                {displayData.length > 0 && (
                                    <span className="flex items-center gap-2">
                                        <span className="text-lg text-primary">
                                            <TbChecks />
                                        </span>
                                        <span className="font-semibold flex items-center gap-1">
                                            <span className="heading-text">
                                                {displayData.length}{' '}
                                                {t(
                                                    'nav.consumables.consumables',
                                                )}
                                            </span>
                                            <span>
                                                {t('nav.shared.selected')}
                                            </span>
                                        </span>
                                    </span>
                                )}
                            </span>

                            <div className="flex items-center gap-2">
                                <Button
                                    size="sm"
                                    variant="default"
                                    onClick={clearSelection}
                                >
                                    Clear Selection
                                </Button>
                                <Button
                                    size="sm"
                                    className="ltr:mr-3 rtl:ml-3"
                                    type="button"
                                    customColorClass={() =>
                                        'border-error ring-1 ring-error text-error hover:border-error hover:ring-error hover:text-error'
                                    }
                                    loading={isDeleting}
                                    onClick={handleDelete}
                                >
                                    {t('nav.shared.delete')}
                                </Button>
                            </div>
                        </div>
                    </div>
                </StickyFooter>
            )}

            <ConfirmDialog
                isOpen={deleteConfirmationOpen}
                type="danger"
                title={t('nav.consumables.removeConsumables')}
                onClose={handleCancel}
                onRequestClose={handleCancel}
                onCancel={handleCancel}
                onConfirm={handleConfirmDelete}
            >
                <p>{t('nav.consumables.confirmRemoveConsumables')}</p>
            </ConfirmDialog>
        </>
    )
}

export default ConsumableListSelected
