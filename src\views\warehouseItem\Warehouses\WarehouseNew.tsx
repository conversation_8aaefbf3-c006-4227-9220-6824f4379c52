/* eslint-disable @typescript-eslint/no-explicit-any */

import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import Container from '@/components/shared/Container'
import AdaptiveCard from '@/components/shared/AdaptiveCard'
import Button from '@/components/ui/Button'
import Notification from '@/components/ui/Notification'
import toast from '@/components/ui/toast'
import WarehouseForm from './components/WarehouseForm'
import useTranslation from '@/utils/hooks/useTranslation'
import { TbArrowLeft, TbPlus } from 'react-icons/tb'
import type { WarehouseForm as WarehouseFormType } from '@/@types/warehouse'
import { useCreateWarehouse } from '@/hooks/warehouses'

const WarehouseNew = () => {
    const { t } = useTranslation()
    const navigate = useNavigate()

    const { mutate: createWarehouse } = useCreateWarehouse()

    const [loading, setLoading] = useState(false)

    const handleBack = () => {
        navigate('/warehouses/warehouses')
    }

    const handleSubmit = async (data: WarehouseFormType) => {
        setLoading(true)
        try {
            await createWarehouse(data)
            toast.push(
                <Notification type="success">
                    {t('nav.warehouses.createSuccess')}
                </Notification>,
                { placement: 'top-center' },
            )
            navigate('/warehouses/warehouses')
        } catch (error: any) {
            const errorMessage = error?.errors
            toast.push(
                <Notification type="danger">
                    {errorMessage
                        ? errorMessage[0]?.description
                        : error?.status}
                </Notification>,
                { placement: 'top-center' },
            )
        } finally {
            setLoading(false)
        }
    }

    const handleCancel = () => {
        navigate('/warehouses/warehouses')
    }

    return (
        <Container>
            <div className="mb-6">
                <div className="flex items-center gap-4 mb-4">
                    <Button
                        variant="plain"
                        size="sm"
                        icon={<TbArrowLeft />}
                        className="text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200"
                        onClick={handleBack}
                    >
                        {t('nav.shared.back')}
                    </Button>
                </div>
            </div>

            <AdaptiveCard className="shadow-lg">
                <div className="p-6">
                    <div className="flex items-center gap-3 mb-6 pb-4 border-b border-gray-200 dark:border-gray-700">
                        <div className="flex items-center justify-center w-14 h-14 bg-blue-100 dark:bg-blue-900/30 rounded-xl">
                            <TbPlus className="w-7 h-7 text-blue-600 dark:text-blue-400" />
                        </div>
                        <div>
                            <h3 className=" text-gray-900 dark:text-gray-100">
                                {t('nav.warehouses.createWarehouse')}
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                                {t('nav.warehouses.createWarehouseDesc')}
                            </p>
                        </div>
                    </div>

                    <WarehouseForm
                        mode="create"
                        loading={loading}
                        onSubmit={handleSubmit}
                        onCancel={handleCancel}
                    />
                </div>
            </AdaptiveCard>
        </Container>
    )
}

export default WarehouseNew
