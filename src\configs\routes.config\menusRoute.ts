import { lazy } from 'react'
import { MENUS_PREFIX_PATH } from '@/constants/route.constant'
import { ADMIN } from '@/constants/roles.constant'
import type { Routes } from '@/@types/routes'

const menusRoute: Routes = [
    {
        key: 'menus.unit-structure',
        path: `${MENUS_PREFIX_PATH}/unit-structure`,
        component: lazy(() => import('@/views/menus/UnitStructure')),
        authority: [ADMIN],
        meta: {
            pageContainerType: 'default',
        },
    },
    {
        key: 'menus.consumables',
        path: `${MENUS_PREFIX_PATH}/consumables`,
        component: lazy(() => import('@/views/menus/Consumables')),
        authority: [ADMIN],
        meta: {
            pageContainerType: 'default',
        },
    },
    {
        key: 'menus.stocks',
        path: `${MENUS_PREFIX_PATH}/stocks`,
        component: lazy(() => import('@/views/menus/Stocks')),
        authority: [ADMIN],
        meta: {
            pageContainerType: 'default',
        },
    },
    {
        key: 'menus.buildings',
        path: `${MENUS_PREFIX_PATH}/buildings`,
        component: lazy(() => import('@/views/menus/Buildings')),
        authority: [ADMIN],
        meta: {
            pageContainerType: 'default',
        },
    },
    {
        key: 'menus.document-types',
        path: `${MENUS_PREFIX_PATH}/document-types`,
        component: lazy(() => import('@/views/menus/DocumentTypes')),
        authority: [ADMIN],
        meta: {
            pageContainerType: 'default',
        },
    },
    {
        key: 'menus.categories',
        path: `${MENUS_PREFIX_PATH}/categories`,
        component: lazy(() => import('@/views/menus/Categories')),
        authority: [ADMIN],
        meta: {
            pageContainerType: 'default',
        },
    },
    {
        key: 'menus.keywords',
        path: `${MENUS_PREFIX_PATH}/keywords`,
        component: lazy(() => import('@/views/menus/Keywords')),
        authority: [ADMIN],
        meta: {
            pageContainerType: 'default',
        },
    },
    {
        key: 'menus.general-categories',
        path: `${MENUS_PREFIX_PATH}/general-categories`,
        component: lazy(() => import('@/views/menus/GeneralCategories')),
        authority: [ADMIN],
        meta: {
            pageContainerType: 'default',
        },
    },
    {
        key: 'menus.entities',
        path: `${MENUS_PREFIX_PATH}/entities`,
        component: lazy(() => import('@/views/menus/Keywords')),
        authority: [ADMIN],
        meta: {
            pageContainerType: 'default',
        },
    },
    {
        key: 'menus.sender-recipient',
        path: `${MENUS_PREFIX_PATH}/sender-recipient`,
        component: lazy(() => import('@/views/menus/Keywords')),
        authority: [ADMIN],
        meta: {
            pageContainerType: 'default',
        },
    },
]

export default menusRoute
