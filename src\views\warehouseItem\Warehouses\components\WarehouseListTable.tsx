/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMemo, useState, useCallback } from 'react'
import { useNavigate } from 'react-router-dom'
import Tooltip from '@/components/ui/Tooltip'
import DataTable from '@/components/shared/DataTable'
import ConfirmDialog from '@/components/shared/ConfirmDialog'
import { TbPencil, TbTrash } from 'react-icons/tb'
import type { ColumnDef } from '@/components/shared/DataTable'
import { Notification, Tag, toast } from '@/components/ui'
import useTranslation from '@/utils/hooks/useTranslation'
import type {} from // WarehouseSortConfig,
'@/store/selectors/warehouseSelector'
import { Warehouse } from '@/@types/warehouse'
import { useDeleteWarehouse, useGetWarehouses } from '@/hooks/warehouses'

const ActionColumn = ({
    onEdit,
    onDelete,
}: {
    onEdit: () => void
    onDelete: () => void
}) => {
    const { t } = useTranslation()
    return (
        <div className="flex items-center justify-center gap-3">
            <Tooltip title={t('nav.shared.edit')}>
                <div
                    className={`text-xl cursor-pointer select-none font-semibold text-amber-600 hover:text-amber-700`}
                    role="button"
                    onClick={onEdit}
                >
                    <TbPencil />
                </div>
            </Tooltip>
            <Tooltip title={t('nav.shared.delete')}>
                <div
                    className={`text-xl cursor-pointer select-none font-semibold text-red-600 hover:text-red-700`}
                    role="button"
                    onClick={onDelete}
                >
                    <TbTrash />
                </div>
            </Tooltip>
        </div>
    )
}

const WarehouseListTable = () => {
    const { t } = useTranslation()
    const navigate = useNavigate()
    const [deleteConfirmationOpen, setDeleteConfirmationOpen] = useState(false)
    const [toDeleteId, setToDeleteId] = useState<string | null>(null)

    const { data: warehouses, isLoading } = useGetWarehouses()
    const { mutate: deleteWarehouse } = useDeleteWarehouse()

    const handleCancel = () => {
        setDeleteConfirmationOpen(false)
        setToDeleteId(null)
    }

    const handleDelete = (id: string) => {
        setDeleteConfirmationOpen(true)
        setToDeleteId(id)
    }

    const handleEdit = useCallback(
        (id: string) => {
            navigate(`/warehouses/${id}/edit`)
        },
        [navigate],
    )

    const handleManageAreas = useCallback(
        (warehouseId: string) => {
            navigate(`/warehouses/${warehouseId}/areas`)
        },
        [navigate],
    )

    const handleConfirmDelete = async () => {
        if (toDeleteId) {
            try {
                await deleteWarehouse(toDeleteId)
                setDeleteConfirmationOpen(false)
                toast.push(
                    <Notification type="success">
                        {t('nav.warehouses.updateSuccess')}
                    </Notification>,
                    { placement: 'top-center' },
                )
                setToDeleteId(null)
            } catch (error: any) {
                const errorMessage = error?.errors
                toast.push(
                    <Notification type="danger">
                        {errorMessage
                            ? errorMessage[0]?.description
                            : error?.status}
                    </Notification>,
                    { placement: 'top-center' },
                )
            }
        }
    }

    const allColumns: ColumnDef<Warehouse>[] = useMemo(() => {
        const columnDefinitions = [
            {
                key: 'id',
                header: t('nav.shared.id'),
                accessorKey: 'id',
                cell: (props: any) => {
                    const row = props.row.original
                    return <span className="heading-text">{row.id}</span>
                },
            },
            {
                key: 'name',
                header: t('nav.shared.name'),
                accessorKey: 'name',
                cell: (props: any) => {
                    const row = props.row.original
                    return <span className="heading-text">{row.name}</span>
                },
            },
            {
                key: 'organizationalNodeName',
                header: t('nav.warehouses.organization'),
                accessorKey: 'organizationalNodeName',
                cell: (props: any) => {
                    const row = props.row.original
                    return (
                        <span className="heading-text">
                            {row.organizationalNodeName}
                        </span>
                    )
                },
            },
            {
                key: 'villageNameAr',
                header: t('nav.buildings.village'),
                accessorKey: 'villageNameAr',
                cell: (props: any) => {
                    const row = props.row.original
                    return (
                        <span className="heading-text">
                            {row.villageNameAr}
                        </span>
                    )
                },
            },
            {
                key: 'governorateNameAr',
                header: t('nav.buildings.governorate'),
                accessorKey: 'governorateNameAr',
                cell: (props: any) => {
                    const row = props.row.original
                    return (
                        <span className="heading-text">
                            {row.governorateNameAr}
                        </span>
                    )
                },
            },
            {
                key: 'responsiblePersonName',
                header: t('nav.shared.assignedBy'),
                accessorKey: 'responsiblePersonName',
                cell: (props: any) => {
                    const row = props.row.original
                    return (
                        <span className="heading-text">
                            {row.responsiblePersonName}
                        </span>
                    )
                },
            },
            {
                key: 'numberOfAreas',
                header: t('nav.warehouses.areas'),
                accessorKey: 'numberOfAreas',
                cell: (props: any) => {
                    const row = props.row.original
                    return (
                        <button
                            type="button"
                            className="flex items-center gap-2 px-3 py-2 bg-blue-50 hover:bg-blue-100 dark:bg-blue-900/20 dark:hover:bg-blue-900/40 text-blue-700 dark:text-blue-300 rounded-lg border border-blue-200 dark:border-blue-700 hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-200 font-medium text-sm"
                            onClick={() => handleManageAreas(row.id)}
                        >
                            <span className="bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 px-2 py-1 rounded-full text-xs font-semibold min-w-[24px] text-center">
                                {row.numberOfAreas || 0}
                            </span>
                        </button>
                    )
                },
            },
            {
                key: 'status',
                header: t('nav.shared.status'),
                accessorKey: 'status',
                cell: (props: any) => {
                    const row = props.row.original
                    let statusText = ''
                    let statusClass = ''

                    switch (row.status) {
                        case 0:
                            statusText = t('nav.shared.inactive')
                            statusClass =
                                'bg-yellow-200 dark:bg-yellow-200 text-gray-900 dark:text-gray-900'
                            break
                        case 1:
                            statusText = t('nav.shared.active')
                            statusClass =
                                'bg-emerald-200 dark:bg-emerald-200 text-gray-900 dark:text-gray-900'
                            break
                        case 2:
                            statusText = t('nav.shared.frozen')
                            statusClass =
                                'bg-red-200 dark:bg-red-200 text-gray-900 dark:text-gray-900'
                            break
                        default:
                            statusText = t('nav.GlobalActions.unapproved')
                            statusClass =
                                'bg-yellow-200 dark:bg-yellow-200 text-gray-900 dark:text-gray-900'
                    }

                    return (
                        <div className="flex items-center justify-center">
                            <Tag className={statusClass}>
                                <span className="capitalize">{statusText}</span>
                            </Tag>
                        </div>
                    )
                },
            },
            {
                key: 'action',
                header: t('nav.shared.actions'),
                id: 'action',
                cell: (props: any) => (
                    <ActionColumn
                        onEdit={() => handleEdit(props.row.original.id)}
                        onDelete={() => handleDelete(props.row.original.id)}
                    />
                ),
            },
        ]
        // Filter columns based on visibleColumns prop
        return columnDefinitions
    }, [t, handleEdit, handleManageAreas])

    // const handleTableSortChange = (sort: OnSortParam) => {
    //     const newSortConfig: WarehouseSortConfig = {
    //         field: sort.key as keyof WarehouseColumns,
    //         direction: sort.order === 'desc' ? 'desc' : 'asc',
    //     }
    //     handleSortChange(newSortConfig)
    // }

    return (
        <>
            <DataTable
                selectable
                columns={allColumns}
                data={warehouses}
                noData={!isLoading && warehouses?.length === 0}
                skeletonAvatarColumns={[0]}
                skeletonAvatarProps={{ width: 28, height: 28 }}
                loading={isLoading}
                cellBorder={true}
                // onSort={handleTableSortChange}
                // onCheckBoxChange={(checked: boolean, row: any) => {
                //     handleSelectWarehouse(row.id, checked)
                // }}
                // onIndeterminateCheckBoxChange={(checked: boolean) => {
                //     handleSelectAllWarehouses(checked)
                // }}
                // checkboxChecked={(row: any) => selectedWarehouses.includes(row.id)}
            />

            <ConfirmDialog
                isOpen={deleteConfirmationOpen}
                type="danger"
                title={t('nav.warehouses.deleteWarehouse')}
                onClose={handleCancel}
                onRequestClose={handleCancel}
                onCancel={handleCancel}
                onConfirm={handleConfirmDelete}
            >
                <p>{t('nav.warehouses.confirmDeleteWarehouse')}</p>
            </ConfirmDialog>
        </>
    )
}

export default WarehouseListTable
