/* eslint-disable @typescript-eslint/no-unused-vars */
import { DragDropContext, type DropResult } from '@hello-pangea/dnd'
import { useState, useRef } from 'react'
import { CreatedNode } from '@/@types/organizationalStructure'
import AdaptiveCard from '@/components/shared/AdaptiveCard'
import ScrumBoardHeader from './components/ScrumBoardHeader'
import BoardViews, { BoardViewsRef } from './components/BoardViews'
import NodeDialog from './components/NodeDialog'
import { TbBuilding, TbCheck, TbAlertTriangle } from 'react-icons/tb'
import useTranslation from '@/utils/hooks/useTranslation'
import BubblePopup from '@/components/shared/BubblePopup'
import {
    useCreateOrgStructure,
    useDeleteOrgStructure,
    useGetOrgsStructure,
    useGetOrgStructureByCode,
} from '@/hooks/orgs'

const ScrumBoard = () => {
    const { t } = useTranslation()

    const { rootNodes = [] } = useGetOrgsStructure()
    const { data: nodeDetails = null } = useGetOrgStructureByCode('1')
    const createNode = useCreateOrgStructure()
    const deleteNode = useDeleteOrgStructure()

    const [newItems, setNewItems] = useState<CreatedNode>({
        name: '',
        description: '',
        parentCode: '',
    })
    const [isDialogOpen, setIsDialogOpen] = useState(false)
    const [pendingItems, setPendingItems] = useState<CreatedNode[]>([])
    const [isDragging, setIsDragging] = useState(false)
    const [dragFeedback, setDragFeedback] = useState<{
        type: 'success' | 'warning' | 'error' | null
        message: string
        operation?: 'add' | 'delete' | 'update'
    }>({ type: null, message: '' })
    const [showBubblePopup, setShowBubblePopup] = useState(false)
    const boardViewsRef = useRef<BoardViewsRef>(null)

    const onDragStart = () => {
        setIsDragging(true)
        setDragFeedback({
            type: 'warning',
            message: t('nav.unitStructure.dragToAddNewItem'),
        })
    }

    const onDragEnd = (result: DropResult) => {
        const { destination, draggableId } = result
        setIsDragging(false)

        if (!destination) {
            setDragFeedback({
                type: 'error',
                message: t('nav.unitStructure.failedToAddNode'),
                operation: 'add',
            })
            setTimeout(() => setDragFeedback({ type: null, message: '' }), 3000)
            return console.log('no destination')
        }

        // Handle new item drop
        if (draggableId.startsWith('pending-item-')) {
            const parentCode =
                destination.droppableId === 'root-list'
                    ? null
                    : destination.droppableId
            const itemIndex = parseInt(draggableId.replace('pending-item-', ''))
            const pendingItem = pendingItems[itemIndex]

            if (!pendingItem) {
                setDragFeedback({
                    type: 'error',
                    message: t('nav.unitStructure.failedToAddNode'),
                    operation: 'add',
                })
                setTimeout(
                    () => setDragFeedback({ type: null, message: '' }),
                    3000,
                )
                return
            }

            // Create the new organizational node
            const newNode: CreatedNode = {
                name: pendingItem.name,
                description: pendingItem.description || '',
                parentCode: parentCode || '',
            }

            setDragFeedback({
                type: 'success',
                message: `${t('nav.unitStructure.nodeAddedSuccessfully')}: "${pendingItem.name}"`,
                operation: 'add',
            })
            setShowBubblePopup(true)

            createNode.mutate(newNode)
            setNewItems({ name: '', description: '', parentCode: '' })
            // Remove the dropped item from pending items
            setPendingItems((prev) =>
                prev.filter((_, index) => index !== itemIndex),
            )
        }
    }

    const handleNodeClick = (code: string) => {
        // handleNodeView(code)
        setIsDialogOpen(true)
    }

    const handleAddItem = async () => {
        if (!newItems.name.trim()) return

        // Create a pending item that will be added to the droppable area
        const tempItem: CreatedNode = {
            name: newItems.name,
            description: newItems.description || '',
            parentCode: newItems.parentCode || '',
        }

        if (newItems.parentCode !== '') {
            try {
                newItems.parentCode === 'root' ? (tempItem.parentCode = '') : ''
                await createNode.mutateAsync(tempItem)
                setDragFeedback({
                    type: 'success',
                    message: `${t('nav.unitStructure.nodeAddedSuccessfully')}: "${tempItem.name}"`,
                    operation: 'add',
                })
                setShowBubblePopup(true)
            } catch (error) {
                setDragFeedback({
                    type: 'error',
                    message: t('nav.unitStructure.failedToAddNode'),
                    operation: 'add',
                })
                setTimeout(
                    () => setDragFeedback({ type: null, message: '' }),
                    3000,
                )
            }
        } else {
            setPendingItems((prev) => [...prev, tempItem])
        }

        setNewItems({ name: '', description: '', parentCode: '' })
    }

    const handleDeleteNode = async (nodeId: string) => {
        try {
            await deleteNode.mutateAsync(nodeId)
            setDragFeedback({
                type: 'success',
                message: t('nav.unitStructure.nodeDeletedSuccessfully'),
                operation: 'delete',
            })
            setShowBubblePopup(true)
        } catch (error) {
            setDragFeedback({
                type: 'error',
                message: t('nav.unitStructure.failedToDeleteNode'),
                operation: 'delete',
            })
            setTimeout(() => setDragFeedback({ type: null, message: '' }), 3000)
        }
    }

    const handleCollapseAll = () => {
        boardViewsRef.current?.collapseAll()
    }

    const handleExpandAll = () => {
        boardViewsRef.current?.expandAll()
    }

    const handleCloseBubblePopup = () => {
        setShowBubblePopup(false)
        setDragFeedback({ type: null, message: '' })
    }

    return (
        <>
            <DragDropContext onDragStart={onDragStart} onDragEnd={onDragEnd}>
                <AdaptiveCard
                    className="h-full relative"
                    bodyClass="h-full flex flex-col"
                >
                    {/* Architectural-themed drag feedback overlay */}
                    {isDragging && (
                        <div className="absolute inset-0 bg-blue-50/30 dark:bg-blue-900/20 backdrop-blur-sm z-10 pointer-events-none">
                            <div className="absolute inset-0 bg-gradient-to-br from-transparent via-blue-100/20 to-transparent dark:from-transparent dark:via-blue-800/20 dark:to-transparent">
                                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                                    <div className="bg-white/90 dark:bg-gray-800/90 rounded-xl p-4 shadow-2xl border border-blue-200 dark:border-blue-700">
                                        <div className="flex items-center gap-3">
                                            <TbBuilding className="w-6 h-6 text-blue-500 animate-pulse" />
                                            <div>
                                                <p className="font-medium text-gray-900 dark:text-gray-100">
                                                    Building Structure
                                                </p>
                                                <p className="text-sm text-blue-600 dark:text-blue-400">
                                                    {t(
                                                        'nav.unitStructure.dragHint',
                                                    )}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Enhanced drag feedback notification */}
                    {dragFeedback.type && (
                        <div
                            className={`absolute top-4 right-4 z-20 max-w-sm transform transition-all duration-300 ${
                                dragFeedback.type
                                    ? 'translate-x-0 opacity-100'
                                    : 'translate-x-full opacity-0'
                            }`}
                        >
                            <div
                                className={`rounded-xl p-4 shadow-lg border ${
                                    dragFeedback.type === 'success'
                                        ? 'bg-emerald-50 dark:bg-emerald-900/20 border-emerald-200 dark:border-emerald-700'
                                        : dragFeedback.type === 'warning'
                                          ? 'bg-amber-50 dark:bg-amber-900/20 border-amber-200 dark:border-amber-700'
                                          : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-700'
                                }`}
                            >
                                <div className="flex items-start gap-3">
                                    {dragFeedback.type === 'success' && (
                                        <TbCheck className="w-5 h-5 text-emerald-500 mt-0.5" />
                                    )}
                                    {dragFeedback.type === 'warning' && (
                                        <TbAlertTriangle className="w-5 h-5 text-amber-500 mt-0.5" />
                                    )}
                                    {dragFeedback.type === 'error' && (
                                        <TbAlertTriangle className="w-5 h-5 text-red-500 mt-0.5" />
                                    )}
                                    <div>
                                        <p
                                            className={`text-sm font-medium ${
                                                dragFeedback.type === 'success'
                                                    ? 'text-emerald-800 dark:text-emerald-200'
                                                    : dragFeedback.type ===
                                                        'warning'
                                                      ? 'text-amber-800 dark:text-amber-200'
                                                      : 'text-red-800 dark:text-red-200'
                                            }`}
                                        >
                                            {dragFeedback.message}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}

                    <ScrumBoardHeader
                        newItems={newItems}
                        setNewItems={setNewItems}
                        pendingItems={pendingItems}
                        setPendingItems={setPendingItems}
                        onAddItem={handleAddItem}
                        onCollapseAll={handleCollapseAll}
                        onExpandAll={handleExpandAll}
                    />
                    <div className="flex-1 overflow-auto">
                        <BoardViews
                            ref={boardViewsRef}
                            items={rootNodes || []}
                            onNodeClick={handleNodeClick}
                        />
                    </div>
                </AdaptiveCard>
            </DragDropContext>

            {/* Bubble Popup for success notifications */}
            <BubblePopup
                isVisible={showBubblePopup && dragFeedback.type === 'success'}
                type={dragFeedback.type || 'success'}
                message={dragFeedback.message}
                title={
                    dragFeedback.operation === 'delete'
                        ? t('nav.unitStructure.deleteSuccess')
                        : t('nav.unitStructure.success')
                }
                onClose={handleCloseBubblePopup}
            />

            {/* Node Dialog for editing/deleting */}
            <NodeDialog
                isOpen={isDialogOpen}
                node={nodeDetails}
                onDelete={handleDeleteNode}
                onClose={() => {
                    setIsDialogOpen(false)
                }}
            />
        </>
    )
}

export default ScrumBoard
