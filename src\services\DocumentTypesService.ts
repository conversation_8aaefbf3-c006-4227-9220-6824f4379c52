import ApiService from '@/services/ApiService'

export interface DocumentType {
    id?: number
    name: string
    status: number
    [key: string]: unknown
}

export async function getDocumentTypes() {
    return ApiService.get<DocumentType[]>('/DocumentTypes')
}

export async function getDocumentTypeById(id: number) {
    return ApiService.get<DocumentType>(`/DocumentTypes/${id}`)
}

export async function getDocumentTypeByName(name: string) {
    return ApiService.get<DocumentType>(`/DocumentTypes/by-name/${name}`)
}

export async function createDocumentType(documentType: DocumentType) {
    return ApiService.post<DocumentType>('/DocumentTypes', documentType)
}

export async function updateDocumentType(
    id: number,
    documentType: DocumentType,
) {
    return ApiService.put<DocumentType>(`/DocumentTypes/${id}`, documentType)
}

export async function updateDocumentTypeStatus(id: number, status: number) {
    return ApiService.patch<DocumentType>(`/DocumentTypes/${id}/status`, {
        status,
    })
}
