/* eslint-disable @typescript-eslint/no-explicit-any */
import { jwtDecode } from 'jwt-decode'

export const getUserRoleFromToken = (token: string): string[] => {
    try {
        if (!token) {
            console.warn('JWT token is empty or undefined')
            return []
        }

        const decoded = jwtDecode<any>(token)
        if (!decoded || !decoded.roles) {
            console.warn('No roles found in JWT token')
            return []
        }

        return decoded.roles
    } catch (error) {
        console.error('Error decoding JWT token:', error)
        return []
    }
}
