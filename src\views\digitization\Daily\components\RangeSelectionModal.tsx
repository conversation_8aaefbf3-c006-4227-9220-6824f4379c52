import { useForm, Controller } from 'react-hook-form'
import Dialog from '@/components/ui/Dialog'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import { FormItem, Form } from '@/components/ui/Form'
import useTranslation from '@/utils/hooks/useTranslation'
import { TbCheck, TbX } from 'react-icons/tb'

interface RangeSelectionModalProps {
    isOpen: boolean
    onClose: () => void
    onSubmit: (fromFile: number, toFile: number) => void
    title: string
    actionType: 'print' | 'close' | 'delete' | null
}

interface RangeFormData {
    fromFile: number
    toFile: number
}

const RangeSelectionModal = ({
    isOpen,
    onClose,
    onSubmit,
    title,
    actionType,
}: RangeSelectionModalProps) => {
    const { t } = useTranslation()

    const {
        handleSubmit,
        control,
        reset,
        formState: { errors, isValid },
        watch,
    } = useForm<RangeFormData>({
        defaultValues: {
            fromFile: 1,
            toFile: 1,
        },
        mode: 'onChange',
    })

    const fromFile = watch('fromFile')
    const toFile = watch('toFile')

    const onFormSubmit = (data: RangeFormData) => {
        if (data.fromFile <= data.toFile) {
            onSubmit(data.fromFile, data.toFile)
            reset()
        }
    }

    const handleClose = () => {
        reset()
        onClose()
    }

    const getActionColor = () => {
        switch (actionType) {
            case 'print':
                return 'text-blue-600'
            case 'close':
                return 'text-orange-600'
            case 'delete':
                return 'text-red-600'
            default:
                return 'text-gray-600'
        }
    }

    const getButtonColor = () => {
        switch (actionType) {
            case 'print':
                return 'bg-blue-500 hover:bg-blue-600'
            case 'close':
                return 'bg-orange-500 hover:bg-orange-600'
            case 'delete':
                return 'bg-red-500 hover:bg-red-600'
            default:
                return 'bg-gray-500 hover:bg-gray-600'
        }
    }

    return (
        <Dialog
            isOpen={isOpen}
            onClose={handleClose}
            onRequestClose={handleClose}
        >
            <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                    <h4 className={`text-lg font-semibold ${getActionColor()}`}>
                        {title}
                    </h4>
                </div>

                <Form onSubmit={handleSubmit(onFormSubmit)}>
                    <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                            <FormItem
                                label={t('nav.shared.fromFileNumber')}
                                invalid={!!errors.fromFile}
                                errorMessage={errors.fromFile?.message}
                            >
                                <Controller
                                    name="fromFile"
                                    control={control}
                                    rules={{
                                        required: t('nav.shared.fieldRequired'),
                                        min: {
                                            value: 1,
                                            message: t('nav.shared.minValue', {
                                                value: 1,
                                            }),
                                        },
                                        validate: (value) => {
                                            if (toFile && value > toFile) {
                                                return t(
                                                    'nav.shared.fromFileMustBeLessThanToFile',
                                                )
                                            }
                                            return true
                                        },
                                    }}
                                    render={({ field }) => (
                                        <Input
                                            {...field}
                                            type="number"
                                            min={1}
                                            placeholder={t(
                                                'nav.shared.enterFileNumber',
                                            )}
                                            onChange={(e) =>
                                                field.onChange(
                                                    parseInt(e.target.value) ||
                                                        1,
                                                )
                                            }
                                        />
                                    )}
                                />
                            </FormItem>

                            <FormItem
                                label={t('nav.shared.toFileNumber')}
                                invalid={!!errors.toFile}
                                errorMessage={errors.toFile?.message}
                            >
                                <Controller
                                    name="toFile"
                                    control={control}
                                    rules={{
                                        required: t('nav.shared.fieldRequired'),
                                        min: {
                                            value: 1,
                                            message: t('nav.shared.minValue', {
                                                value: 1,
                                            }),
                                        },
                                        validate: (value) => {
                                            if (fromFile && value < fromFile) {
                                                return t(
                                                    'nav.shared.toFileMustBeGreaterThanFromFile',
                                                )
                                            }
                                            return true
                                        },
                                    }}
                                    render={({ field }) => (
                                        <Input
                                            {...field}
                                            type="number"
                                            min={1}
                                            placeholder={t(
                                                'nav.shared.enterFileNumber',
                                            )}
                                            onChange={(e) =>
                                                field.onChange(
                                                    parseInt(e.target.value) ||
                                                        1,
                                                )
                                            }
                                        />
                                    )}
                                />
                            </FormItem>
                        </div>

                        {fromFile && toFile && (
                            <div className="bg-gray-50 p-3 rounded-md">
                                <p className="text-sm text-gray-600">
                                    {t('nav.shared.rangePreview')}:{' '}
                                    {t('nav.shared.rangePreviewFormat', {
                                        from: fromFile,
                                        to: toFile,
                                        count: toFile - fromFile + 1,
                                        files: t('nav.shared.files'),
                                    })}
                                </p>
                            </div>
                        )}
                    </div>

                    <div className="flex items-center justify-end gap-3 mt-6 pt-4 border-t border-gray-200">
                        <Button
                            type="button"
                            variant="plain"
                            onClick={handleClose}
                        >
                            {t('nav.shared.cancel')}
                        </Button>
                        <Button
                            type="submit"
                            variant="solid"
                            icon={<TbCheck />}
                            className={getButtonColor()}
                            disabled={!isValid || !fromFile || !toFile}
                        >
                            {t('nav.shared.confirm')}
                        </Button>
                    </div>
                </Form>
            </div>
        </Dialog>
    )
}

export default RangeSelectionModal
