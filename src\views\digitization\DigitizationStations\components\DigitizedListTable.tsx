/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMemo, useState, useCallback } from 'react'
import Tooltip from '@/components/ui/Tooltip'
import DataTable from '@/components/shared/DataTable'
import ConfirmDialog from '@/components/shared/ConfirmDialog'
import { TbPencil, TbTrash } from 'react-icons/tb'
import type { OnSortParam, ColumnDef } from '@/components/shared/DataTable'
import { Tag } from '@/components/ui'
import useTranslation from '@/utils/hooks/useTranslation'
import type { DigitizationStationColumns } from '@/@types/digitizationStation'
import {
    useDeleteDigitizationStation,
    useGetDigitizationStations,
} from '@/hooks/digitization-station'

interface DigitizedListTableProps {
    onEdit: (station_id: number) => void
}

const ActionColumn = ({
    onEdit,
    onDelete,
}: {
    onEdit: () => void
    onDelete: () => void
}) => {
    const { t } = useTranslation()
    return (
        <div className="flex items-center justify-center gap-3">
            <Tooltip title={t('nav.shared.edit')}>
                <div
                    className={`text-xl cursor-pointer select-none font-semibold`}
                    role="button"
                    onClick={onEdit}
                >
                    <TbPencil />
                </div>
            </Tooltip>
            <Tooltip title={t('nav.shared.delete')}>
                <div
                    className={`text-xl cursor-pointer select-none font-semibold`}
                    role="button"
                    onClick={onDelete}
                >
                    <TbTrash />
                </div>
            </Tooltip>
        </div>
    )
}

const DigitizedListTable = ({ onEdit }: DigitizedListTableProps) => {
    const { t } = useTranslation()
    const [deleteConfirmationOpen, setDeleteConfirmationOpen] = useState(false)
    const [toDeleteId, setToDeleteId] = useState<number | null>(null)

    const { data: stations, isLoading } = useGetDigitizationStations()
    const { mutate: deleteStation } = useDeleteDigitizationStation()

    const handleCancel = () => {
        setDeleteConfirmationOpen(false)
        setToDeleteId(null)
    }

    const handleDelete = useCallback((id: number) => {
        setDeleteConfirmationOpen(true)
        setToDeleteId(id)
    }, [])

    const handleEdit = useCallback(
        (id: number) => {
            onEdit(id)
        },
        [onEdit],
    )

    const handleConfirmDelete = async () => {
        if (toDeleteId) {
            try {
                await deleteStation(toDeleteId)
                setDeleteConfirmationOpen(false)
                setToDeleteId(null)
            } catch (error) {
                console.error('Failed to delete station:', error)
            }
        }
    }

    const allColumns: ColumnDef<DigitizationStationColumns>[] = useMemo(() => {
        const columnDefinitions = [
            {
                key: 'id',
                header: t('nav.shared.id'),
                accessorKey: 'id',
                cell: (props: any) => {
                    const row = props.row.original
                    return <span className="heading-text">{row.id}</span>
                },
            },
            {
                key: 'numberOfOrganizations',
                header: t('nav.digitizationStations.numberOfOrganizations'),
                accessorKey: 'numberOfOrganizations',
                cell: (props: any) => {
                    const row = props.row.original
                    return (
                        <span className="heading-text">
                            {row.numberOfOrganizations}
                        </span>
                    )
                },
            },
            {
                key: 'numberOfUsers',
                header: t('nav.digitizationStations.numberOfUsers'),
                accessorKey: 'numberOfUsers',
                cell: (props: any) => {
                    const row = props.row.original
                    return (
                        <span className="heading-text">
                            {row.numberOfUsers}
                        </span>
                    )
                },
            },
            {
                key: 'creationDate',
                header: t('nav.digitizationStations.creationDate'),
                accessorKey: 'creationDate',
                cell: (props: any) => {
                    const row = props.row.original
                    return (
                        <span className="heading-text">
                            {new Date(row.creationDate).toLocaleDateString()}
                        </span>
                    )
                },
            },
            {
                key: 'buildingName',
                header: t('nav.digitizationStations.buildingName'),
                accessorKey: 'buildingName',
                cell: (props: any) => {
                    const row = props.row.original
                    return (
                        <span className="heading-text">{row.buildingName}</span>
                    )
                },
            },
            {
                key: 'buildingAddress',
                header: t('nav.digitizationStations.buildingAddress'),
                accessorKey: 'buildingAddress',
                cell: (props: any) => {
                    const row = props.row.original
                    return (
                        <span className="heading-text">
                            {row.buildingAddress}
                        </span>
                    )
                },
            },
            {
                key: 'status',
                header: t('nav.shared.status'),
                accessorKey: 'status',
                cell: (props: any) => {
                    const row = props.row.original
                    const statusText =
                        row.status === 1
                            ? t('nav.GlobalActions.approve')
                            : t('nav.GlobalActions.frozen')
                    const statusClass =
                        row.status === 1
                            ? 'bg-emerald-200 dark:bg-emerald-200 text-gray-900 dark:text-gray-900'
                            : 'bg-red-200 dark:bg-red-200 text-gray-900 dark:text-gray-900'
                    return (
                        <div className="flex items-center justify-center">
                            <Tag className={statusClass}>
                                <span className="capitalize">{statusText}</span>
                            </Tag>
                        </div>
                    )
                },
            },
            {
                key: 'action',
                header: t('nav.shared.edit'),
                id: 'action',
                cell: (props: any) => (
                    <ActionColumn
                        onEdit={() => handleEdit(props.row.original.id)}
                        onDelete={() => handleDelete(props.row.original.id)}
                    />
                ),
            },
        ]
        return columnDefinitions
    }, [t, handleEdit, handleDelete])

    const handleTableSortChange = (sort: OnSortParam) => {
        console.log('Sort requested:', sort)
    }

    return (
        <>
            <DataTable
                columns={allColumns}
                data={stations}
                noData={!isLoading && stations?.length === 0}
                skeletonAvatarColumns={[0]}
                skeletonAvatarProps={{ width: 28, height: 28 }}
                loading={isLoading}
                cellBorder={true}
                onSort={handleTableSortChange}
            />

            <ConfirmDialog
                isOpen={deleteConfirmationOpen}
                type="danger"
                title={t('nav.digitizationStations.deleteStation')}
                onClose={handleCancel}
                onRequestClose={handleCancel}
                onCancel={handleCancel}
                onConfirm={handleConfirmDelete}
            >
                <p>{t('nav.digitizationStations.confirmDeleteStation')}</p>
            </ConfirmDialog>
        </>
    )
}

export default DigitizedListTable
