/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback, useMemo, useState } from 'react'
import Tooltip from '@/components/ui/Tooltip'
import DataTable from '@/components/shared/DataTable'
import ConfirmDialog from '@/components/shared/ConfirmDialog'
import { TbPencil, TbTrash } from 'react-icons/tb'
import useTranslation from '@/utils/hooks/useTranslation'
import Status from '@/components/shared/displaying/Status'
import { Notification, toast } from '@/components/ui'
import { useDeleteStock, useGetStocks } from '@/hooks/stock'
import useLocale from '@/utils/hooks/useLocale'

interface StockListTableProps {
    onEdit: (stock_id: number) => void
}

const ActionColumn = ({
    onEdit,
    onDelete,
}: {
    onEdit: () => void
    onDelete: () => void
}) => {
    const { t } = useTranslation()
    return (
        <div className="flex items-center justify-center gap-3">
            <Tooltip title={t('nav.shared.edit')}>
                <div
                    className={`text-xl cursor-pointer select-none font-semibold`}
                    role="button"
                    onClick={onEdit}
                >
                    <TbPencil />
                </div>
            </Tooltip>
            <Tooltip title={t('nav.shared.delete')}>
                <div
                    className={`text-xl cursor-pointer select-none font-semibold`}
                    role="button"
                    onClick={onDelete}
                >
                    <TbTrash />
                </div>
            </Tooltip>
        </div>
    )
}

const StockListTable = ({ onEdit }: StockListTableProps) => {
    const { t } = useTranslation()
    const { locale } = useLocale()
    const isRTL = locale === 'ar' ? true : false
    const [deleteConfirmationOpen, setDeleteConfirmationOpen] = useState(false)
    const [toDeleteId, setToDeleteId] = useState<number | null>(null)

    const { data: stocks, isLoading } = useGetStocks()
    const deleteStock = useDeleteStock()

    const handleCancel = () => {
        setDeleteConfirmationOpen(false)
        setToDeleteId(null)
    }

    const handleDelete = (id: number) => {
        setDeleteConfirmationOpen(true)
        setToDeleteId(id)
    }

    const handleEdit = useCallback(
        (id: number) => {
            onEdit(id)
        },
        [onEdit],
    )

    const handleConfirmDelete = async () => {
        if (toDeleteId) {
            try {
                await deleteStock.mutateAsync(toDeleteId)
                toast.push(
                    <Notification title="" type="success" duration={2500}>
                        {t('nav.shared.deleted')}
                    </Notification>,
                    { placement: 'top-center' },
                )
                setDeleteConfirmationOpen(false)
                setToDeleteId(null)
            } catch (error: any) {
                const data = error?.errors[0]

                toast.push(
                    <Notification title="" type="danger" duration={2500}>
                        {data?.description || t('nav.systemSecurity.failed')}
                    </Notification>,
                    { placement: 'top-center' },
                )
            }
        }
    }

    const allColumns = useMemo(() => {
        const columnDefinitions = [
            {
                key: 'id',
                header: t('nav.shared.id'),
                accessorKey: 'id',
                cell: (props: any) => {
                    const row = props.row.original
                    return <span className="heading-text">{row.id}</span>
                },
            },
            {
                key: 'name',

                header: t('nav.shared.name'),
                accessorKey: 'name',
                cell: (props: any) => {
                    const row = props.row.original
                    return <span className="heading-text">{row.name}</span>
                },
            },
            {
                key: 'village',
                header: t('nav.buildings.village'),
                accessorKey: 'villageNameAr',
                cell: (props: any) => {
                    const row = props.row.original
                    return (
                        <span className="heading-text">
                            {row.villageNameAr}
                        </span>
                    )
                },
            },
            {
                key: 'city',
                header: t('nav.buildings.city'),
                accessorKey: isRTL
                    ? 'cityOrDistrictNameAr'
                    : 'cityOrDistrictNameEn',
                cell: (props: any) => {
                    const row = props.row.original
                    return (
                        <span className="heading-text">
                            {isRTL
                                ? row.cityOrDistrictNameAr
                                : row.cityOrDistrictNameEn}
                        </span>
                    )
                },
            },
            {
                key: 'governorate',
                header: t('nav.buildings.governorate'),
                accessorKey: isRTL ? 'governorateNameAr' : 'governorateNameEn',
                cell: (props: any) => {
                    const row = props.row.original
                    return (
                        <span className="heading-text">
                            {isRTL
                                ? row.governorateNameAr
                                : row.governorateNameEn}
                        </span>
                    )
                },
            },
            {
                key: 'managerName',
                header: t('nav.shared.manager'),
                accessorKey: 'managerName',
                cell: (props: any) => {
                    const row = props.row.original
                    return (
                        <span className="heading-text">{row.managerName}</span>
                    )
                },
            },
            {
                key: 'status',
                header: t('nav.shared.status'),
                accessorKey: 'status',
                cell: (props: any) => <Status row={props.row} />,
            },
            {
                key: 'action',
                header: t('nav.shared.edit'),
                id: 'action',
                cell: (props: any) => (
                    <ActionColumn
                        onEdit={() => handleEdit(props.row.original.id)}
                        onDelete={() => handleDelete(props.row.original.id)}
                    />
                ),
            },
        ]
        // Filter columns based on visibleColumns prop
        return columnDefinitions
    }, [handleEdit, isRTL, t])

    const handleTableSortChange = () => {}

    return (
        <>
            <DataTable
                selectable
                columns={allColumns}
                data={stocks}
                noData={!isLoading && stocks?.length === 0}
                skeletonAvatarColumns={[0]}
                skeletonAvatarProps={{ width: 28, height: 28 }}
                loading={isLoading}
                cellBorder={true}
                checkboxChecked={() => false}
                onSort={handleTableSortChange}
                onCheckBoxChange={() => ''}
                onIndeterminateCheckBoxChange={() => {}}
            />

            <ConfirmDialog
                isOpen={deleteConfirmationOpen}
                type="danger"
                title={t('nav.shared.deleteStock')}
                onClose={handleCancel}
                onRequestClose={handleCancel}
                onCancel={handleCancel}
                onConfirm={handleConfirmDelete}
            >
                <p>{t('nav.shared.confirmDeleteStock')}</p>
            </ConfirmDialog>
        </>
    )
}

export default StockListTable
