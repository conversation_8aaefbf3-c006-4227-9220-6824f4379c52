/* eslint-disable @typescript-eslint/no-explicit-any */
import { create } from 'zustand'
import { jwtDecode } from 'jwt-decode'
import { clearTokenRefresh } from '@/services/axios/authManger'

interface AuthState {
    setAuth: (token: string, refreshToken: string) => void
    setActiveOrg: (orgCode: string) => void
    logout: () => void
}

export const useAuthStore = create<AuthState>()(() => ({
    setAuth: (token, refreshToken) => {
        localStorage.setItem('token', token)
        localStorage.setItem('refreshToken', refreshToken)
        const decoded: any = jwtDecode(token)
        localStorage.setItem('roles', decoded.roles || null)
    },

    setActiveOrg: (orgCode) => {
        localStorage.setItem('activeOrg', orgCode)
    },

    logout: () => {
        clearTokenRefresh()
        // localStorage.removeItem('token')
        // localStorage.removeItem('refreshToken')
        localStorage.removeItem('roles')
        localStorage.removeItem('activeOrg')
    },
}))
