/* eslint-disable @typescript-eslint/no-explicit-any */
import { create } from 'zustand'
import { jwtDecode } from 'jwt-decode'
import { clearTokenRefresh } from '@/services/axios/authManger'
import type { SignInResponse } from '@/@types/auth'
import { USER } from '@/constants/roles.constant'

interface AuthState {
    user: any | null
    mustChangePassword: boolean
    showPasswordDialog: boolean
    showBranchDialog: boolean
    setAuth: (signInResponse: SignInResponse) => void
    setActiveOrg: (orgCode: string) => void
    setPasswordChanged: () => void
    setBranchSelected: () => void
    logout: () => void
    getRoles: () => string[]
}

export const useAuthStore = create<AuthState>()((set, get) => ({
    user: null,
    mustChangePassword: false,
    showPasswordDialog: false,
    showBranchDialog: false,

    setAuth: (signInResponse) => {
        const { token, refreshToken, mustChangePassword, user } = signInResponse

        // Store tokens
        localStorage.setItem('token', token)
        localStorage.setItem('refreshToken', refreshToken)

        // Decode token to get roles
        const decoded: any = jwtDecode(token)
        const roles = decoded.roles || decoded.role || []
        const rolesArray = Array.isArray(roles) ? roles : [roles]
        localStorage.setItem('roles', JSON.stringify(rolesArray))

        // Determine what dialogs to show based on mustChangePassword and user role
        const hasUserRole = rolesArray.includes(USER)

        // Set user data and password change requirement
        set({
            user,
            mustChangePassword,
            showPasswordDialog: mustChangePassword,
            // Show branch dialog if no password change needed and user has USER role
            showBranchDialog: !mustChangePassword && hasUserRole,
        })
    },

    setActiveOrg: (orgCode) => {
        localStorage.setItem('activeOrg', orgCode)
    },

    setPasswordChanged: () => {
        // Check if user has USER role to determine if branch dialog should be shown
        const roles = get().getRoles()
        const hasUserRole = roles.includes(USER)

        set({
            mustChangePassword: false,
            showPasswordDialog: false,
            showBranchDialog: hasUserRole, // Show branch dialog after password change only for USER role
        })
    },

    setBranchSelected: () => {
        set({ showBranchDialog: false })
    },

    getRoles: () => {
        try {
            const roles = localStorage.getItem('roles')
            return roles ? JSON.parse(roles) : []
        } catch {
            return []
        }
    },

    logout: () => {
        clearTokenRefresh()
        localStorage.removeItem('token')
        localStorage.removeItem('refreshToken')
        localStorage.removeItem('roles')
        localStorage.removeItem('activeOrg')
        set({
            user: null,
            mustChangePassword: false,
            showPasswordDialog: false,
            showBranchDialog: false,
        })
    },
}))
