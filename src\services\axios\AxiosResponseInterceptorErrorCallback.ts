import { tokenStorage } from '@/utils/tokenStorage'
import type { AxiosError } from 'axios'
import API from './AxiosBase'

const unauthorizedCode = [401, 419, 440]
let isRefreshing = false

const AxiosResponseInterceptorErrorCallback = async (error: AxiosError) => {
    const { response, config } = error

    if (
        response &&
        unauthorizedCode.includes(response.status) &&
        !isRefreshing
    ) {
        isRefreshing = true

        try {
            const token = await tokenStorage.getToken()
            const refreshToken = await tokenStorage.getRefreshToken()

            if (token && refreshToken) {
                const refreshResponse = await API.post('/Auth/refresh-token', {
                    token,
                    refreshToken,
                })
                console.log('refreshResponse', refreshResponse)

                if (
                    refreshResponse.data.token &&
                    refreshResponse.data.refreshToken
                ) {
                    tokenStorage.setToken(refreshResponse.data.token)
                    tokenStorage.setRefreshToken(
                        refreshResponse.data.refreshToken,
                    )

                    isRefreshing = false
                    // Retry the original request
                    return API(config!)
                }
            }
        } catch (refreshError) {
            console.error('Token refresh failed:', refreshError)
        }

        isRefreshing = false
        // Sign out user and clear all auth data
    }

    return Promise.reject(error)
}

export default AxiosResponseInterceptorErrorCallback
