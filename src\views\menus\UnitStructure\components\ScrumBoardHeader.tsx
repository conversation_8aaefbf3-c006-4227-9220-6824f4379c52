/* eslint-disable @typescript-eslint/no-explicit-any */
import Input from '@/components/ui/Input'
import { Button } from '@/components/ui/Button'
import { TbPlus, TbTrash, TbBuilding, TbHierarchy3 } from 'react-icons/tb'
import useTranslation from '@/utils/hooks/useTranslation'
import { Droppable, Draggable } from '@hello-pangea/dnd'
import Select from '@/components/ui/Select'
import { CreatedNode } from '@/@types/organizationalStructure'
import ToggleCollapseExpandButton from './ToggleCollapseExpandButton'
import { useState } from 'react'
import { useGetOrgsStructure } from '@/hooks/orgs'

// import { Select } from '@/components/ui'

type ScrumBoardHeaderProps = {
    newItems: CreatedNode
    setNewItems: (items: CreatedNode) => void
    onAddItem: () => void
    pendingItems?: CreatedNode[]
    setPendingItems?: (items: CreatedNode[]) => void
    onCollapseAll: () => void
    onExpandAll: () => void
}

// Transform nested nodes to Select options format
type SelectOption = {
    value: string
    label: string
    level?: number
}

const transformNodesToOptions = (nodes: any[], level = 0): SelectOption[] => {
    const options: SelectOption[] = []

    nodes.forEach((node) => {
        const indent = '  '.repeat(level)
        options.push({
            value: node.code,
            label: `${indent}${node.name} | ${node.code}`,
            level,
        })

        if (node.children && node.children.length > 0) {
            options.push(...transformNodesToOptions(node.children, level + 1))
        }
    })

    return options
}

const ScrumBoardHeader = ({
    newItems,
    setNewItems,
    onAddItem,
    pendingItems,
    setPendingItems,
    onCollapseAll,
    onExpandAll,
}: ScrumBoardHeaderProps) => {
    const { nestedNodes } = useGetOrgsStructure()
    const { t } = useTranslation()
    const [selectedParent, setSelectedParent] = useState<SelectOption | null>(
        null,
    )

    const handleDeleteItem = (name: string) => {
        if (setPendingItems && pendingItems) {
            const updatedItems = pendingItems.filter(
                (item) => item.name !== name,
            )
            setPendingItems(updatedItems)
        }
    }

    const handleSelectParent = (code: string | null) => {
        setNewItems({ ...newItems, parentCode: code || '' })
    }

    const handleSingleSelectParent = (selectedOption: SelectOption | null) => {
        setSelectedParent(selectedOption)
        if (selectedOption) {
            handleSelectParent(selectedOption.value)
        } else {
            handleSelectParent('root')
        }
    }

    const handleAddItem = () => {
        onAddItem()
        // Reset the select level after adding
        setSelectedParent(null)
        setNewItems({ ...newItems, parentCode: '' })
    }

    // Transform nestedNodes to options and add root option
    const selectOptions: SelectOption[] = [
        { value: 'root', label: t('nav.unitStructure.createNewRoot') },
        ...transformNodesToOptions(nestedNodes),
    ]

    return (
        <div className="flex flex-col justify-between">
            {/* Header Section with Architectural Theme */}
            <div className="bg-gradient-to-r from-slate-50 to-blue-50 dark:from-slate-800 dark:to-blue-900/20 rounded-xl p-6 mb-6 border border-slate-200 dark:border-slate-700 shadow-sm">
                <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-4 sm:mb-6">
                    <div className="flex items-center gap-2 sm:gap-3">
                        <div className="p-1.5 sm:p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                            <TbBuilding className="w-5 h-5 sm:w-6 sm:h-6 text-blue-600 dark:text-blue-400" />
                        </div>
                        <div>
                            <h3 className="text-lg sm:text-xl font-semibold text-slate-800 dark:text-slate-200 flex items-center gap-2">
                                {t('nav.unitStructure.structure')}
                                <TbHierarchy3 className="w-4 h-4 sm:w-5 sm:h-5 text-slate-500" />
                            </h3>
                            <p className="text-xs sm:text-sm text-slate-600 dark:text-slate-400 mt-1">
                                {t('nav.unitStructure.pageHint')}
                            </p>
                        </div>
                    </div>
                    <div className="flex gap-2 w-full sm:w-auto">
                        <ToggleCollapseExpandButton
                            className="flex-1 sm:flex-none text-xs sm:text-sm"
                            onCollapseAll={onCollapseAll}
                            onExpandAll={onExpandAll}
                        />
                    </div>
                </div>

                {/* Enhanced Form Section */}
                <div className="bg-white dark:bg-slate-800 rounded-lg p-5 border border-slate-200 dark:border-slate-700 shadow-sm">
                    <div className="flex items-center gap-2 mb-4">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300">
                            {t('nav.unitStructure.addNewUnit')}
                        </h4>
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
                        <div className="space-y-2">
                            <Input
                                placeholder={t('nav.unitStructure.itemName')}
                                value={newItems.name}
                                className="bg-slate-50 dark:bg-slate-700 border-slate-200 dark:border-slate-600 focus:border-blue-500 dark:focus:border-blue-400 transition-colors text-sm"
                                onChange={(e) =>
                                    setNewItems({
                                        ...newItems,
                                        name: e.target.value,
                                    })
                                }
                            />
                        </div>
                        <div className="space-y-2">
                            <Input
                                placeholder={t('nav.unitStructure.description')}
                                value={newItems.description}
                                className="bg-slate-50 dark:bg-slate-700 border-slate-200 dark:border-slate-600 focus:border-blue-500 dark:focus:border-blue-400 transition-colors text-sm"
                                onChange={(e) =>
                                    setNewItems({
                                        ...newItems,
                                        description: e.target.value,
                                    })
                                }
                            />
                        </div>
                        <div className="space-y-2 sm:col-span-2 lg:col-span-1  ">
                            <Select<SelectOption, false>
                                isSearchable
                                isClearable
                                placeholder={t(
                                    'nav.unitStructure.selectParent',
                                )}
                                options={selectOptions}
                                value={selectedParent}
                                className="text-sm"
                                noOptionsMessage={() =>
                                    t('nav.unitStructure.noOptionsFound')
                                }
                                onChange={handleSingleSelectParent}
                            />
                        </div>
                        <div className="space-y-2 sm:col-span-2 lg:col-span-1">
                            <Button
                                variant="solid"
                                color="blue"
                                icon={<TbPlus />}
                                size="sm"
                                className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105 text-sm"
                                onClick={handleAddItem}
                            >
                                {t('nav.shared.add')}
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
            {pendingItems && pendingItems.length > 0 && (
                <Droppable
                    droppableId="new-item-container"
                    type="ITEMS"
                    isDropDisabled={true}
                >
                    {(provided, snapshot) => (
                        <div
                            ref={provided.innerRef}
                            {...provided.droppableProps}
                            className={`p-2 sm:p-3 rounded-lg border-2 transition-colors ${
                                snapshot.isDraggingOver
                                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                                    : 'border-gray-300 dark:border-gray-600'
                            }`}
                        >
                            {pendingItems.map((item, index) => (
                                <Draggable
                                    key={item.parentCode + ' ' + index}
                                    draggableId={`pending-item-${index}`}
                                    index={index}
                                >
                                    {(provided, dragSnapshot) => (
                                        <div className="flex flex-row-reverse items-center justify-between gap-2 sm:gap-4 mb-2">
                                            <Button
                                                className="flex justify-center items-center gap-1 sm:gap-2 text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-lg px-2 sm:px-3 py-1.5 sm:py-2 transition-all duration-200 shadow-sm hover:shadow-md text-xs sm:text-sm"
                                                onClick={() =>
                                                    handleDeleteItem(item.name)
                                                }
                                            >
                                                <TbTrash
                                                    size={16}
                                                    className=" self-center"
                                                />
                                            </Button>
                                            <div
                                                ref={provided.innerRef}
                                                {...provided.draggableProps}
                                                {...provided.dragHandleProps}
                                                className={`transition-all duration-200  w-full ${
                                                    dragSnapshot.isDragging
                                                        ? 'opacity-75 scale-105'
                                                        : ''
                                                }`}
                                            >
                                                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30 border border-blue-200 dark:border-blue-700 rounded-lg p-2 sm:p-3 shadow-sm hover:shadow-md transition-all cursor-grab active:cursor-grabbing hover:from-blue-100 hover:to-indigo-100 dark:hover:from-blue-900/40 dark:hover:to-indigo-900/40">
                                                    <div className="flex items-center gap-2 sm:gap-3 min-w-0">
                                                        <div className="p-1 bg-blue-200 dark:bg-blue-800 rounded flex-shrink-0">
                                                            <TbPlus
                                                                size={12}
                                                                className="sm:w-[14px] sm:h-[14px] text-blue-700 dark:text-blue-300"
                                                            />
                                                        </div>
                                                        <div className="min-w-0 flex-1">
                                                            <span className="text-xs sm:text-sm font-medium text-blue-800 dark:text-blue-200 block truncate">
                                                                {item.name}
                                                            </span>
                                                            {item.description && (
                                                                <p className="text-xs text-blue-600 dark:text-blue-400 mt-1 truncate">
                                                                    {
                                                                        item.description
                                                                    }
                                                                </p>
                                                            )}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    )}
                                </Draggable>
                            ))}
                            {provided.placeholder}
                        </div>
                    )}
                </Droppable>
            )}
        </div>
    )
}

export default ScrumBoardHeader
