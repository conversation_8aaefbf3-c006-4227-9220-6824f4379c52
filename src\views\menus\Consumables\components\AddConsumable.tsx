/* eslint-disable @typescript-eslint/no-unused-vars */
import { FormItem } from '@/components/ui/Form'
import Input from '@/components/ui/Input'
import { Controller, useWatch } from 'react-hook-form'
import useTranslation from '@/utils/hooks/useTranslation'
import { Card } from '@/components/ui'
import { Select } from '@/components/ui/Select'
import { FormSectionCreateProps } from '../types'
import {
    TbBox,
    TbPlus,
    TbPackage,
    TbFileText,
    TbBuilding,
    TbHash,
    TbLock,
    TbFiles,
} from 'react-icons/tb'
import { Tooltip } from '@/components/ui/Tooltip'
import classNames from 'classnames'
import { useEffect, useState } from 'react'
import { useGetStocks } from '@/hooks/stock'

const BasicInfoSection = ({
    control,
    errors,
    setValue,
}: FormSectionCreateProps) => {
    const { t } = useTranslation()

    const { data: stocks } = useGetStocks()

    const stockOptions = stocks?.map((stock) => ({
        value: stock.id,
        label: stock.name,
    }))

    const selectedType = useWatch({
        control,
        name: 'type',
    })

    // State for UI selection using unique labels
    const [selectedLabel, setSelectedLabel] = useState('add')

    // Define the three main types that should always appear
    const mainTypes = [
        {
            value: 1,
            label: 'ملفات',
            icon: <TbFiles className="text-3xl" />,
        },
        {
            value: 2,
            label: 'صناديق',
            icon: <TbBox className="text-3xl" />,
        },
        {
            value: 3,
            label: 'أفيز',
            icon: <TbLock className="text-3xl" />,
        },
    ]

    // eslint-disable-next-line react-hooks/exhaustive-deps
    const categoryOptions = [
        ...mainTypes,
        {
            value: -1,
            label: 'add',
            icon: <TbPlus className="text-3xl" />,
        },
    ]

    // Update name and type fields when selectedLabel changes
    useEffect(() => {
        const selectedOption = categoryOptions.find(
            (item) => item.label === selectedLabel,
        )

        if (selectedOption && setValue) {
            if (selectedLabel !== 'add') {
                setValue('name', selectedOption.label)
                setValue('type', selectedOption.value)
            } else {
                setValue('name', '')
                setValue('type', -1)
            }
        }
    }, [selectedLabel, categoryOptions, setValue])

    return (
        <Card className="w-full p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
            {/* Enhanced Header */}
            <div className="flex flex-col justify-between gap-4">
                {/* type */}
                <FormItem
                    className="mb-4"
                    invalid={Boolean(errors.type)}
                    errorMessage={errors.type?.message}
                >
                    <Controller
                        name="type"
                        control={control}
                        defaultValue={-1}
                        render={({ field }) => (
                            <div className="flex flex-wrap items-stretch gap-4">
                                {categoryOptions.map((option) => (
                                    <Tooltip
                                        key={option.label}
                                        title={option.label}
                                        placement="top"
                                    >
                                        <div
                                            className={classNames(
                                                'border-2 rounded-xl py-4 px-6 cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-105',
                                                selectedLabel === option.label
                                                    ? 'border-primary bg-primary/5 shadow-lg scale-105'
                                                    : 'border-gray-200 dark:border-gray-600 hover:border-primary/50',
                                            )}
                                            onClick={() =>
                                                setSelectedLabel(option.label)
                                            }
                                        >
                                            <div className="flex flex-col items-center justify-center h-20">
                                                <div
                                                    className={classNames(
                                                        'p-3 rounded-full transition-all duration-300',
                                                        selectedLabel ===
                                                            option.label
                                                            ? 'text-primary bg-primary/10 scale-110 shadow-md'
                                                            : 'text-gray-500 bg-gray-100 dark:bg-gray-700 hover:scale-105 hover:shadow-md',
                                                    )}
                                                >
                                                    {option.icon}
                                                </div>
                                            </div>
                                        </div>
                                    </Tooltip>
                                ))}
                            </div>
                        )}
                    />
                </FormItem>

                {/* name */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <FormItem
                        className="mb-4"
                        label={t('nav.shared.name')}
                        invalid={Boolean(errors.name)}
                        errorMessage={errors.name?.message}
                    >
                        <Controller
                            name="name"
                            control={control}
                            render={({ field }) => (
                                <div className="relative">
                                    <TbPackage className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                                    <Input
                                        type="text"
                                        autoComplete="off"
                                        placeholder={t('nav.shared.enterName')}
                                        disabled={selectedLabel !== 'add'}
                                        className="pl-10"
                                        {...field}
                                    />
                                </div>
                            )}
                        />
                    </FormItem>

                    <FormItem
                        className="mb-4"
                        label={t('nav.shared.description')}
                        invalid={Boolean(errors.description)}
                        errorMessage={errors.description?.message}
                    >
                        <Controller
                            name="description"
                            control={control}
                            render={({ field }) => (
                                <div className="relative">
                                    <TbFileText className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                                    <Input
                                        type="text"
                                        autoComplete="off"
                                        placeholder={t(
                                            'nav.shared.enterDescription',
                                        )}
                                        className="pl-10"
                                        {...field}
                                    />
                                </div>
                            )}
                        />
                    </FormItem>

                    <div className="flex items-center justify-between gap-4">
                        {selectedType === 3 ? (
                            <>
                                <FormItem
                                    className="mb-4"
                                    label={t('nav.consumables.countRange')}
                                    invalid={Boolean(errors.rangeStart)}
                                    errorMessage={errors.rangeStart?.message}
                                >
                                    <Controller
                                        name="rangeStart"
                                        control={control}
                                        render={({ field }) => (
                                            <Input
                                                type="number"
                                                autoComplete="off"
                                                placeholder="من"
                                                value={field.value || ''}
                                                onChange={(e) =>
                                                    field.onChange(
                                                        Number(e.target.value),
                                                    )
                                                }
                                            />
                                        )}
                                    />
                                </FormItem>
                                <FormItem
                                    className="mb-4"
                                    label={'إلى'}
                                    invalid={Boolean(errors.rangeEnd)}
                                    errorMessage={errors.rangeEnd?.message}
                                >
                                    <Controller
                                        name="rangeEnd"
                                        control={control}
                                        render={({ field }) => (
                                            <Input
                                                type="number"
                                                autoComplete="off"
                                                placeholder="إلى"
                                                value={field.value || ''}
                                                onChange={(e) =>
                                                    field.onChange(
                                                        Number(e.target.value),
                                                    )
                                                }
                                            />
                                        )}
                                    />
                                </FormItem>
                            </>
                        ) : (
                            <FormItem
                                className="mb-4 w-full"
                                label={t('nav.consumables.count')}
                                invalid={Boolean(errors.quantity)}
                                errorMessage={errors.quantity?.message}
                            >
                                <Controller
                                    name="quantity"
                                    control={control}
                                    render={({ field }) => (
                                        <div className="relative">
                                            <TbHash className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                                            <Input
                                                type="number"
                                                autoComplete="off"
                                                placeholder={t(
                                                    'nav.consumables.enterCount',
                                                )}
                                                className="pl-10"
                                                onChange={(e) =>
                                                    field.onChange(
                                                        Number(e.target.value),
                                                    )
                                                }
                                            />
                                        </div>
                                    )}
                                />
                            </FormItem>
                        )}
                    </div>

                    <FormItem
                        className="mb-4"
                        label={t('nav.Stock.stocks')}
                        invalid={Boolean(errors.stockId)}
                        errorMessage={errors.stockId?.message}
                    >
                        <Controller
                            name="stockId"
                            control={control}
                            render={({ field }) => (
                                <div className="relative">
                                    <TbBuilding className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 z-10" />
                                    <Select
                                        options={stockOptions}
                                        placeholder={t('nav.Stock.selectStock')}
                                        className="pl-10"
                                        onChange={(option) =>
                                            field.onChange(
                                                option?.value || null,
                                            )
                                        }
                                    />
                                </div>
                            )}
                        />
                    </FormItem>
                </div>
            </div>
        </Card>
    )
}

export default BasicInfoSection
