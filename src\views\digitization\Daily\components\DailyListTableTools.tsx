import { useState } from 'react'
import Input from '@/components/ui/Input'
import Button from '@/components/ui/Button'
import useTranslation from '@/utils/hooks/useTranslation'
import RangeSelectionModal from './RangeSelectionModal'
import { TbSearch, TbPrinter, TbFolderX, TbTrash } from 'react-icons/tb'

interface DailyListTableToolsProps {
    selectedCount?: number
    onPrintBarcodes?: (fromFile: number, toFile: number) => void
    onCloseFiles?: (fromFile: number, toFile: number) => void
    onDeleteFiles?: (fromFile: number, toFile: number) => void
}

const DailyListTableTools = ({
    selectedCount = 0,
    onPrintBarcodes,
    onCloseFiles,
    onDeleteFiles,
}: DailyListTableToolsProps) => {
    const { t } = useTranslation()
    const [searchTerm, setSearchTerm] = useState('')
    const [isRangeModalOpen, setIsRangeModalOpen] = useState(false)
    const [currentAction, setCurrentAction] = useState<
        'print' | 'close' | 'delete' | null
    >(null)

    const handleSearch = (value: string) => {
        setSearchTerm(value)
        // Implement search logic here
        console.log('Search:', value)
    }

    const handleActionClick = (action: 'print' | 'close' | 'delete') => {
        setCurrentAction(action)
        setIsRangeModalOpen(true)
    }

    const handleRangeSubmit = (fromFile: number, toFile: number) => {
        switch (currentAction) {
            case 'print':
                onPrintBarcodes?.(fromFile, toFile)
                break
            case 'close':
                onCloseFiles?.(fromFile, toFile)
                break
            case 'delete':
                onDeleteFiles?.(fromFile, toFile)
                break
        }
        setIsRangeModalOpen(false)
        setCurrentAction(null)
    }

    return (
        <div className="space-y-4 flex items-center justify-between gap-2">
            {/* Search and Filter Row */}
            <div className=" flex-1">
                <Input
                    placeholder={t('nav.shared.search')}
                    value={searchTerm}
                    prefix={<TbSearch className="text-lg" />}
                    className="w-full"
                    onChange={(e) => handleSearch(e.target.value)}
                />
            </div>

            {/* Action Buttons Row */}
            {/* <div className="flex flex-wrap items-center gap-3">
                <Button
                    variant="default"
                    size="sm"
                    icon={<TbPrinter />}
                    className="flex items-center gap-2"
                    onClick={() => handleActionClick('print')}
                >
                    {t('nav.dailyDigitization.printBarcode')}
                </Button>
                <Button
                    variant="default"
                    size="sm"
                    icon={<TbFolderX />}
                    className="flex items-center gap-2"
                    onClick={() => handleActionClick('close')}
                >
                    {t('nav.dailyDigitization.closeFiles')}
                </Button>
                <Button
                    variant="default"
                    size="sm"
                    icon={<TbTrash />}
                    className="flex items-center gap-2 text-red-600 hover:text-red-700"
                    onClick={() => handleActionClick('delete')}
                >
                    {t('nav.shared.delete')}
                </Button>
                {selectedCount > 0 && (
                    <span className="text-sm text-gray-600 ml-2">
                        {selectedCount} {t('nav.shared.selected')}
                    </span>
                )}
            </div> */}

            {/* Range Selection Modal */}
            {/* <RangeSelectionModal
                isOpen={isRangeModalOpen}
                title={
                    currentAction === 'print'
                        ? t('nav.dailyDigitization.printBarcode')
                        : currentAction === 'close'
                          ? t('nav.dailyDigitization.closeFiles')
                          : currentAction === 'delete'
                            ? t('nav.shared.delete')
                            : ''
                }
                actionType={currentAction}
                onClose={() => {
                    setIsRangeModalOpen(false)
                    setCurrentAction(null)
                }}
                onSubmit={handleRangeSubmit}
            /> */}
        </div>
    )
}

export default DailyListTableTools
