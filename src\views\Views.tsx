import { Suspense, useEffect, useState } from 'react'
import { Outlet } from 'react-router-dom'
import { USER } from '@/constants/roles.constant'
import Loading from '@/components/shared/Loading'
import BranchSelectionDialog from '@/components/shared/BranchSelectionDialog'
import BranchConfirmationPopup from '@/components/shared/BranchConfirmationPopup'
import type { Branch } from '@/@types/auth'
import ChangePasswordDialog from '@/components/shared/ChangePasswordDialog'
import { useGetAccount } from '@/hooks/auth'

const Views = () => {
    const authenticated = localStorage.getItem('token')
    const [isInitialized, setIsInitialized] = useState(false)

    // Branch selection state
    const [availableBranches, setAvailableBranches] = useState<Branch[]>([])
    // const [selectedBranch, setSelectedBranch] = useState<Branch | null>(null)
    const [showBranchDialog, setShowBranchDialog] = useState(false)
    const [showBranchConfirmation, setShowBranchConfirmation] = useState(false)
    const [confirmationBranch, setConfirmationBranch] = useState<Branch | null>(
        null,
    )

    // Initialize component state
    useEffect(() => {
        setIsInitialized(true)
    }, [])

    // Handle branch fetching and dialog display for USER role
    useEffect(() => {
        if (authenticated && isInitialized) {
            const { data: user } = useGetAccount()
            const branches = user?.organizationalNodes
            setAvailableBranches(branches!)
        }
    }, [authenticated, isInitialized])

    // Get user roles from localStorage
    const userRoles = localStorage.getItem('roles') || []
    console.log('userRoles: ', userRoles)

    const hasUserRole = userRoles.includes(USER as never)

    const shouldShowBranchDialog =
        hasUserRole && showBranchDialog && isInitialized

    // Handle branch selection from dialog
    const handleBranchSelected = (branch: Branch) => {
        setConfirmationBranch(branch)
        setShowBranchDialog(false)
        setShowBranchConfirmation(true)

        localStorage.setItem(
            'selectedOrganization',
            JSON.stringify({
                id: branch.code,
                name: branch.name,
            }),
        )
    }

    // Handle branch confirmation completion
    const handleBranchConfirmationComplete = () => {
        setShowBranchConfirmation(false)
        setConfirmationBranch(null)
    }

    // Handle dialog close
    const handleBranchDialogClose = () => {
        setShowBranchDialog(false)
    }

    return (
        <div className="app-layout-blank flex flex-auto flex-col h-[100vh]">
            <Suspense fallback={<Loading loading={true} />}>
                <Outlet />
            </Suspense>

            {/* Password Change Dialog */}
            <ChangePasswordDialog />

            {/* Branch Selection Dialog */}
            <BranchSelectionDialog
                isOpen={shouldShowBranchDialog}
                availableBranches={availableBranches}
                onClose={handleBranchDialogClose}
                onBranchSelected={handleBranchSelected}
            />

            {/* Branch Confirmation Popup */}
            <BranchConfirmationPopup
                show={showBranchConfirmation}
                branch={confirmationBranch}
                onComplete={handleBranchConfirmationComplete}
            />
        </div>
    )
}

export default Views
