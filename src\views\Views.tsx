import { Suspense, useEffect, useState } from 'react'
import { USER } from '@/constants/roles.constant'
import Loading from '@/components/shared/Loading'
import BranchSelectionDialog from '@/components/shared/BranchSelectionDialog'
import BranchConfirmationPopup from '@/components/shared/BranchConfirmationPopup'
import type { Branch } from '@/@types/auth'
import ChangePasswordDialog from '@/components/shared/ChangePasswordDialog'
import { useGetAccount } from '@/hooks/auth'
import AllRoutes from '@/components/route/AllRoutes'
import { useAuthStore } from '@/views/auth/store/Auth'
import { useNavigate } from 'react-router-dom'
import appConfig from '@/configs/app.config'

const Views = () => {
    const authenticated = localStorage.getItem('token')
    const [isInitialized, setIsInitialized] = useState(false)
    const navigate = useNavigate()
    const { showBranchDialog, setBranchSelected, setActiveOrg, getRoles } =
        useAuthStore()

    // Branch selection state
    const [availableBranches, setAvailableBranches] = useState<Branch[]>([])
    const [showBranchConfirmation, setShowBranchConfirmation] = useState(false)
    const [confirmationBranch, setConfirmationBranch] = useState<Branch | null>(
        null,
    )

    // Initialize component state
    useEffect(() => {
        setIsInitialized(true)
    }, [])

    // Handle branch fetching and dialog display for USER role
    useEffect(() => {
        if (authenticated && isInitialized && showBranchDialog) {
            const { data: user } = useGetAccount()
            const branches = user?.organizationalNodes
            if (branches) {
                setAvailableBranches(branches)
            }
        }
    }, [authenticated, isInitialized, showBranchDialog])

    // Get user roles from localStorage
    const userRoles = getRoles()
    console.log('userRoles: ', userRoles)

    const hasUserRole = userRoles.includes(USER)

    const shouldShowBranchDialog =
        hasUserRole && showBranchDialog && isInitialized

    // Handle branch selection from dialog
    const handleBranchSelected = (branch: Branch) => {
        setConfirmationBranch(branch)
        setBranchSelected() // This will hide the branch dialog
        setShowBranchConfirmation(true)

        // Store the selected organization using setActiveOrg
        setActiveOrg(branch.code)

        // Also store in the old format for compatibility
        localStorage.setItem(
            'selectedOrganization',
            JSON.stringify({
                id: branch.code,
                name: branch.name,
            }),
        )
    }

    // Handle branch confirmation completion
    const handleBranchConfirmationComplete = () => {
        setShowBranchConfirmation(false)
        setConfirmationBranch(null)

        // Navigate to USER authenticated path after branch selection
        navigate(appConfig.userAuthenticatedPath)
    }

    // Handle dialog close
    const handleBranchDialogClose = () => {
        setBranchSelected() // Close the dialog using auth store
    }

    return (
        <div className="app-layout-blank flex flex-auto flex-col h-[100vh]">
            <Suspense fallback={<Loading loading={true} />}>
                <AllRoutes />
            </Suspense>

            {/* Password Change Dialog */}
            <ChangePasswordDialog />

            {/* Branch Selection Dialog */}
            <BranchSelectionDialog
                isOpen={shouldShowBranchDialog}
                availableBranches={availableBranches}
                onClose={handleBranchDialogClose}
                onBranchSelected={handleBranchSelected}
            />

            {/* Branch Confirmation Popup */}
            <BranchConfirmationPopup
                show={showBranchConfirmation}
                branch={confirmationBranch}
                onComplete={handleBranchConfirmationComplete}
            />
        </div>
    )
}

export default Views
