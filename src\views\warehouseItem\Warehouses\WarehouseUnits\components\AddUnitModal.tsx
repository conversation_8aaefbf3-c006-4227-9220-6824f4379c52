import { useState } from 'react'
import { useF<PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import Dialog from '@/components/ui/Dialog'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import { FormItem, Form } from '@/components/ui/Form'
import useTranslation from '@/utils/hooks/useTranslation'
import { TbX, Tb<PERSON>heck, TbBox } from 'react-icons/tb'

interface AddUnitModalProps {
    isOpen: boolean
    onClose: () => void
    onSubmit: (data: UnitFormData) => void
}

interface UnitFormData {
    numberOfUnits: number
    unitLength: number
    unitWidth: number
    numberOfShelvesPerUnit: number
    shelfLength: number
    shelfWidth: number
    numberOfBoxesPerShelf: number
}

const AddUnitModal = ({ isOpen, onClose, onSubmit }: AddUnitModalProps) => {
    const { t } = useTranslation()
    const [loading, setLoading] = useState(false)

    const unitSchema = z.object({
        numberOfUnits: z
            .number()
            .min(1, { message: t('nav.warehouses.numberOfUnitsRequired') }),
        unitLength: z
            .number()
            .min(0.1, { message: t('nav.warehouses.unitLengthRequired') }),
        unitWidth: z
            .number()
            .min(0.1, { message: t('nav.warehouses.unitWidthRequired') }),
        numberOfShelvesPerUnit: z
            .number()
            .min(1, {
                message: t('nav.warehouses.numberOfShelvesPerUnitRequired'),
            }),
        shelfLength: z
            .number()
            .min(0.1, { message: t('nav.warehouses.shelfLengthRequired') }),
        shelfWidth: z
            .number()
            .min(0.1, { message: t('nav.warehouses.shelfWidthRequired') }),
        numberOfBoxesPerShelf: z
            .number()
            .min(1, {
                message: t('nav.warehouses.numberOfBoxesPerShelfRequired'),
            }),
    })

    const {
        handleSubmit,
        control,
        reset,
        formState: { errors },
    } = useForm<UnitFormData>({
        defaultValues: {
            numberOfUnits: 0,
            unitLength: 0,
            unitWidth: 0,
            numberOfShelvesPerUnit: 0,
            shelfLength: 0,
            shelfWidth: 0,
            numberOfBoxesPerShelf: 0,
        },
        resolver: zodResolver(unitSchema),
    })

    const handleFormSubmit = async (data: UnitFormData) => {
        setLoading(true)
        try {
            // Simulate API call
            await new Promise((resolve) => setTimeout(resolve, 1000))
            onSubmit(data)
            reset()
        } catch (error) {
            console.error('Error creating unit:', error)
        } finally {
            setLoading(false)
        }
    }

    const handleClose = () => {
        reset()
        onClose()
    }

    return (
        <Dialog
            isOpen={isOpen}
            width={800}
            height={550}
            onClose={handleClose}
            onRequestClose={handleClose}
        >
            <div className="flex flex-col h-full max-w-4xl mx-auto">
                {/* Header */}
                <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                    <div className="flex items-center gap-3">
                        <div className="flex items-center justify-center w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                            <TbBox className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                        </div>
                        <div>
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                {t('nav.warehouses.configureUnits')}
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                                {t('nav.warehouses.configureUnitsDesc')}
                            </p>
                        </div>
                    </div>
                    <Button
                        variant="plain"
                        size="sm"
                        icon={<TbX />}
                        className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                        onClick={handleClose}
                    />
                </div>

                {/* Form */}
                <div className="flex-1 overflow-y-auto p-6">
                    <Form onSubmit={handleSubmit(handleFormSubmit)}>
                        <div className="space-y-6">
                            {/* Units Section */}
                            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                                <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-4">
                                    {t('nav.warehouses.unitsConfiguration')}
                                </h4>
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <FormItem
                                        label={`${t('nav.warehouses.numberOfUnits')} *`}
                                        invalid={!!errors.numberOfUnits}
                                        errorMessage={
                                            errors.numberOfUnits?.message
                                        }
                                    >
                                        <Controller
                                            name="numberOfUnits"
                                            control={control}
                                            render={({ field }) => (
                                                <Input
                                                    {...field}
                                                    type="number"
                                                    min="1"
                                                    placeholder="0"
                                                    onChange={(e) =>
                                                        field.onChange(
                                                            parseInt(
                                                                e.target.value,
                                                            ) || 0,
                                                        )
                                                    }
                                                />
                                            )}
                                        />
                                    </FormItem>
                                    <FormItem
                                        label={`${t('nav.warehouses.unitLength')} *`}
                                        invalid={!!errors.unitLength}
                                        errorMessage={
                                            errors.unitLength?.message
                                        }
                                    >
                                        <Controller
                                            name="unitLength"
                                            control={control}
                                            render={({ field }) => (
                                                <Input
                                                    {...field}
                                                    type="number"
                                                    min="0.1"
                                                    step="0.1"
                                                    placeholder="0.0"
                                                    onChange={(e) =>
                                                        field.onChange(
                                                            parseFloat(
                                                                e.target.value,
                                                            ) || 0,
                                                        )
                                                    }
                                                />
                                            )}
                                        />
                                    </FormItem>
                                    <FormItem
                                        label={`${t('nav.warehouses.unitWidth')} *`}
                                        invalid={!!errors.unitWidth}
                                        errorMessage={errors.unitWidth?.message}
                                    >
                                        <Controller
                                            name="unitWidth"
                                            control={control}
                                            render={({ field }) => (
                                                <Input
                                                    {...field}
                                                    type="number"
                                                    min="0.1"
                                                    step="0.1"
                                                    placeholder="0.0"
                                                    onChange={(e) =>
                                                        field.onChange(
                                                            parseFloat(
                                                                e.target.value,
                                                            ) || 0,
                                                        )
                                                    }
                                                />
                                            )}
                                        />
                                    </FormItem>
                                </div>
                            </div>

                            {/* Shelves Section */}
                            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                                <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-4">
                                    {t('nav.warehouses.shelvesConfiguration')}
                                </h4>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <FormItem
                                        label={`${t('nav.warehouses.numberOfShelvesPerUnit')} *`}
                                        invalid={
                                            !!errors.numberOfShelvesPerUnit
                                        }
                                        errorMessage={
                                            errors.numberOfShelvesPerUnit
                                                ?.message
                                        }
                                    >
                                        <Controller
                                            name="numberOfShelvesPerUnit"
                                            control={control}
                                            render={({ field }) => (
                                                <Input
                                                    {...field}
                                                    type="number"
                                                    min="1"
                                                    placeholder="0"
                                                    onChange={(e) =>
                                                        field.onChange(
                                                            parseInt(
                                                                e.target.value,
                                                            ) || 0,
                                                        )
                                                    }
                                                />
                                            )}
                                        />
                                    </FormItem>
                                    <FormItem
                                        label={`${t('nav.warehouses.numberOfBoxesPerShelf')} *`}
                                        invalid={!!errors.numberOfBoxesPerShelf}
                                        errorMessage={
                                            errors.numberOfBoxesPerShelf
                                                ?.message
                                        }
                                    >
                                        <Controller
                                            name="numberOfBoxesPerShelf"
                                            control={control}
                                            render={({ field }) => (
                                                <Input
                                                    {...field}
                                                    type="number"
                                                    min="1"
                                                    placeholder="0"
                                                    onChange={(e) =>
                                                        field.onChange(
                                                            parseInt(
                                                                e.target.value,
                                                            ) || 0,
                                                        )
                                                    }
                                                />
                                            )}
                                        />
                                    </FormItem>
                                    <FormItem
                                        label={`${t('nav.warehouses.shelfLength')} *`}
                                        invalid={!!errors.shelfLength}
                                        errorMessage={
                                            errors.shelfLength?.message
                                        }
                                    >
                                        <Controller
                                            name="shelfLength"
                                            control={control}
                                            render={({ field }) => (
                                                <Input
                                                    {...field}
                                                    type="number"
                                                    min="0.1"
                                                    step="0.1"
                                                    placeholder="0.0"
                                                    onChange={(e) =>
                                                        field.onChange(
                                                            parseFloat(
                                                                e.target.value,
                                                            ) || 0,
                                                        )
                                                    }
                                                />
                                            )}
                                        />
                                    </FormItem>
                                    <FormItem
                                        label={`${t('nav.warehouses.shelfWidth')} *`}
                                        invalid={!!errors.shelfWidth}
                                        errorMessage={
                                            errors.shelfWidth?.message
                                        }
                                    >
                                        <Controller
                                            name="shelfWidth"
                                            control={control}
                                            render={({ field }) => (
                                                <Input
                                                    {...field}
                                                    type="number"
                                                    min="0.1"
                                                    step="0.1"
                                                    placeholder="0.0"
                                                    onChange={(e) =>
                                                        field.onChange(
                                                            parseFloat(
                                                                e.target.value,
                                                            ) || 0,
                                                        )
                                                    }
                                                />
                                            )}
                                        />
                                    </FormItem>
                                </div>
                            </div>
                        </div>
                    </Form>
                </div>

                {/* Action Buttons - Fixed at bottom */}
                <div className="flex justify-end gap-3 p-6 pt-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
                    <Button
                        type="button"
                        variant="plain"
                        disabled={loading}
                        className="min-w-[100px]"
                        onClick={handleClose}
                    >
                        {t('nav.shared.cancel')}
                    </Button>
                    <Button
                        type="submit"
                        variant="solid"
                        icon={<TbCheck />}
                        loading={loading}
                        className="min-w-[100px] bg-blue-600 hover:bg-blue-700"
                        onClick={handleSubmit(handleFormSubmit)}
                    >
                        {t('nav.shared.create')}
                    </Button>
                </div>
            </div>
        </Dialog>
    )
}

export default AddUnitModal
