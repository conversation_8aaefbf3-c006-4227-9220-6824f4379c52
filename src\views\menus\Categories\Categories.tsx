import { useState } from 'react'
import { Container, AdaptiveCard } from '@/components/shared'
import CategoryHeader from './components/CategoryHeader'
import CategoryActionTools from './components/CategoryActionTools'
import CategoriesTable from './components/CategoriesTable'
import CategoryDialog from './components/CategoryDialog'

const Categories = () => {
    const [dialogIsOpen, setDialogIsOpen] = useState(false)
    const [editingCategoryId, setEditingCategoryId] = useState<number | null>(
        null,
    )
    const [visibleColumns, setVisibleColumns] = useState([
        'id',
        'name',
        'status',
        'actions',
    ])

    const handleAddCategory = () => {
        setEditingCategoryId(null)
        setDialogIsOpen(true)
    }

    const handleEditCategory = (id: number) => {
        setEditingCategoryId(id)
        setDialogIsOpen(true)
    }

    const handleCloseDialog = () => {
        setDialogIsOpen(false)
        setEditingCategoryId(null)
    }

    const handleColumnVisibilityChange = (columns: string[]) => {
        setVisibleColumns(columns)
    }

    return (
        <>
            <Container>
                <AdaptiveCard>
                    <div className="flex flex-col gap-4">
                        <CategoryHeader onAddCategory={handleAddCategory} />

                        <CategoryActionTools
                            onColumnVisibilityChange={
                                handleColumnVisibilityChange
                            }
                        />

                        <div className="flex flex-col gap-4">
                            <CategoriesTable
                                visibleColumns={visibleColumns}
                                onEdit={handleEditCategory}
                            />
                        </div>

                        <CategoryDialog
                            isOpen={dialogIsOpen}
                            categoryId={editingCategoryId}
                            onClose={handleCloseDialog}
                        />
                    </div>
                </AdaptiveCard>
            </Container>
            {/* there selected rows here */}
        </>
    )
}

export default Categories
