import type { NavigationTree } from '@/@types/navigation'
import menusNavigationConfig from './menus.navigation.config'
import systemSecurityNavigationConfig from './systemSecurity.navigation.config'
import warehouseNavigationConfig from './warehouseItems.navigation.config'
import digitizationNavigationConfig from './digitization.navigation.config'
import boxesNavigationConfig from './boxes.navigation.config'

const navigationConfig: NavigationTree[] = [
    ...menusNavigationConfig,
    ...systemSecurityNavigationConfig,
    ...warehouseNavigationConfig,
    ...digitizationNavigationConfig,
    ...boxesNavigationConfig,
]

export default navigationConfig
