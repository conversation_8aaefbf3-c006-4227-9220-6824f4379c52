import { useState } from 'react'
import Button from '@/components/ui/Button'
import Drawer from '@/components/ui/Drawer'
import Checkbox from '@/components/ui/Checkbox'
import Select from '@/components/ui/Select'
import { Form, FormItem } from '@/components/ui/Form'
import { TbFilter } from 'react-icons/tb'
import { useForm, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import type { ZodType } from 'zod'
import useTranslation from '@/utils/hooks/useTranslation'
import { BuildingFilterConfig } from '@/store/selectors/buildingSelector'

type FilterFormSchema = {
    nameFilter?: string
    villageFilter?: string
    cityFilter?: string
    governorateFilter?: string
    countryFilter?: string
    statusFilter?: string
    filterColumns?: string[]
}

const validationSchema: ZodType<FilterFormSchema> = z.object({
    nameFilter: z.string().optional().default(''),
    villageFilter: z.string().optional().default(''),
    cityFilter: z.string().optional().default(''),
    governorateFilter: z.string().optional().default(''),
    countryFilter: z.string().optional().default(''),
    statusFilter: z.string().optional().default(''),
    filterColumns: z.array(z.string()).optional().default([]),
})

const BuildingTableFilter = () => {
    const { t } = useTranslation()

    const defaultColumns = [
        { value: 'name', label: t('nav.shared.name') },
        { value: 'village', label: t('nav.buildings.village') },
        { value: 'city', label: t('nav.buildings.city') },
        { value: 'governorate', label: t('nav.buildings.governorate') },
        { value: 'country', label: t('nav.buildings.country') },
        { value: 'status', label: t('nav.shared.status') },
    ]

    const [filterIsOpen, setFilterIsOpen] = useState(false)

    // Create village options for the select
    const villageOptions = [
        { value: '', label: t('nav.shared.all') },
        ...availableVillages, // Already in {value, label} format from selector
    ]

    // Create city options for the select
    const cityOptions = [
        { value: '', label: t('nav.shared.all') },
        ...availableCities, // Already in {value, label} format from selector
    ]

    // Create governorate options for the select
    const governorateOptions = [
        { value: '', label: t('nav.shared.all') },
        ...availableGovernorates, // Already in {value, label} format from selector
    ]

    // Create status options for the select
    const statusOptions = [
        { value: '', label: t('nav.shared.all') },
        { value: '0', label: t('nav.shared.inactive') },
        { value: '1', label: t('nav.shared.active') },
    ]

    // Check if any filters are active
    // const hasActiveFilters =
    //     filterConfig &&
    //     ((filterConfig.names && filterConfig.names.length > 0) ||
    //         (filterConfig.villages && filterConfig.villages.length > 0) ||
    //         (filterConfig.cities && filterConfig.cities.length > 0) ||
    //         (filterConfig.governorates &&
    //             filterConfig.governorates.length > 0) ||
    //         (filterConfig.countries && filterConfig.countries.length > 0) ||
    //         filterConfig.status !== undefined)

    const {
        handleSubmit,
        control,
        reset,
        formState: { errors },
    } = useForm<FilterFormSchema>({
        defaultValues: {
            nameFilter: '',
            villageFilter: '',
            cityFilter: '',
            governorateFilter: '',
            countryFilter: '', // Added missing field
            statusFilter: '',
            filterColumns: [
                'name',
                'village',
                'city',
                'governorate',
                'country',
                'status',
                'action',
            ],
        },
        resolver: zodResolver(validationSchema),
    })

    const onSubmit = (values: FilterFormSchema) => {
        console.log('🔥 onSubmit called with values:', values)
        console.log('🔥 Form errors:', errors)

        // Build comprehensive filter config
        const newFilterConfig: Partial<BuildingFilterConfig> = {}

        // Apply name filter
        if (values.nameFilter && values.nameFilter !== t('nav.shared.all')) {
            newFilterConfig.names = [values.nameFilter]
        }

        // Apply village filter
        if (
            values.villageFilter &&
            values.villageFilter !== t('nav.shared.all')
        ) {
            newFilterConfig.villages = [values.villageFilter]
        }

        // Apply city filter
        if (values.cityFilter && values.cityFilter !== t('nav.shared.all')) {
            newFilterConfig.cities = [values.cityFilter]
        }

        // Apply governorate filter
        if (
            values.governorateFilter &&
            values.governorateFilter !== t('nav.shared.all')
        ) {
            newFilterConfig.governorates = [values.governorateFilter]
        }

        // Apply country filter
        if (
            values.countryFilter &&
            values.countryFilter !== t('nav.shared.all')
        ) {
            newFilterConfig.countries = [values.countryFilter]
        }

        // Apply status filter
        if (values.statusFilter) {
            newFilterConfig.status = parseInt(values.statusFilter)
        }

        // Apply all filters at once using updateFilterConfig
        handleFilterChange(newFilterConfig)

        // Apply column filter
        if (values.filterColumns) {
            setColumnsAction(values.filterColumns)
        }

        setFilterIsOpen(false)
    }

    const handleClearFilters = () => {
        // Clear all filters using updateFilterConfig
        handleFilterChange({
            names: [],
            villages: [],
            cities: [],
            governorates: [],
            countries: [],
            status: undefined,
        })

        // Reset form values
        reset({
            nameFilter: '',
            villageFilter: '',
            cityFilter: '',
            governorateFilter: '',
            statusFilter: '',
            filterColumns: [
                'name',
                'village',
                'city',
                'governorate',
                'country',
                'status',
                'action',
            ],
        })

        // Reset columns
        setColumnsAction(defaultColumns.map((column) => column.value))
    }

    const handleClose = () => {
        setFilterIsOpen(false)
    }

    return (
        <>
            <Button
                icon={<TbFilter />}
                variant={'default'}
                onClick={() => setFilterIsOpen(true)}
            >
                {t('nav.shared.filter')}
            </Button>

            <Drawer
                title={t('nav.shared.filter')}
                isOpen={filterIsOpen}
                footer={
                    <div className="flex justify-around items-center w-full ">
                        <Button
                            className=""
                            variant="plain"
                            onClick={handleClearFilters}
                        >
                            {t('nav.shared.clear')}
                        </Button>
                        <Button
                            variant="solid"
                            onClick={handleSubmit(onSubmit)}
                        >
                            {t('nav.shared.confirm')}
                        </Button>
                    </div>
                }
                width={400}
                onClose={handleClose}
                onRequestClose={handleClose}
            >
                <Form
                    className="h-full"
                    containerClassName="flex flex-col justify-between h-full"
                    onSubmit={handleSubmit(onSubmit)}
                >
                    <div className="space-y-4">
                        {/* Village Filter */}
                        <FormItem label={t('nav.buildings.filterByVillage')}>
                            <Controller
                                name="villageFilter"
                                control={control}
                                render={({ field }) => (
                                    <Select
                                        options={villageOptions}
                                        placeholder={t(
                                            'nav.buildings.selectVillage',
                                        )}
                                        value={villageOptions.find(
                                            (option) =>
                                                option.value === field.value,
                                        )}
                                        onChange={(option) =>
                                            field.onChange(option?.value || '')
                                        }
                                    />
                                )}
                            />
                        </FormItem>

                        {/* City Filter */}
                        <FormItem label={t('nav.buildings.filterByCity')}>
                            <Controller
                                name="cityFilter"
                                control={control}
                                render={({ field }) => (
                                    <Select
                                        options={cityOptions}
                                        placeholder={t(
                                            'nav.buildings.selectCity',
                                        )}
                                        value={cityOptions.find(
                                            (option) =>
                                                option.value === field.value,
                                        )}
                                        onChange={(option) =>
                                            field.onChange(option?.value || '')
                                        }
                                    />
                                )}
                            />
                        </FormItem>

                        {/* Governorate Filter */}
                        <FormItem
                            label={t('nav.buildings.filterByGovernorate')}
                        >
                            <Controller
                                name="governorateFilter"
                                control={control}
                                render={({ field }) => (
                                    <Select
                                        options={governorateOptions}
                                        placeholder={t(
                                            'nav.buildings.selectGovernorate',
                                        )}
                                        value={governorateOptions.find(
                                            (option) =>
                                                option.value === field.value,
                                        )}
                                        onChange={(option) =>
                                            field.onChange(option?.value || '')
                                        }
                                    />
                                )}
                            />
                        </FormItem>

                        {/* Status Filter */}
                        <FormItem label={t('nav.shared.status')}>
                            <Controller
                                name="statusFilter"
                                control={control}
                                render={({ field }) => (
                                    <Select
                                        options={statusOptions}
                                        placeholder={t(
                                            'nav.shared.selectStatus',
                                        )}
                                        value={statusOptions.find(
                                            (option) =>
                                                option.value === field.value,
                                        )}
                                        onChange={(option) =>
                                            field.onChange(option?.value || '')
                                        }
                                    />
                                )}
                            />
                        </FormItem>

                        {/* Column Visibility */}
                        <FormItem label={t('nav.shared.customizeColumns')}>
                            <div className="mt-4 space-y-2">
                                <Controller
                                    name="filterColumns"
                                    control={control}
                                    render={({ field }) => (
                                        <Checkbox.Group
                                            className="grid grid-cols-2 gap-8"
                                            value={field.value}
                                            onChange={field.onChange}
                                        >
                                            {defaultColumns.map((column) => (
                                                <Checkbox
                                                    key={column.value}
                                                    name={field.name}
                                                    value={column.value}
                                                    className="justify-between flex-row-reverse heading-text w-full"
                                                >
                                                    {column.label}
                                                </Checkbox>
                                            ))}
                                        </Checkbox.Group>
                                    )}
                                />
                            </div>
                        </FormItem>
                    </div>
                </Form>
            </Drawer>
        </>
    )
}

export default BuildingTableFilter
