import Container from '@/components/shared/Container'
import AdaptiveCard from '@/components/shared/AdaptiveCard'
import WarehouseListActionTools from './components/WarehouseListActionTools'
import WarehouseListTable from './components/WarehouseListTable'
import useTranslation from '@/utils/hooks/useTranslation'
import { TbBuilding } from 'react-icons/tb'
import { useGetWarehouses } from '@/hooks/warehouses'

const Warehouses = () => {
    const { t } = useTranslation()

    const { data: warehouses } = useGetWarehouses()

    return (
        <>
            <Container>
                <AdaptiveCard className="shadow-lg">
                    <div className="flex flex-col gap-6">
                        {/* Enhanced Header Section */}
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 pb-4 border-b border-gray-200">
                            <div className="flex items-center gap-3">
                                <div className="flex items-center justify-center w-10 h-10 bg-primary-subtle rounded-lg">
                                    <TbBuilding className="w-5 h-5 text-primary-deep" />
                                </div>
                                <div>
                                    <h3 className=" mb-1">
                                        {t('nav.menus.warehouses')}
                                    </h3>
                                    <p className="text-sm text-gray-400">
                                        {warehouses?.length}{' '}
                                        {warehouses?.length === 1
                                            ? t('nav.warehouses.warehouse')
                                            : t(
                                                  'nav.warehouses.warehouses',
                                              )}{' '}
                                        {/* {t('nav.shared.total')} */}
                                    </p>
                                </div>
                            </div>
                            <WarehouseListActionTools />
                        </div>

                        {/* Table Tools Section */}
                        <div className="">
                            {/* <WarehouseListTableTools /> */}
                        </div>

                        {/* Table Section */}
                        <div className="">
                            <WarehouseListTable />
                        </div>
                    </div>
                </AdaptiveCard>
            </Container>

            {/* Selection Footer */}
            {/* <WarehouseSelectionFooter
                selectedCount={0}
                onDeleteSelected={() => {}}

                onClearSelection={() => {}}

            /> */}
        </>
    )
}

export default Warehouses
