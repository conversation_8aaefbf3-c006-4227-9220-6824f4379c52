import { useState } from 'react'
import Dialog from '@/components/ui/Dialog'
import DailyForm from './DailyForm'
import type { FileForm } from '@/@types/file'
import { TbPlus } from 'react-icons/tb'
import { useGetMyDigitizationStations } from '@/hooks/digitization-station'
import { useCreateFile } from '@/hooks/files'

interface DailyModalProps {
    isOpen: boolean
    onSuccess: () => void
    onClose: () => void
    fileId?: string
}

const DailyModal = ({ isOpen, onSuccess, onClose }: DailyModalProps) => {
    const { data: myStation = [], isLoading } = useGetMyDigitizationStations()
    const { mutate: createFile } = useCreateFile()
    const [isSubmitting, setIsSubmitting] = useState(false)

    const handleSubmit = async (formData: FileForm) => {
        const dataSend: FileForm = {
            ...formData,
            digitizationStationId: myStation[0].id || 0,
            organizationalNodeId: localStorage.getItem('org_code') || '',
            digitizationType: 2,
        }
        setIsSubmitting(true)
        try {
            await createFile(dataSend)
            onSuccess()
        } catch (error) {
            console.error(`Failed to add file:`, error)
        } finally {
            setIsSubmitting(false)
        }
    }

    const handleClose = () => {
        if (!isSubmitting) {
            onClose()
        }
    }

    return (
        <Dialog
            isOpen={isOpen}
            onClose={handleClose}
            onRequestClose={handleClose}
        >
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
                <div className="flex items-center gap-2">
                    <TbPlus className="text-lg" />
                </div>
            </div>

            <div className="p-4">
                <DailyForm
                    isSubmitting={isSubmitting}
                    isLoading={isLoading}
                    onSubmit={handleSubmit}
                    onCancel={handleClose}
                />
            </div>
        </Dialog>
    )
}

export default DailyModal
