import { lazy } from 'react'
import { DA<PERSON>Y_DIGITIZATION_PREFIX_PATH } from '@/constants/route.constant'
import type { Routes } from '@/@types/routes'
import { ADMIN, USER } from '@/constants/roles.constant'

const digitizationRoute: Routes = [
    {
        key: 'digitization.station',
        path: `${DAILY_DIGITIZATION_PREFIX_PATH}/station`,
        component: lazy(
            () => import('@/views/digitization/DigitizationStations'),
        ),
        authority: [ADMIN],
        meta: {
            pageContainerType: 'default',
        },
    },
    {
        key: 'digitization.daily',
        path: `${DAILY_DIGITIZATION_PREFIX_PATH}/daily`,
        component: lazy(() => import('@/views/digitization/Daily')),
        authority: [USER],
        meta: {
            pageContainerType: 'default',
        },
    },
    {
        key: 'digitization.documents',
        path: `${DAILY_DIGITIZATION_PREFIX_PATH}/:fileId/documents`,
        component: lazy(() => import('@/views/digitization/Documents')),
        authority: [USER],
        meta: {
            pageContainerType: 'default',
        },
    },
    {
        key: 'digitization.docsReview',
        path: `${DAILY_DIGITIZATION_PREFIX_PATH}/:fileId/docs-review/:documentId`,
        component: lazy(() => import('@/views/digitization/DocsReview')),
        authority: [USER],
        meta: {
            pageContainerType: 'default',
        },
    },
]

export default digitizationRoute
