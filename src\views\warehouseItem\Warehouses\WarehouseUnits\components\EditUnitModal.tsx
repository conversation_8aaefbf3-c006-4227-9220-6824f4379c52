import { useState, useEffect } from 'react'
import { use<PERSON><PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import Dialog from '@/components/ui/Dialog'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import { FormItem, Form } from '@/components/ui/Form'
import useTranslation from '@/utils/hooks/useTranslation'
import { TbCheck, TbBox } from 'react-icons/tb'

interface EditUnitModalProps {
    isOpen: boolean
    onClose: () => void
    onSubmit: (data: EditUnitFormData) => void
    initialData?: EditUnitFormData | null
}

interface EditUnitFormData {
    length: number
    width: number
}

const EditUnitModal = ({
    isOpen,
    onClose,
    onSubmit,
    initialData,
}: EditUnitModalProps) => {
    const { t } = useTranslation()
    const [loading, setLoading] = useState(false)

    const editUnitSchema = z.object({
        length: z
            .number()
            .min(0.1, { message: t('nav.warehouses.unitLengthRequired') }),
        width: z
            .number()
            .min(0.1, { message: t('nav.warehouses.unitWidthRequired') }),
    })

    const {
        handleSubmit,
        control,
        reset,
        formState: { errors },
    } = useForm<EditUnitFormData>({
        defaultValues: {
            length: 0,
            width: 0,
        },
        resolver: zodResolver(editUnitSchema),
    })

    // Reset form when initialData changes
    useEffect(() => {
        if (initialData) {
            reset({
                length: initialData.length,
                width: initialData.width,
            })
        }
    }, [initialData, reset])

    const handleFormSubmit = async (data: EditUnitFormData) => {
        setLoading(true)
        try {
            // Simulate API call
            await new Promise((resolve) => setTimeout(resolve, 1000))
            onSubmit(data)
            reset()
        } catch (error) {
            console.error('Error updating unit:', error)
        } finally {
            setLoading(false)
        }
    }

    const handleClose = () => {
        reset()
        onClose()
    }

    return (
        <Dialog
            isOpen={isOpen}
            width={600}
            height={400}
            onClose={handleClose}
            onRequestClose={handleClose}
        >
            <div className="flex flex-col h-full max-w-4xl mx-auto">
                {/* Header */}
                <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                    <div className="flex items-center gap-3">
                        <div className="flex items-center justify-center w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                            <TbBox className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                        </div>
                        <div>
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                {t('nav.warehouses.editUnit')}
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                                {t('nav.warehouses.editUnitDescription')}
                            </p>
                        </div>
                    </div>
                </div>

                {/* Form */}
                <div className="flex-1 overflow-y-auto p-6">
                    <Form onSubmit={handleSubmit(handleFormSubmit)}>
                        <div className="space-y-6">
                            {/* Unit Dimensions Section */}
                            <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                                <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-4">
                                    {t('nav.warehouses.unitDimensions')}
                                </h4>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    {/* Unit Length */}
                                    <FormItem
                                        label={`${t('nav.warehouses.unitLength')} *`}
                                        invalid={!!errors.length}
                                        errorMessage={errors.length?.message}
                                    >
                                        <Controller
                                            name="length"
                                            control={control}
                                            render={({ field }) => (
                                                <Input
                                                    {...field}
                                                    type="number"
                                                    min="0.1"
                                                    step="0.1"
                                                    placeholder="0.0"
                                                    onChange={(e) =>
                                                        field.onChange(
                                                            parseFloat(
                                                                e.target.value,
                                                            ) || 0,
                                                        )
                                                    }
                                                />
                                            )}
                                        />
                                    </FormItem>
                                    {/* Unit Width */}
                                    <FormItem
                                        label={`${t('nav.warehouses.unitWidth')} *`}
                                        invalid={!!errors.width}
                                        errorMessage={errors.width?.message}
                                    >
                                        <Controller
                                            name="width"
                                            control={control}
                                            render={({ field }) => (
                                                <Input
                                                    {...field}
                                                    type="number"
                                                    min="0.1"
                                                    step="0.1"
                                                    placeholder="0.0"
                                                    onChange={(e) =>
                                                        field.onChange(
                                                            parseFloat(
                                                                e.target.value,
                                                            ) || 0,
                                                        )
                                                    }
                                                />
                                            )}
                                        />
                                    </FormItem>
                                </div>
                            </div>
                        </div>
                    </Form>
                </div>

                {/* Action Buttons - Fixed at bottom */}
                <div className="flex justify-end gap-3 p-6 pt-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
                    <Button
                        type="button"
                        variant="plain"
                        onClick={handleClose}
                        disabled={loading}
                        className="min-w-[100px]"
                    >
                        {t('nav.shared.cancel')}
                    </Button>
                    <Button
                        type="submit"
                        variant="solid"
                        icon={<TbCheck />}
                        loading={loading}
                        className="min-w-[100px] bg-blue-600 hover:bg-blue-700"
                        onClick={handleSubmit(handleFormSubmit)}
                    >
                        {t('nav.shared.update')}
                    </Button>
                </div>
            </div>
        </Dialog>
    )
}

export default EditUnitModal
