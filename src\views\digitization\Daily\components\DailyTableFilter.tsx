import { useState } from 'react'
import { useF<PERSON>, Controller } from 'react-hook-form'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import Select from '@/components/ui/Select'
import DatePicker from '@/components/ui/DatePicker'
import Card from '@/components/ui/Card'
import { FormItem, Form } from '@/components/ui/Form'
import useTranslation from '@/utils/hooks/useTranslation'
import type { DailyFilterConfig } from '../types'
import { TbFilter, TbX, TbRefresh } from 'react-icons/tb'

interface DailyTableFilterProps {
    onClose: () => void
    onApplyFilter: (filters: Partial<DailyFilterConfig>) => void
    onClearFilters: () => void
}

const DailyTableFilter = ({
    onClose,
    onApplyFilter,
    onClearFilters,
}: DailyTableFilterProps) => {
    const { t } = useTranslation()

    const {
        handleSubmit,
        control,
        reset,
        formState: { isDirty },
    } = useForm<DailyFilterConfig>({
        defaultValues: {
            fileTitle: '',
            confidentiality: '',
            digitizationStationName: '',
            categoryName: '',
            organizationalNodeName: '',
            digitizationType: '',
            fileStatus: '',
            mediumType: '',
            createdByUsername: '',
            dateRange: {
                startDate: '',
                endDate: '',
            },
        },
    })

    const confidentialityOptions = [
        { value: '', label: t('nav.shared.all') },
        { value: 1, label: t('nav.File.confidentiality.public') },
        { value: 2, label: t('nav.File.confidentiality.confidential') },
        { value: 3, label: t('nav.File.confidentiality.secret') },
        { value: 4, label: t('nav.File.confidentiality.topSecret') },
    ]

    const digitizationTypeOptions = [
        { value: '', label: t('nav.shared.all') },
        { value: 1, label: t('nav.File.digitizationType.scan') },
        { value: 2, label: t('nav.File.digitizationType.photo') },
        { value: 3, label: t('nav.File.digitizationType.import') },
    ]

    const fileStatusOptions = [
        { value: '', label: t('nav.shared.all') },
        { value: 1, label: t('nav.shared.active') },
        { value: 2, label: t('nav.shared.pending') },
        { value: 3, label: t('nav.shared.inactive') },
    ]

    const mediumTypeOptions = [
        { value: '', label: t('nav.shared.all') },
        { value: 1, label: t('nav.File.mediumType.paper') },
        { value: 2, label: t('nav.File.mediumType.electronic') },
        { value: 3, label: t('nav.File.mediumType.mixed') },
    ]

    const onSubmit = (data: DailyFilterConfig) => {
        onApplyFilter(data)
    }

    const handleClearFilters = () => {
        reset()
        onClearFilters()
    }

    return (
        <div className="h-full flex flex-col">
            <div className="flex-1 overflow-y-auto p-4">
                <Form onSubmit={handleSubmit(onSubmit)}>
                    <div className="space-y-4">
                        <FormItem label={t('nav.File.fileTitle')}>
                            <Controller
                                name="fileTitle"
                                control={control}
                                render={({ field }) => (
                                    <Input
                                        {...field}
                                        placeholder={t('nav.File.fileTitle')}
                                    />
                                )}
                            />
                        </FormItem>

                        <FormItem label={t('nav.File.confidentiality')}>
                            <Controller
                                name="confidentiality"
                                control={control}
                                render={({ field }) => (
                                    <Select
                                        {...field}
                                        options={confidentialityOptions}
                                        placeholder={t(
                                            'nav.shared.selectOption',
                                        )}
                                    />
                                )}
                            />
                        </FormItem>

                        <FormItem label={t('nav.File.digitizationStation')}>
                            <Controller
                                name="digitizationStationName"
                                control={control}
                                render={({ field }) => (
                                    <Input
                                        {...field}
                                        placeholder={t(
                                            'nav.File.digitizationStation',
                                        )}
                                    />
                                )}
                            />
                        </FormItem>

                        <FormItem label={t('nav.File.category')}>
                            <Controller
                                name="categoryName"
                                control={control}
                                render={({ field }) => (
                                    <Input
                                        {...field}
                                        placeholder={t('nav.File.category')}
                                    />
                                )}
                            />
                        </FormItem>

                        <FormItem label={t('nav.File.organizationalNode')}>
                            <Controller
                                name="organizationalNodeName"
                                control={control}
                                render={({ field }) => (
                                    <Input
                                        {...field}
                                        placeholder={t(
                                            'nav.File.organizationalNode',
                                        )}
                                    />
                                )}
                            />
                        </FormItem>

                        <FormItem label={t('nav.File.digitizationType')}>
                            <Controller
                                name="digitizationType"
                                control={control}
                                render={({ field }) => (
                                    <Select
                                        {...field}
                                        options={digitizationTypeOptions}
                                        placeholder={t(
                                            'nav.shared.selectOption',
                                        )}
                                    />
                                )}
                            />
                        </FormItem>

                        <FormItem label={t('nav.File.status')}>
                            <Controller
                                name="fileStatus"
                                control={control}
                                render={({ field }) => (
                                    <Select
                                        {...field}
                                        options={fileStatusOptions}
                                        placeholder={t(
                                            'nav.shared.selectOption',
                                        )}
                                    />
                                )}
                            />
                        </FormItem>

                        <FormItem label={t('nav.File.mediumType')}>
                            <Controller
                                name="mediumType"
                                control={control}
                                render={({ field }) => (
                                    <Select
                                        {...field}
                                        options={mediumTypeOptions}
                                        placeholder={t(
                                            'nav.shared.selectOption',
                                        )}
                                    />
                                )}
                            />
                        </FormItem>

                        <FormItem label={t('nav.File.createdBy')}>
                            <Controller
                                name="createdByUsername"
                                control={control}
                                render={({ field }) => (
                                    <Input
                                        {...field}
                                        placeholder={t('nav.File.createdBy')}
                                    />
                                )}
                            />
                        </FormItem>

                        <FormItem label={t('nav.shared.startDate')}>
                            <Controller
                                name="dateRange.startDate"
                                control={control}
                                render={({ field }) => (
                                    <DatePicker
                                        {...field}
                                        placeholder={t('nav.shared.selectDate')}
                                    />
                                )}
                            />
                        </FormItem>

                        <FormItem label={t('nav.shared.endDate')}>
                            <Controller
                                name="dateRange.endDate"
                                control={control}
                                render={({ field }) => (
                                    <DatePicker
                                        {...field}
                                        placeholder={t('nav.shared.selectDate')}
                                    />
                                )}
                            />
                        </FormItem>
                    </div>
                </Form>
            </div>

            {/* Sticky Footer */}
            <div className="flex items-center justify-end gap-3 p-4 border-t border-gray-200 bg-white">
                <Button
                    type="button"
                    variant="plain"
                    icon={<TbRefresh />}
                    onClick={handleClearFilters}
                    disabled={!isDirty}
                >
                    {t('nav.shared.clearFilters')}
                </Button>
                <Button
                    type="submit"
                    variant="solid"
                    icon={<TbFilter />}
                    onClick={handleSubmit(onSubmit)}
                >
                    {t('nav.shared.applyFilters')}
                </Button>
            </div>
        </div>
    )
}

export default DailyTableFilter
