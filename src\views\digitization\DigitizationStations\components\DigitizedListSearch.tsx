import Input from '@/components/ui/Input'
import useTranslation from '@/utils/hooks/useTranslation'
import { TbSearch, TbX } from 'react-icons/tb'
import { Button } from '@/components/ui'
import { useState } from 'react'

const DigitizedListSearch = () => {
    const { t } = useTranslation()
    const [searchTerm, setSearchTerm] = useState('')

    const handleSearch = (value: string) => {
        setSearchTerm(value)
        // TODO: Implement search functionality
    }

    const clearSearch = () => {
        setSearchTerm('')
        // TODO: Implement clear search functionality
    }

    return (
        <div className="flex items-center gap-2">
            <div className="relative">
                <Input
                    placeholder={t('nav.shared.search')}
                    value={searchTerm}
                    suffix={<TbSearch className="text-lg" />}
                    className="w-64"
                    onChange={(e) => handleSearch(e.target.value)}
                />
                {searchTerm && (
                    <Button
                        className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                        onClick={clearSearch}
                    >
                        <TbX className="text-lg" />
                    </Button>
                )}
            </div>
        </div>
    )
}

export default DigitizedListSearch
