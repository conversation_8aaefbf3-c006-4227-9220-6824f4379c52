import { useMutation } from '@tanstack/react-query'
import { signInApi } from '@/services/AuthService'
import { useAuthStore } from '@/views/auth/store/Auth'

export function useSignIn() {
    const { setAuth } = useAuthStore()

    return useMutation({
        mutationFn: signInApi,
        onSuccess: (data) => {
            if (data && data.token) {
                setAuth(data.token, data.refreshToken)
            }
        },
        onError: (error) => {
            console.error('Error updating keyword:', error)
        },
    })
}
