import { useMutation } from '@tanstack/react-query'
import { signInApi } from '@/services/AuthService'
import { useAuthStore } from '@/views/auth/store/Auth'

export function useSignIn() {
    const { setAuth } = useAuthStore()

    return useMutation({
        mutationFn: signInApi,
        onSuccess: (data) => {
            if (data && data.token) {
                setAuth(data) // Pass the entire response
            }
        },
        onError: (error) => {
            console.error('Error signing in:', error)
        },
    })
}
