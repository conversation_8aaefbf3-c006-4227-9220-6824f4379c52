/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { useState } from 'react'
import Dialog from '@/components/ui/Dialog'
import Button from '@/components/ui/Button'
import UserForm from './UserForm'
import { CreateUser, User } from '@/@types/auth'
import Notification from '@/components/ui/Notification'
import toast from '@/components/ui/toast'
import { t } from 'i18next'
import { useCreateUser, useUpdateUser, useUpdateUserStatus } from '@/hooks/user'

type UserModelProps = {
    dialogIsOpen: boolean
    onDialogClose: () => void
    user?: User | null
}

const UserModel = (props: UserModelProps) => {
    const { dialogIsOpen, onDialogClose, user } = props
    const [loading, setLoading] = useState(false)
    const { mutate: createUser } = useCreateUser()
    const { mutate: updateUser } = useUpdateUser()
    const { mutate: updateUserStatus } = useUpdateUserStatus()

    const handleFormSubmit = async (values: CreateUser) => {
        setLoading(true)
        try {
            if (user) {
                await updateUser({ id: user.id, userData: values })
                toast.push(
                    <Notification title="" type="success" duration={2500}>
                        {t('nav.systemSecurity.userUpdated')}
                    </Notification>,
                    { placement: 'top-center' },
                )
            } else {
                await createUser(values)

                toast.push(
                    <Notification title="" type="success" duration={2500}>
                        {t('nav.systemSecurity.userCreated')}
                    </Notification>,
                    { placement: 'top-center' },
                )
            }
            onDialogClose()
        } catch (error: any) {
            const data = error?.errors[0]

            toast.push(
                <Notification title="" type="danger" duration={2500}>
                    {data?.code === 'User.EmailExists'
                        ? t('nav.systemSecurity.emailAlreadyExists')
                        : data?.code === 'User.PhoneNumberExists'
                          ? t('nav.systemSecurity.phoneNumberAlreadyInUse')
                          : data?.description ||
                            t('nav.systemSecurity.failedToCreateUser')}
                </Notification>,
                { placement: 'top-center' },
            )
        } finally {
            setLoading(false)
        }
    }

    const handleClose = () => {
        if (!loading) {
            onDialogClose()
        }
    }

    return (
        <Dialog
            isOpen={dialogIsOpen}
            shouldCloseOnOverlayClick={false}
            shouldCloseOnEsc={!loading}
            width={750}
            height={500}
            className={''}
            onClose={handleClose}
            onRequestClose={handleClose}
        >
            <div className="flex flex-col h-full justify-between ">
                <h4 className="text-xl font-semibold pb-2 border-b border-gray-200">
                    {user
                        ? t('nav.systemSecurity.editUser')
                        : t('nav.systemSecurity.addUser')}
                </h4>
                <div className="overflow-y-auto">
                    <UserForm
                        loading={loading}
                        newUser={!user}
                        defaultValues={
                            user
                                ? {
                                      firstName: user.firstName || '',
                                      lastName: user.lastName || '',
                                      email: user.email || '',
                                      phoneNumber: user.phoneNumber || '',
                                      organizationalNodeCodes:
                                          user.organizationalNodes.map(
                                              (node) => node.code,
                                          ),
                                  }
                                : undefined
                        }
                        onFormSubmit={handleFormSubmit}
                    >
                        <div className="flex items-center justify-end gap-2">
                            <Button
                                size="sm"
                                type="button"
                                disabled={loading}
                                onClick={handleClose}
                            >
                                {t('nav.shared.cancel')}
                            </Button>
                            {user && (
                                <Button
                                    size="sm"
                                    type="button"
                                    disabled={loading}
                                    variant="solid"
                                    className={
                                        user?.status === 1
                                            ? 'bg-blue-500 hover:bg-blue-600 border-blue-500 hover:border-blue-600'
                                            : 'bg-emerald-500 hover:bg-emerald-600 border-emerald-500 hover:border-emerald-600'
                                    }
                                    onClick={() => {
                                        if (user) {
                                            updateUserStatus({
                                                userId: user.id,
                                                status:
                                                    user.status === 1 ? 2 : 1,
                                            })

                                            toast.push(
                                                <Notification
                                                    title=""
                                                    type="success"
                                                    duration={2500}
                                                >
                                                    {t(
                                                        'nav.systemSecurity.userStatusUpdated',
                                                    )}
                                                </Notification>,
                                                {
                                                    placement: 'top-center',
                                                },
                                            )
                                            onDialogClose()
                                        }
                                    }}
                                >
                                    {user?.status === 1
                                        ? t('nav.GlobalActions.frozen')
                                        : t('nav.GlobalActions.approve')}
                                </Button>
                            )}
                            <Button
                                size="sm"
                                variant="solid"
                                type="submit"
                                loading={loading}
                            >
                                {user
                                    ? t('nav.shared.update')
                                    : t('nav.shared.create')}
                            </Button>
                        </div>
                    </UserForm>
                </div>
            </div>
        </Dialog>
    )
}

export default UserModel
