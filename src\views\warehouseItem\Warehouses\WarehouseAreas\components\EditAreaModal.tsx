import { useState, useEffect } from 'react'
import { use<PERSON><PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import Dialog from '@/components/ui/Dialog'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import { FormItem, Form } from '@/components/ui/Form'
import useTranslation from '@/utils/hooks/useTranslation'
import { TbX, TbCheck, TbEdit } from 'react-icons/tb'

interface EditAreaModalProps {
    isOpen: boolean
    onClose: () => void
    onSubmit: (data: EditAreaFormData) => void
    initialData?: EditAreaFormData | null
}

interface EditAreaFormData {
    length: number
    width: number
}

const EditAreaModal = ({ isOpen, onClose, onSubmit, initialData }: EditAreaModalProps) => {
    const { t } = useTranslation()
    const [loading, setLoading] = useState(false)

    const editAreaSchema = z.object({
        length: z
            .number()
            .min(0.1, { message: t('nav.warehouses.areaLengthRequired') }),
        width: z
            .number()
            .min(0.1, { message: t('nav.warehouses.areaWidthRequired') }),
    })

    const {
        handleSubmit,
        control,
        reset,
        formState: { errors },
    } = useForm<EditAreaFormData>({
        defaultValues: {
            length: 0,
            width: 0,
        },
        resolver: zodResolver(editAreaSchema),
    })

    // Reset form when initialData changes
    useEffect(() => {
        if (initialData) {
            reset({
                length: initialData.length,
                width: initialData.width,
            })
        }
    }, [initialData, reset])

    const handleFormSubmit = async (data: EditAreaFormData) => {
        setLoading(true)
        try {
            // Simulate API call
            await new Promise((resolve) => setTimeout(resolve, 1000))
            onSubmit(data)
            reset()
        } catch (error) {
            console.error('Error updating area:', error)
        } finally {
            setLoading(false)
        }
    }

    const handleClose = () => {
        reset()
        onClose()
    }

    return (
        <Dialog
            isOpen={isOpen}
            onClose={handleClose}
            onRequestClose={handleClose}
        >
            <div className="max-w-md mx-auto">
                {/* Header */}
                <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                    <div className="flex items-center gap-3">
                        <div className="flex items-center justify-center w-10 h-10 bg-indigo-100 dark:bg-indigo-900/30 rounded-lg">
                            <TbEdit className="w-5 h-5 text-indigo-600 dark:text-indigo-400" />
                        </div>
                        <div>
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                {t('nav.warehouses.editArea')}
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                                {t('nav.warehouses.editAreaDesc')}
                            </p>
                        </div>
                    </div>
                    <Button
                        variant="plain"
                        size="sm"
                        icon={<TbX />}
                        className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                        onClick={handleClose}
                    />
                </div>

                {/* Form */}
                <div className="p-6">
                    <Form onSubmit={handleSubmit(handleFormSubmit)}>
                        <div className="space-y-6">
                            {/* Area Dimensions */}
                            <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                                <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-4">
                                    {t('nav.warehouses.areaDimensions')}
                                </h4>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <FormItem
                                        label={`${t('nav.warehouses.areaLength')} *`}
                                        invalid={!!errors.length}
                                        errorMessage={errors.length?.message}
                                    >
                                        <Controller
                                            name="length"
                                            control={control}
                                            render={({ field }) => (
                                                <Input
                                                    {...field}
                                                    type="number"
                                                    min="0.1"
                                                    step="0.1"
                                                    placeholder="0.0"
                                                    onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                                                />
                                            )}
                                        />
                                    </FormItem>
                                    <FormItem
                                        label={`${t('nav.warehouses.areaWidth')} *`}
                                        invalid={!!errors.width}
                                        errorMessage={errors.width?.message}
                                    >
                                        <Controller
                                            name="width"
                                            control={control}
                                            render={({ field }) => (
                                                <Input
                                                    {...field}
                                                    type="number"
                                                    min="0.1"
                                                    step="0.1"
                                                    placeholder="0.0"
                                                    onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                                                />
                                            )}
                                        />
                                    </FormItem>
                                </div>
                            </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex justify-end gap-3 mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                            <Button
                                type="button"
                                variant="plain"
                                disabled={loading}
                                className="min-w-[100px]"
                                onClick={handleClose}
                            >
                                {t('nav.shared.cancel')}
                            </Button>
                            <Button
                                type="submit"
                                variant="solid"
                                icon={<TbCheck />}
                                loading={loading}
                                className="min-w-[100px] bg-indigo-600 hover:bg-indigo-700"
                            >
                                {t('nav.shared.update')}
                            </Button>
                        </div>
                    </Form>
                </div>
            </div>
        </Dialog>
    )
}

export default EditAreaModal
