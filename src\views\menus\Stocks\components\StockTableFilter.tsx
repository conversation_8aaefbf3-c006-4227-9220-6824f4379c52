import { useState } from 'react'
import { use<PERSON><PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z, type ZodType } from 'zod'
import Button from '@/components/ui/Button'
import Drawer from '@/components/ui/Drawer'
import Select from '@/components/ui/Select'
import Checkbox from '@/components/ui/Checkbox'
import { FormItem, FormContainer, Form } from '@/components/ui/Form'
import useTranslation from '@/utils/hooks/useTranslation'
import { TbFilter, TbFilterOff } from 'react-icons/tb'
import type { StockFilterConfig } from '@/store/selectors/stockSelector'

type FilterFormSchema = {
    nameFilter?: string
    villageFilter?: string
    cityFilter?: string
    governorateFilter?: string
    countryFilter?: string
    managerFilter?: string
    statusFilter?: string
    filterColumns?: string[]
}

const validationSchema: ZodType<FilterFormSchema> = z.object({
    nameFilter: z.string().optional().default(''),
    villageFilter: z.string().optional().default(''),
    cityFilter: z.string().optional().default(''),
    governorateFilter: z.string().optional().default(''),
    countryFilter: z.string().optional().default(''),
    managerFilter: z.string().optional().default(''),
    statusFilter: z.string().optional().default(''),
    filterColumns: z.array(z.string()).optional().default([]),
})

const StockTableFilter = () => {
    const { t } = useTranslation()
    const [filterIsOpen, setFilterIsOpen] = useState(false)

    // Define default columns with proper translations
    const defaultColumns = [
        { value: 'id', label: 'ID' },
        { value: 'name', label: t('nav.shared.name') },
        { value: 'village', label: t('nav.buildings.village') },
        { value: 'city', label: t('nav.buildings.city') },
        { value: 'governorate', label: t('nav.buildings.governorate') },
        { value: 'managerName', label: t('nav.Stock.stockManager') },
        { value: 'status', label: t('nav.shared.status') },
        { value: 'action', label: t('nav.shared.actions') },
    ]

    // Create village options for the select
    const villageOptions = [
        { value: '', label: t('nav.shared.all') },
        ...availableVillages, // Already in {value, label} format from selector
    ]

    // Create city options for the select
    const cityOptions = [
        { value: '', label: t('nav.shared.all') },
        ...availableCities, // Already in {value, label} format from selector
    ]

    // Create governorate options for the select
    const governorateOptions = [
        { value: '', label: t('nav.shared.all') },
        ...availableGovernorates, // Already in {value, label} format from selector
    ]

    // Create manager options for the select
    const managerOptions = [
        { value: '', label: t('nav.shared.all') },
        ...availableManagers, // Already in {value, label} format from selector
    ]

    // Create status options for the select
    const statusOptions = [
        { value: '', label: t('nav.shared.all') },
        { value: '0', label: t('nav.GlobalActions.frozen') },
        { value: '1', label: t('nav.shared.active') },
    ]

    // Check if any filters are active
    const hasActiveFilters =
        filterConfig &&
        ((filterConfig.names && filterConfig.names.length > 0) ||
            (filterConfig.villages && filterConfig.villages.length > 0) ||
            (filterConfig.cities && filterConfig.cities.length > 0) ||
            (filterConfig.governorates &&
                filterConfig.governorates.length > 0) ||
            (filterConfig.countries && filterConfig.countries.length > 0) ||
            (filterConfig.managers && filterConfig.managers.length > 0) ||
            filterConfig.status !== undefined)

    const {
        handleSubmit,
        control,
        reset,
        formState: { errors },
    } = useForm<FilterFormSchema>({
        defaultValues: {
            nameFilter: '',
            villageFilter: '',
            cityFilter: '',
            governorateFilter: '',
            countryFilter: '',
            managerFilter: '',
            statusFilter: '',
            filterColumns: [
                'id',
                'name',
                'village',
                'city',
                'governorate',
                'managerName',
                'status',
                'action',
            ],
        },
        resolver: zodResolver(validationSchema),
    })

    const onSubmit = (values: FilterFormSchema) => {
        console.log('🔥 onSubmit called with values:', values)
        console.log('🔥 Form errors:', errors)

        // Build comprehensive filter config
        const newFilterConfig: Partial<StockFilterConfig> = {}

        // Apply village filter
        if (values.villageFilter && values.villageFilter !== '') {
            newFilterConfig.villages = [values.villageFilter]
        }

        // Apply city filter
        if (values.cityFilter && values.cityFilter !== '') {
            newFilterConfig.cities = [values.cityFilter]
        }

        // Apply governorate filter
        if (values.governorateFilter && values.governorateFilter !== '') {
            newFilterConfig.governorates = [values.governorateFilter]
        }

        // Apply manager filter
        if (values.managerFilter && values.managerFilter !== '') {
            newFilterConfig.managers = [values.managerFilter]
        }

        // Apply status filter
        if (values.statusFilter && values.statusFilter !== '') {
            newFilterConfig.status = parseInt(values.statusFilter)
        }

        // Apply column filter

        if (values.filterColumns) {
            setColumnsAction(values.filterColumns)
        }

        setFilterIsOpen(false)
    }

    const handleClearFilters = () => {
        // Clear all filters
        const clearedConfig: Partial<StockFilterConfig> = {
            names: [],
            villages: [],
            cities: [],
            governorates: [],
            managers: [],
            status: undefined,
        }

        handleFilterChange(clearedConfig)

        // Reset form values
        reset({
            nameFilter: '',
            villageFilter: '',
            cityFilter: '',
            governorateFilter: '',
            managerFilter: '',
            statusFilter: '',
            filterColumns: defaultColumns.map((col) => col.value),
        })

        // Reset columns to default
        setColumnsAction(defaultColumns.map((col) => col.value))

        setFilterIsOpen(false)
    }

    return (
        <>
            <Button
                icon={hasActiveFilters ? <TbFilterOff /> : <TbFilter />}
                variant={hasActiveFilters ? 'solid' : 'default'}
                size="sm"
                className={`shadow-sm hover:shadow-md transition-all duration-200 ${
                    hasActiveFilters
                        ? 'bg-orange-500 hover:bg-orange-600 text-white'
                        : 'border-gray-300 hover:border-gray-400'
                }`}
                onClick={() => setFilterIsOpen(true)}
            >
                {t('nav.shared.filter')}
                {hasActiveFilters && (
                    <span className="ml-1 px-1.5 py-0.5 text-xs bg-white bg-opacity-20 rounded-full">
                        {Object.values(filterConfig).filter(Boolean).length}
                    </span>
                )}
            </Button>
            <Drawer
                title={t('nav.shared.filter')}
                isOpen={filterIsOpen}
                footer={
                    <div className="flex justify-around items-center w-full ">
                        <Button
                            className=""
                            variant="plain"
                            onClick={handleClearFilters}
                        >
                            {t('nav.shared.clear')}
                        </Button>
                        <Button
                            variant="solid"
                            onClick={handleSubmit(onSubmit)}
                        >
                            {t('nav.shared.confirm')}
                        </Button>
                    </div>
                }
                onClose={() => setFilterIsOpen(false)}
                onRequestClose={() => setFilterIsOpen(false)}
            >
                <FormContainer>
                    <Form onSubmit={handleSubmit(onSubmit)}>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {/* Village Filter */}
                            <FormItem label={t('nav.stocks.filterByVillage')}>
                                <Controller
                                    name="villageFilter"
                                    control={control}
                                    render={({ field }) => (
                                        <Select
                                            options={villageOptions}
                                            placeholder={t(
                                                'nav.stocks.selectVillage',
                                            )}
                                            value={villageOptions.find(
                                                (option) =>
                                                    option.value ===
                                                    field.value,
                                            )}
                                            onChange={(option) =>
                                                field.onChange(
                                                    option?.value || '',
                                                )
                                            }
                                        />
                                    )}
                                />
                            </FormItem>

                            {/* City Filter */}
                            <FormItem label={t('nav.stocks.filterByCity')}>
                                <Controller
                                    name="cityFilter"
                                    control={control}
                                    render={({ field }) => (
                                        <Select
                                            options={cityOptions}
                                            placeholder={t(
                                                'nav.stocks.selectCity',
                                            )}
                                            value={cityOptions.find(
                                                (option) =>
                                                    option.value ===
                                                    field.value,
                                            )}
                                            onChange={(option) =>
                                                field.onChange(
                                                    option?.value || '',
                                                )
                                            }
                                        />
                                    )}
                                />
                            </FormItem>

                            {/* Governorate Filter */}
                            <FormItem
                                label={t('nav.stocks.filterByGovernorate')}
                            >
                                <Controller
                                    name="governorateFilter"
                                    control={control}
                                    render={({ field }) => (
                                        <Select
                                            options={governorateOptions}
                                            placeholder={t(
                                                'nav.stocks.selectGovernorate',
                                            )}
                                            value={governorateOptions.find(
                                                (option) =>
                                                    option.value ===
                                                    field.value,
                                            )}
                                            onChange={(option) =>
                                                field.onChange(
                                                    option?.value || '',
                                                )
                                            }
                                        />
                                    )}
                                />
                            </FormItem>
                            {/* Manager Filter */}
                            <FormItem label={t('nav.stocks.filterByManager')}>
                                <Controller
                                    name="managerFilter"
                                    control={control}
                                    render={({ field }) => (
                                        <Select
                                            options={managerOptions}
                                            placeholder={t(
                                                'nav.stocks.selectManager',
                                            )}
                                            value={managerOptions.find(
                                                (option) =>
                                                    option.value ===
                                                    field.value,
                                            )}
                                            onChange={(option) =>
                                                field.onChange(
                                                    option?.value || '',
                                                )
                                            }
                                        />
                                    )}
                                />
                            </FormItem>

                            {/* Status Filter */}
                            <FormItem label={t('nav.shared.status')}>
                                <Controller
                                    name="statusFilter"
                                    control={control}
                                    render={({ field }) => (
                                        <Select
                                            options={statusOptions}
                                            placeholder={t(
                                                'nav.shared.selectStatus',
                                            )}
                                            value={statusOptions.find(
                                                (option) =>
                                                    option.value ===
                                                    field.value,
                                            )}
                                            onChange={(option) =>
                                                field.onChange(
                                                    option?.value || '',
                                                )
                                            }
                                        />
                                    )}
                                />
                            </FormItem>
                        </div>

                        {/* Column Visibility Section */}
                        <FormItem
                            label={t('nav.shared.customizeColumns')}
                            className="mt-6"
                        >
                            <div className="mt-4 space-y-2">
                                <Controller
                                    name="filterColumns"
                                    control={control}
                                    render={({ field }) => (
                                        <Checkbox.Group
                                            className="grid grid-cols-2 gap-6"
                                            value={field.value}
                                            onChange={field.onChange}
                                        >
                                            {defaultColumns.map((column) => (
                                                <Checkbox
                                                    key={column.value}
                                                    name={field.name}
                                                    value={column.value}
                                                    className="justify-between flex-row-reverse heading-text w-full"
                                                >
                                                    {column.label}
                                                </Checkbox>
                                            ))}
                                        </Checkbox.Group>
                                    )}
                                />
                            </div>
                        </FormItem>
                    </Form>
                </FormContainer>
            </Drawer>
        </>
    )
}

export default StockTableFilter
