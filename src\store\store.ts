import { configureStore, combineReducers } from '@reduxjs/toolkit'
import locationReducer from './slices/locationSlice'
import categoryReducer from './slices/categorySlice'
import branchReducer from './slices/branchSlice'
import routeKeyReducer from './routeKeyStore'
import localeReducer from './localeStore'

const rootReducer = combineReducers({
    location: locationReducer,
    category: categoryReducer,
    branch: branchReducer,
    routeKey: routeKeyReducer,
    locale: localeReducer,
})

export const store = configureStore({
    reducer: rootReducer,
    middleware: (getDefaultMiddleware) =>
        getDefaultMiddleware({
            serializableCheck: false,
        }),
})

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch
