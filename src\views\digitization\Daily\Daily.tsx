import Container from '@/components/shared/Container'
import AdaptiveCard from '@/components/shared/AdaptiveCard'
import DailyListActionTools from './components/DailyListActionTools'
import DailyListTableTools from './components/DailyListTableTools'
import DailyListTable from './components/DailyListTable'
import DailyModal from './components/DailyModal'
import useTranslation from '@/utils/hooks/useTranslation'
import { useState } from 'react'
import { TbFileText } from 'react-icons/tb'

const Daily = () => {
    const { t } = useTranslation()

    const [isEditModalOpen, setIsEditModalOpen] = useState(false)
    const [editingFileId, setEditingFileId] = useState<string | undefined>()

    const handleEditSuccess = () => {
        setIsEditModalOpen(false)
        setEditingFileId(undefined)
    }

    const handleEditClose = () => {
        setIsEditModalOpen(false)
        setEditingFileId(undefined)
    }

    return (
        <>
            <Container>
                <AdaptiveCard className="shadow-lg">
                    <div className="flex flex-col gap-6">
                        {/* Enhanced Header Section */}
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 pb-4 border-b border-gray-200">
                            <div className="flex items-center gap-3">
                                <div className="flex items-center justify-center w-10 h-10 bg-primary-subtle rounded-lg">
                                    <TbFileText className="w-5 h-5 text-primary-deep" />
                                </div>
                                <h3 className=" ">
                                    {t('nav.digitization.daily')}
                                </h3>
                            </div>
                            <DailyListActionTools />
                        </div>

                        {/* Table Tools Section */}
                        <div className="">
                            <DailyListTableTools />
                        </div>

                        {/* Table Section */}
                        <div className="">
                            <DailyListTable />
                        </div>
                    </div>
                </AdaptiveCard>
            </Container>

            {/* Edit Modal */}
            <DailyModal
                isOpen={isEditModalOpen}
                fileId={editingFileId}
                onSuccess={handleEditSuccess}
                onClose={handleEditClose}
            />

            {/* Selection Footer */}
            {/* <DailySelectionFooter
                selectedCount={0}
                onDeleteSelected={handleDeleteSelected}
                onClearSelection={handleClearSelection}
                onStatusUpdate={(status) =>
                    console.log('Update status:', status)
                }
            /> */}
        </>
    )
}

export default Daily
