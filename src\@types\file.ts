export type FileForm = {
    fileTitle: string
    confidentiality: Confidentiality
    digitizationStationId: number
    categoryId: number
    organizationalNodeId: string
    digitizationType: DigitizationType
    mediumType: MediumType
    userIdsWithAccess: string[]
}

export type FileAccess = {
    userId: string
    username: string
    accessType: number
    createdAt: string
}

export type File = {
    fileId: string
    fileTitle: string
    categoryName: string
    fileStatus: number
    mediumType: number
    numberOfPages: number
}

export interface FileDetails {
    fileId: string
    fileTitle: string
    confidentiality: number
    digitizationStationId: number
    digitizationStationName: string
    categoryId: number
    categoryName: string
    organizationalNodeId: string
    organizationalNodeName: string
    digitizationType: number
    fileStatus: number
    mediumType: number
    numberOfPages: number
    createdByID: string
    createdByUsername: string
    createdAt: string
    updatedByID: string
    updatedByUsername: string
    updatedAt: string
    fileAccesses: FileAccess[]
}

export enum Confidentiality {
    NonConfidential = 0,
    Confidential = 1,
    HighlyConfidential = 2,
}

export enum DigitizationType {
    Daily = 2,
    Backlog = 4,
}

export enum MediumType {
    PaperBased = 0,
    ElectronicWithoutPhysicalCopy = 1,
    ElectronicWithPhysicalCopy = 2,
}
