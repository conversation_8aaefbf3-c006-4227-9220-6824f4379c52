/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState } from 'react'
import Dialog from '@/components/ui/Dialog'
import Notification from '@/components/ui/Notification'
import toast from '@/components/ui/toast'
import BuildingForm from './BuildingForm'
import useTranslation from '@/utils/hooks/useTranslation'
import { BuildingForm as BuildingFormType } from '@/@types/building'
import FormActions from '@/components/shared/actions/FormActions'
import {
    useChangeStatusBuilding,
    useCreateBuilding,
    useGetBuildingById,
    useUpdateBuilding,
} from '@/hooks/building'

type BuildingModalProps = {
    isOpen: boolean
    onClose: () => void
    selectedBuildingId?: number
}

const BuildingModal = ({
    isOpen,
    onClose,
    selectedBuildingId,
}: BuildingModalProps) => {
    const { t } = useTranslation()
    const [isSubmitting, setIsSubmitting] = useState(false)

    const { data: editData } = useGetBuildingById(selectedBuildingId!)
    const updateBuilding = useUpdateBuilding()
    const createBuilding = useCreateBuilding()
    const updateBuildingStatus = useChangeStatusBuilding()

    const isEdit = !!selectedBuildingId

    const handleFormSubmit = async (values: BuildingFormType) => {
        setIsSubmitting(true)

        try {
            if (selectedBuildingId) {
                await updateBuilding.mutateAsync({
                    id: selectedBuildingId,
                    building: { ...values, id: selectedBuildingId },
                })
            } else {
                await createBuilding.mutateAsync(values)
            }
            toast.push(
                <Notification type="success">
                    {isEdit
                        ? t('nav.buildings.buildingUpdated')
                        : t('nav.buildings.buildingCreated')}
                </Notification>,
                { placement: 'top-center' },
            )
            onClose()
        } catch (error: any) {
            const msg = error?.errors[0]

            toast.push(
                <Notification title="" type="danger" duration={2500}>
                    {msg?.description || t('nav.shared.failed')}
                </Notification>,
                { placement: 'top-center' },
            )
        } finally {
            setIsSubmitting(false)
        }
    }

    const getDefaultValues = (): Partial<BuildingFormType> => {
        if (isEdit && editData) {
            return {
                id: editData.id || 0,
                address: editData.address || '',
                name: editData.name || '',
                description: editData.description || '',
                districtOrVillageCode: editData.districtOrVillageCode || '',
                email: editData.email || '',
                mobileNumber: editData.mobileNumber || '',
            }
        }

        return {
            name: '',
            address: '',
            description: '',
            districtOrVillageCode: '',
            email: '',
            mobileNumber: '',
        }
    }

    return (
        <>
            <Dialog
                isOpen={isOpen}
                shouldCloseOnOverlayClick={true}
                shouldCloseOnEsc={true}
                width={1000}
                height={550}
                className={'h-min'}
                onClose={onClose}
                onRequestClose={onClose}
            >
                <div className="flex flex-col h-full">
                    <div className="flex items-center justify-center">
                        <h3 className="text-lg font-semibold">
                            {isEdit
                                ? t('nav.shared.edit') +
                                  ' ' +
                                  t('nav.buildings.buildings')
                                : t('nav.shared.add') +
                                  ' ' +
                                  t('nav.buildings.buildings')}
                        </h3>
                    </div>

                    <div className="flex-1 overflow-auto">
                        <BuildingForm
                            defaultValues={getDefaultValues()}
                            onFormSubmit={handleFormSubmit}
                        >
                            <div className="flex items-center justify-between px-4 pt-4  dark:bg-gray-800">
                                <div>
                                    {isEdit && (
                                        <div className="text-sm text-gray-500 dark:text-gray-400">
                                            {t('nav.shared.id')} :{' '}
                                            {editData?.id || 0}
                                        </div>
                                    )}
                                </div>
                                <FormActions
                                    isEdit={isEdit}
                                    data={editData}
                                    loading={isSubmitting}
                                    showStatusButton={true}
                                    statusButtonColor={
                                        editData?.status === 1
                                            ? 'red'
                                            : 'emerald'
                                    }
                                    onStatusChange={async () => {
                                        if (editData) {
                                            try {
                                                await updateBuildingStatus.mutateAsync(
                                                    {
                                                        id: editData.id,
                                                        status:
                                                            editData.status ===
                                                            1
                                                                ? 2
                                                                : 1,
                                                    },
                                                )
                                                toast.push(
                                                    <Notification
                                                        title=""
                                                        type="success"
                                                        duration={2500}
                                                    >
                                                        {t(
                                                            'nav.buildings.buildingStatusUpdated',
                                                        )}
                                                    </Notification>,
                                                    {
                                                        placement: 'top-center',
                                                    },
                                                )
                                                onClose()
                                            } catch (error: any) {
                                                toast.push(
                                                    <Notification
                                                        title=""
                                                        type="danger"
                                                        duration={2500}
                                                    >
                                                        {error?.errors?.[0]
                                                            ?.description ||
                                                            t(
                                                                'nav.shared.failed',
                                                            )}
                                                    </Notification>,
                                                    {
                                                        placement: 'top-center',
                                                    },
                                                )
                                            }
                                        }
                                    }}
                                />
                            </div>
                        </BuildingForm>
                    </div>
                </div>
            </Dialog>
        </>
    )
}

export default BuildingModal
