import { Form, FormItem } from '@/components/ui/Form'
import Input from '@/components/ui/Input'
import Button from '@/components/ui/Button'
import useTranslation from '@/utils/hooks/useTranslation'
import { Controller, useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import type { StockRequest, StockPhone } from '@/@types/stocks'
import { Card } from '@/components/ui'
import { useEffect, useState } from 'react'
import LocationSelector from '@/components/shared/LocationSelector'
import {
    TbTrash,
    TbPlus,
    TbPhone,
    TbMapPin,
    TbUser,
    TbMail,
    TbFileText,
} from 'react-icons/tb'
import BottomStickyBar from '@/components/template/BottomStickyBar'
import { CommonProps } from '@/@types/common'
import { useGetStockById } from '@/hooks/stock'

type StockFormProps = {
    onSubmit: (data: StockRequest) => Promise<void>
    mode: 'add' | 'edit'
    stock_id?: number
} & CommonProps

// Create a validation schema for form submission
const validationSchema = () => {
    const { t } = useTranslation()

    // Phone number validation regex - allows digits, spaces, hyphens, parentheses, and plus sign
    const phoneRegex = /^[\d\s\-()+ ]*$/

    return z.object({
        name: z.string().min(1, { message: t('nav.Form.nameRequired') }),
        managerName: z
            .string()
            .min(1, { message: t('nav.Form.managerRequired') }),
        villageCode: z
            .string()
            .min(1, { message: t('nav.Form.villageRequired') }),
        address: z.string().min(1, { message: t('nav.Form.addressRequired') }),
        maintenancePhone: z
            .string()
            .optional()
            .refine((val) => !val || phoneRegex.test(val), {
                message: t('nav.Form.phoneNumbersOnly'),
            }),
        securityPhone: z
            .string()
            .optional()
            .refine((val) => !val || phoneRegex.test(val), {
                message: t('nav.Form.phoneNumbersOnly'),
            }),
        email: z
            .string()
            .email({ message: t('nav.Form.invalidEmail') })
            .optional()
            .or(z.literal('')),
        description: z.string().optional(),
        stockPhones: z
            .array(
                z.object({
                    phoneNumber: z
                        .string()
                        .min(1, { message: t('nav.Form.phoneRequired') })
                        .refine((val) => phoneRegex.test(val), {
                            message: t('nav.Form.phoneNumbersOnly'),
                        }),
                    notes: z.string().optional(),
                }),
            )
            .optional()
            .default([]),
    })
}

const StockForm = ({ onSubmit, mode, stock_id, children }: StockFormProps) => {
    const { data: stock, isLoading: stockLoading } = useGetStockById(stock_id!)
    const { t } = useTranslation()

    const editData = stock

    // State for managing phone inputs
    const [phoneInput, setPhoneInput] = useState<StockPhone>({
        phoneNumber: '',
        notes: '',
    })

    const {
        handleSubmit,
        formState: { errors },
        control,
        reset,
    } = useForm<StockRequest>({
        defaultValues: {
            name: '',
            managerName: '',
            villageCode: '',
            address: '',
            maintenancePhone: '',
            securityPhone: '',
            email: '',
            description: '',
            stockPhones: [],
        },
        resolver: zodResolver(validationSchema()),
    })

    // Update form when stock data changes
    useEffect(() => {
        if (editData && !stockLoading && mode === 'edit') {
            reset({
                name: editData.name || '',
                managerName: editData.managerName || '',
                villageCode: editData.villageCode || '',
                address: editData.address || '',
                maintenancePhone: editData.maintenancePhone || '',
                securityPhone: editData.securityPhone || '',
                email: editData.email || '',
                description: editData.description || '',
                stockPhones: editData.stockPhones || [],
            })
        } else if (mode === 'add') {
            reset({
                name: '',
                managerName: '',
                villageCode: '',
                address: '',
                maintenancePhone: '',
                securityPhone: '',
                email: '',
                description: '',
                stockPhones: [],
            })
        }
    }, [editData, reset, mode, stockLoading])

    const onFormSubmit = (values: StockRequest) => {
        onSubmit(values)
    }

    const handlePhoneInput = (value: string) => {
        return value.replace(/[^\d\s\-()+ ]/g, '')
    }

    // Show loading state while fetching stock data
    if (mode === 'edit' && stockLoading) {
        return (
            <div className="flex items-center justify-center p-8">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">
                        {t('nav.shared.loading')}...
                    </p>
                </div>
            </div>
        )
    }

    return (
        <Form
            className="flex w-full gap-4"
            containerClassName="flex flex-col w-full gap-4"
            onSubmit={handleSubmit(onFormSubmit)}
        >
            <div className="flex flex-col gap-4">
                <Card className="w-full p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormItem
                            className="mb-4"
                            label={t('nav.Stock.stockName')}
                            invalid={Boolean(errors.name)}
                            errorMessage={errors.name?.message}
                        >
                            <Controller
                                name="name"
                                control={control}
                                render={({ field }) => (
                                    <Input
                                        type="text"
                                        autoComplete="off"
                                        placeholder={t(
                                            'nav.Stock.enterStockName',
                                        )}
                                        {...field}
                                    />
                                )}
                            />
                        </FormItem>

                        <FormItem
                            className="mb-4"
                            label={t('nav.Stock.stockManager')}
                            invalid={Boolean(errors.managerName)}
                            errorMessage={errors.managerName?.message}
                        >
                            <Controller
                                name="managerName"
                                control={control}
                                render={({ field }) => (
                                    <div className="relative">
                                        <TbUser className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                                        <Input
                                            type="text"
                                            autoComplete="off"
                                            placeholder={t(
                                                'nav.Stock.enterStockManager',
                                            )}
                                            className="pl-10"
                                            {...field}
                                        />
                                    </div>
                                )}
                            />
                        </FormItem>

                        <FormItem
                            className="mb-4"
                            label={t('nav.shared.address')}
                            invalid={Boolean(errors.address)}
                            errorMessage={errors.address?.message}
                        >
                            <Controller
                                name="address"
                                control={control}
                                render={({ field }) => (
                                    <Input
                                        type="text"
                                        autoComplete="off"
                                        placeholder={t(
                                            'nav.shared.enterAddress',
                                        )}
                                        {...field}
                                    />
                                )}
                            />
                        </FormItem>

                        <FormItem
                            className="mb-4"
                            label={t('nav.shared.email')}
                            invalid={Boolean(errors.email)}
                            errorMessage={errors.email?.message}
                        >
                            <Controller
                                name="email"
                                control={control}
                                render={({ field }) => (
                                    <div className="relative">
                                        <TbMail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                                        <Input
                                            type="email"
                                            autoComplete="off"
                                            placeholder={t(
                                                'nav.shared.enterEmail',
                                            )}
                                            className="pl-10"
                                            {...field}
                                        />
                                    </div>
                                )}
                            />
                        </FormItem>

                        <FormItem
                            className="mb-4"
                            label={t('nav.Stock.stockMaintenancePhone')}
                            invalid={Boolean(errors.maintenancePhone)}
                            errorMessage={errors.maintenancePhone?.message}
                        >
                            <Controller
                                name="maintenancePhone"
                                control={control}
                                render={({ field }) => (
                                    <div className="relative">
                                        <TbPhone className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                                        <Input
                                            type="tel"
                                            autoComplete="off"
                                            placeholder={t(
                                                'nav.Stock.enterStockPhones',
                                            )}
                                            className="pl-10"
                                            value={field.value || ''}
                                            onChange={(e) => {
                                                const filteredValue =
                                                    handlePhoneInput(
                                                        e.target.value,
                                                    )
                                                field.onChange(filteredValue)
                                            }}
                                        />
                                    </div>
                                )}
                            />
                        </FormItem>

                        <FormItem
                            className="mb-4"
                            label={t('nav.Stock.stockSecurityPhone')}
                            invalid={Boolean(errors.securityPhone)}
                            errorMessage={errors.securityPhone?.message}
                        >
                            <Controller
                                name="securityPhone"
                                control={control}
                                render={({ field }) => (
                                    <div className="relative">
                                        <TbPhone className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                                        <Input
                                            type="tel"
                                            autoComplete="off"
                                            placeholder={t(
                                                'nav.Stock.enterStockPhones',
                                            )}
                                            className="pl-10"
                                            value={field.value || ''}
                                            onChange={(e) => {
                                                const filteredValue =
                                                    handlePhoneInput(
                                                        e.target.value,
                                                    )
                                                field.onChange(filteredValue)
                                            }}
                                        />
                                    </div>
                                )}
                            />
                        </FormItem>
                    </div>

                    <FormItem
                        className="mb-4"
                        label={t('nav.shared.description')}
                        invalid={Boolean(errors.description)}
                        errorMessage={errors.description?.message}
                    >
                        <Controller
                            name="description"
                            control={control}
                            render={({ field }) => (
                                <div className="relative">
                                    <TbFileText className="absolute left-3 top-3 w-4 h-4 text-gray-400" />
                                    <Input
                                        textArea
                                        rows={3}
                                        autoComplete="off"
                                        placeholder={t(
                                            'nav.shared.enterDescription',
                                        )}
                                        className="pl-10"
                                        {...field}
                                    />
                                </div>
                            )}
                        />
                    </FormItem>
                </Card>

                {/* Stock Phones Section */}
                <Card className="w-full p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                    <div className="flex items-center gap-3 mb-6">
                        <div className="flex items-center justify-center w-10 h-10 bg-primary-subtle rounded-lg">
                            <TbPhone className="w-5 h-5 text-primary-deep" />
                        </div>
                        <h4 className="">{t('nav.Stock.stockPhones')}</h4>
                    </div>

                    <FormItem
                        className=""
                        invalid={Boolean(errors.stockPhones)}
                        errorMessage={errors.stockPhones?.message}
                    >
                        <Controller
                            name="stockPhones"
                            control={control}
                            render={({ field }) => (
                                <div className="space-y-4">
                                    {/* Display existing phones */}
                                    {field.value && field.value.length > 0 && (
                                        <div className="space-y-2">
                                            {field.value.map(
                                                (
                                                    phone: StockPhone,
                                                    index: number,
                                                ) => (
                                                    <div
                                                        key={index}
                                                        className="flex items-center gap-2 p-3 border rounded-lg "
                                                    >
                                                        <div className="flex-1">
                                                            <div className="font-medium ">
                                                                {
                                                                    phone.phoneNumber
                                                                }
                                                            </div>
                                                            {phone.notes && (
                                                                <div className="text-sm text-gray-500 mt-1">
                                                                    {
                                                                        phone.notes
                                                                    }
                                                                </div>
                                                            )}
                                                        </div>
                                                        <Button
                                                            type="button"
                                                            size="sm"
                                                            variant="plain"
                                                            icon={<TbTrash />}
                                                            onClick={() => {
                                                                const newPhones =
                                                                    [
                                                                        ...(field.value ||
                                                                            []),
                                                                    ]
                                                                newPhones.splice(
                                                                    index,
                                                                    1,
                                                                )
                                                                field.onChange(
                                                                    newPhones,
                                                                )
                                                            }}
                                                        >
                                                            {t(
                                                                'nav.shared.remove',
                                                            )}
                                                        </Button>
                                                    </div>
                                                ),
                                            )}
                                        </div>
                                    )}

                                    {/* Add new phone form */}
                                    <div className="border-t pt-4">
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                                            <div>
                                                <label className="flex items-center gap-2 text-sm font-medium mb-1">
                                                    <TbPhone className="w-4 h-4 text-gray-500" />
                                                    {t(
                                                        'nav.shared.phoneNumber',
                                                    )}
                                                </label>
                                                <div className="relative">
                                                    <TbPhone className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                                                    <Input
                                                        type="tel"
                                                        placeholder={t(
                                                            'nav.Stock.enterStockPhone',
                                                        )}
                                                        value={
                                                            phoneInput.phoneNumber
                                                        }
                                                        className="pl-10"
                                                        onChange={(e) => {
                                                            const filteredValue =
                                                                handlePhoneInput(
                                                                    e.target
                                                                        .value,
                                                                )
                                                            setPhoneInput(
                                                                (prev) => ({
                                                                    ...prev,
                                                                    phoneNumber:
                                                                        filteredValue,
                                                                }),
                                                            )
                                                        }}
                                                    />
                                                </div>
                                            </div>
                                            <div>
                                                <label className="flex items-center gap-2 text-sm font-medium mb-1">
                                                    <TbFileText className="w-4 h-4 text-gray-500" />
                                                    {t('nav.shared.notes')}
                                                </label>
                                                <div className="relative">
                                                    <TbFileText className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                                                    <Input
                                                        type="text"
                                                        placeholder={t(
                                                            'nav.Stock.enterStockNotes',
                                                        )}
                                                        value={phoneInput.notes}
                                                        className="pl-10"
                                                        onChange={(e) =>
                                                            setPhoneInput(
                                                                (prev) => ({
                                                                    ...prev,
                                                                    notes: e
                                                                        .target
                                                                        .value,
                                                                }),
                                                            )
                                                        }
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                        <Button
                                            type="button"
                                            size="sm"
                                            variant="solid"
                                            icon={<TbPlus />}
                                            disabled={
                                                !phoneInput.phoneNumber.trim()
                                            }
                                            onClick={() => {
                                                if (
                                                    phoneInput.phoneNumber.trim()
                                                ) {
                                                    const newPhones = [
                                                        ...(field.value || []),
                                                        {
                                                            phoneNumber:
                                                                phoneInput.phoneNumber.trim(),
                                                            notes: phoneInput.notes.trim(),
                                                        },
                                                    ]
                                                    field.onChange(newPhones)
                                                    setPhoneInput({
                                                        phoneNumber: '',
                                                        notes: '',
                                                    })
                                                }
                                            }}
                                        >
                                            {t('nav.shared.addPhone')}
                                        </Button>
                                    </div>
                                </div>
                            )}
                        />
                    </FormItem>
                </Card>

                <Card className="w-full p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                    <div className="flex items-center gap-3 mb-6">
                        <div className="flex items-center justify-center w-10 h-10 bg-primary-subtle rounded-lg">
                            <TbMapPin className="w-5 h-5 text-primary-deep" />
                        </div>
                        <h4 className="text-lg font-semibold text-gray-900">
                            {t('nav.shared.location')}
                        </h4>
                    </div>
                    <LocationSelector
                        control={control}
                        errors={errors}
                        className="w-full"
                        gridCols="grid-cols-1 sm:grid-cols-2 md:grid-cols-4"
                        districtField="villageCode"
                    />
                </Card>

                <BottomStickyBar>{children}</BottomStickyBar>
            </div>
        </Form>
    )
}

export default StockForm
