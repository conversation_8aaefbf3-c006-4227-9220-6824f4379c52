import React, { useState } from 'react'
import AdaptiveCard from '@/components/shared/AdaptiveCard'
import Container from '@/components/shared/Container'
import DocumentTypesTable from './components/DocumentTypesTable'
import DocumentTypeDialog from './components/DocumentTypeDialog'
import DocumentTypeHeader from './components/DocumentTypeHeader'
import DocumentTypeActionTools from './components/DocumentTypeActionTools'
// import DocumentTypeSelectionFooter from './components/DocumentTypeSelectionFooter'
import useTranslation from '@/utils/hooks/useTranslation'
import { TbFileText } from 'react-icons/tb'

const DocumentTypes = () => {
    const { t } = useTranslation()

    const [dialogOpen, setDialogOpen] = useState(false)
    const [editingDocumentTypeId, setEditingDocumentTypeId] = useState<
        number | null
    >(null)

    const handleAddDocumentType = () => {
        setEditingDocumentTypeId(null)
        setDialogOpen(true)
    }

    const handleEditDocumentType = (id: number) => {
        setEditingDocumentTypeId(id)
        setDialogOpen(true)
    }

    const handleCloseDialog = () => {
        setDialogOpen(false)
        setEditingDocumentTypeId(null)
    }

    return (
        <>
            <Container>
                <AdaptiveCard className="shadow-lg">
                    <div className="flex flex-col gap-6">
                        {/* Enhanced Header Section */}
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 pb-4 border-b border-gray-200 dark:border-gray-700">
                            <div className="flex items-center gap-3">
                                <div className="flex items-center justify-center w-10 h-10 bg-primary-subtle rounded-lg">
                                    <TbFileText className="w-5 h-5 text-primary-deep" />
                                </div>
                                <div>
                                    <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-1">
                                        {t('nav.documentTypes.documentTypes')}
                                    </h3>
                                </div>
                            </div>
                            <DocumentTypeHeader
                                onAddDocumentType={handleAddDocumentType}
                            />
                        </div>

                        {/* Action Tools Section */}
                        <div className="">
                            <DocumentTypeActionTools />
                        </div>

                        {/* Table Section */}
                        <div className="">
                            <DocumentTypesTable
                                onEdit={handleEditDocumentType}
                            />
                        </div>

                        <DocumentTypeDialog
                            isOpen={dialogOpen}
                            documentTypeId={editingDocumentTypeId}
                            onClose={handleCloseDialog}
                        />
                    </div>
                </AdaptiveCard>
            </Container>

            {/* Selection Footer */}
            {/* <DocumentTypeSelectionFooter
                selectedCount={selectedDocumentTypes?.length || 0}
                onDeleteSelected={deleteSelectedDocumentTypes}
                onClearSelection={clearSelection}
            /> */}
        </>
    )
}

export default DocumentTypes
