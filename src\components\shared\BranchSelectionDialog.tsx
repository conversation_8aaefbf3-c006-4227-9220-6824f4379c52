import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { TbBuildingStore, TbCheck, TbArrowRight } from 'react-icons/tb'
import Dialog from '@/components/ui/Dialog'
import Button from '@/components/ui/Button'
import Badge from '@/components/ui/Badge'
import type { Branch } from '@/@types/auth'

interface BranchSelectionDialogProps {
    isOpen: boolean
    onClose: () => void
    onBranchSelected: (branch: Branch) => void
    availableBranches: Branch[]
}

const BranchSelectionDialog = ({
    isOpen,
    onClose,
    onBranchSelected,
    availableBranches,
}: BranchSelectionDialogProps) => {
    const { t } = useTranslation()

    const [selectedBranchCode, setSelectedBranchCode] = useState<string>('')
    const [isConfirming, setIsConfirming] = useState(false)

    const handleBranchSelect = (branch: Branch) => {
        setSelectedBranchCode(branch.code)
    }

    const handleConfirm = async () => {
        const selectedBranch = availableBranches.find(
            (b) => b.code === selectedBranchCode,
        )
        if (selectedBranch) {
            setIsConfirming(true)
            onBranchSelected(selectedBranch)
            setTimeout(() => {
                setIsConfirming(false)
                onClose()
            }, 200)
        }
    }

    return (
        <Dialog
            isOpen={isOpen}
            width={600}
            height={450}
            className="branch-selection-dialog"
            closable={false}
        >
            <div className="flex flex-col h-full">
                {/* Header */}
                <div className="flex flex-col items-center space-y-2 mb-4">
                    <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                        <TbBuildingStore
                            size={24}
                            className="text-blue-600 dark:text-blue-400"
                        />
                    </div>
                    <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                        {t('nav.branch.selectBranch', 'Select Your Branch')}
                    </h2>
                    <p className="text-sm text-gray-600 dark:text-gray-300 text-center">
                        {t(
                            'nav.branch.selectBranchDescription',
                            'Choose the branch you want to work with',
                        )}
                    </p>
                    <Badge className="text-xs px-2 py-1">
                        {availableBranches.length}{' '}
                        {t(
                            'nav.branch.availableBranches',
                            'Available Branches',
                        )}
                    </Badge>
                </div>

                {/* Branch List */}
                <div className="flex-1 overflow-y-auto">
                    <div className="space-y-2">
                        {availableBranches.map((branch) => (
                            <div
                                key={branch.code}
                                className={`
                                    flex items-center justify-between p-3 rounded-lg border cursor-pointer
                                    transition-colors duration-150 hover:bg-gray-50 dark:hover:bg-gray-800/50
                                    ${
                                        selectedBranchCode === branch.code
                                            ? 'border-blue-500 bg-blue-50/50 dark:bg-blue-900/10 ring-1 ring-blue-200 dark:ring-blue-800'
                                            : 'border-gray-200 dark:border-gray-700'
                                    }
                                `}
                                onClick={() => handleBranchSelect(branch)}
                            >
                                <div className="flex items-center space-x-3">
                                    <div
                                        className={`
                                            p-2 rounded-md
                                            ${
                                                selectedBranchCode ===
                                                branch.code
                                                    ? 'bg-blue-500 text-white'
                                                    : 'bg-gray-100 dark:bg-gray-800 text-gray-500 dark:text-gray-400'
                                            }
                                        `}
                                    >
                                        <TbBuildingStore size={16} />
                                    </div>
                                    <div>
                                        <h3 className="font-medium text-gray-900 dark:text-white text-sm">
                                            {branch.name}
                                        </h3>
                                        <p className="text-xs text-gray-500 dark:text-gray-400">
                                            {branch.code}
                                        </p>
                                    </div>
                                </div>

                                {selectedBranchCode === branch.code && (
                                    <div className="bg-green-500 text-white p-1.5 rounded-full">
                                        <TbCheck size={14} />
                                    </div>
                                )}
                            </div>
                        ))}
                    </div>
                </div>

                {/* Footer */}
                <div className="flex justify-between items-center pt-3 mt-3 border-t border-gray-200 dark:border-gray-700">
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                        {selectedBranchCode ? (
                            <span className="text-green-600 dark:text-green-400 font-medium flex items-center gap-1">
                                <TbCheck size={16} />
                                {t(
                                    'nav.branch.branchSelected',
                                    'Branch selected',
                                )}
                            </span>
                        ) : (
                            t(
                                'nav.branch.selectBranchTocontinue',
                                'Please select a branch to continue',
                            )
                        )}
                    </div>

                    <Button
                        variant="solid"
                        size="sm"
                        disabled={!selectedBranchCode || isConfirming}
                        loading={isConfirming}
                        className="min-w-[120px]"
                        icon={
                            !isConfirming ? (
                                <TbArrowRight size={16} />
                            ) : undefined
                        }
                        onClick={handleConfirm}
                    >
                        {isConfirming
                            ? t('nav.branch.confirming', 'Confirming...')
                            : t('nav.branch.confirm', 'Confirm Selection')}
                    </Button>
                </div>
            </div>
        </Dialog>
    )
}

export default BranchSelectionDialog
