import { useMutation } from '@tanstack/react-query'
import { useAuthStore } from '@/views/auth/store/Auth'
import { signOutApi } from '@/services/AuthService'

export function useSignOut() {
    const { logout } = useAuthStore()

    return useMutation({
        mutationFn: signOutApi,
        onSuccess: () => {
            logout()
        },
        onError: (error) => {
            console.error('Error signing out:', error)
        },
    })
}
