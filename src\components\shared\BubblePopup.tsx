import React from 'react'
import { Tb<PERSON><PERSON><PERSON>, Tb<PERSON><PERSON><PERSON><PERSON>riangle, TbX } from 'react-icons/tb'
import Button from '@/components/ui/Button'
import useTranslation from '@/utils/hooks/useTranslation'

interface BubblePopupProps {
    isVisible: boolean
    type: 'success' | 'warning' | 'error'
    message: string
    onClose: () => void
    title?: string
}

const BubblePopup: React.FC<BubblePopupProps> = ({
    isVisible,
    type,
    message,
    onClose,
    title,
}) => {
    const { t } = useTranslation()

    if (!isVisible) return null

    const getTypeStyles = () => {
        switch (type) {
            case 'success':
                return {
                    container:
                        'bg-emerald-50 dark:bg-emerald-900/20 border-emerald-200 dark:border-emerald-700',
                    icon: 'text-emerald-500',
                    text: 'text-emerald-800 dark:text-emerald-200',
                    button: 'bg-emerald-500 hover:bg-emerald-500 active:bg-emerald-500 focus:bg-emerald-500 text-white hover:text-white active:text-white focus:text-white',
                }
            case 'warning':
                return {
                    container:
                        'bg-amber-50 dark:bg-amber-900/20 border-amber-200 dark:border-amber-700',
                    icon: 'text-amber-500',
                    text: 'text-amber-800 dark:text-amber-200',
                    button: 'bg-amber-500 hover:bg-amber-500 active:bg-amber-500 focus:bg-amber-500 text-white hover:text-white active:text-white focus:text-white',
                }
            case 'error':
                return {
                    container:
                        'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-700',
                    icon: 'text-red-500',
                    text: 'text-red-800 dark:text-red-200',
                    button: 'bg-red-500 hover:bg-red-500 active:bg-red-500 focus:bg-red-500 text-white hover:text-white active:text-white focus:text-white',
                }
            default:
                return {
                    container:
                        'bg-gray-50 dark:bg-gray-900/20 border-gray-200 dark:border-gray-700',
                    icon: 'text-gray-500',
                    text: 'text-gray-800 dark:text-gray-200',
                    button: 'bg-gray-500 hover:bg-gray-500 active:bg-gray-500 focus:bg-gray-500 text-white hover:text-white active:text-white focus:text-white',
                }
        }
    }

    const getIcon = () => {
        switch (type) {
            case 'success':
                return <TbCheck className={`w-6 h-6 ${getTypeStyles().icon}`} />
            case 'warning':
            case 'error':
                return (
                    <TbAlertTriangle
                        className={`w-6 h-6 ${getTypeStyles().icon}`}
                    />
                )
            default:
                return null
        }
    }

    const styles = getTypeStyles()

    return (
        <>
            {/* Backdrop overlay */}
            <div
                className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40 transition-opacity duration-300"
                onClick={onClose}
            />

            {/* Bubble popup */}
            <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50 max-w-md w-full mx-4">
                <div
                    className={`rounded-2xl p-6 shadow-2xl border-2 ${styles.container} transform transition-all duration-300 scale-100 animate-in zoom-in-95`}
                >
                    {/* Close button */}
                    <button
                        onClick={onClose}
                        className="absolute top-3 right-3 p-1 rounded-full hover:bg-black/5 dark:hover:bg-white/5 transition-colors"
                        aria-label="Close"
                    >
                        <TbX className="w-4 h-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" />
                    </button>

                    {/* Content */}
                    <div className="flex items-start gap-4 mb-6">
                        <div className="flex-shrink-0 mt-1">{getIcon()}</div>
                        <div className="flex-1">
                            {title && (
                                <h3
                                    className={`text-lg font-semibold mb-2 ${styles.text}`}
                                >
                                    {title}
                                </h3>
                            )}
                            <p
                                className={`text-sm leading-relaxed ${styles.text}`}
                            >
                                {message}
                            </p>
                        </div>
                    </div>

                    {/* OK Button */}
                    <div className="flex justify-end">
                        <Button
                            variant="solid"
                            customColorClass={() => styles.button}
                            className="px-6 py-2 rounded-lg font-medium shadow-sm"
                            onClick={onClose}
                        >
                            {t('common.ok')}
                        </Button>
                    </div>
                </div>
            </div>
        </>
    )
}

export default BubblePopup
