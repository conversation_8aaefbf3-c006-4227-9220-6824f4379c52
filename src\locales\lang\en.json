{"app": {"title": "Archiving"}, "nav": {"dashboard": {"dashboard": "Dashboard", "ecommerce": "Ecommerce", "analytic": "Analytic", "project": "Project", "marketing": "Marketing"}, "menus": {"menus": "Menus", "unitStructure": "Unit Structure", "consumables": "Consumables", "stocks": "Stocks", "warehouses": "Warehouses", "buildings": "Buildings", "documentTypes": "Document Types", "categories": "Categories", "keywords": "Keywords", "generalCategories": "General Categories", "languages": "Languages", "entities": "Entities", "senderRecipient": "Sender Recipient", "digitizationStations": "Digitization Stations"}, "boxes": {"boxes": "Boxes", "boxesList": "Boxes List", "createBox": "Create Box", "closeBoxes": "Close Boxes"}, "linkFiles": {"linkFiles": "Link Files", "linkFilesToBoxes": "Link Files to Boxes", "selectBox": "Select Box", "selectFiles": "Select Files", "linkSelectedFiles": "Link Selected Files", "filesLinkedSuccessfully": "Files linked successfully", "linkingFailed": "Failed to link files", "success": "Success", "noFilesSelected": "No files selected", "noBoxSelected": "No box selected", "selectBoxFirst": "Please select a box first", "selectFilesFirst": "Please select files to link"}, "documentTypes": {"documentTypes": "Document Types", "addDocumentType": "Add Document Type", "editDocumentType": "Edit Document Type", "name": "Name", "enterName": "Enter name...", "removeDocumentType": "Remove Document Type", "deleteConfirmation": "Are you sure you want to delete this document type? This action cannot be undone.", "totalDocumentTypes": "Total Document Types", "activeDocumentTypes": "Active Document Types", "inactiveDocumentTypes": "Inactive Document Types", "documentType": "Document Type", "deactivateSelected": "Deactivate Selected", "confirmDeactivateSelected": "Are you sure you want to deactivate the selected document types?"}, "categories": {"categories": "Categories", "addCategory": "Add Category", "editCategory": "Edit Category", "removeCategory": "Remove Category", "deleteConfirmation": "Are you sure you want to delete this category? This action cannot be undone.", "name": "Name", "enterName": "Enter name...", "description": "Description", "enterDescription": "Enter description...", "status": "Status", "approvedOnly": "Approved Only", "showApprovedOnly": "Show Approved Only", "totalCategories": "Total Categories", "activeCategories": "Active Categories", "inactiveCategories": "Inactive Categories", "approvedCategories": "Approved Categories", "pendingCategories": "Pending Categories", "filterByStatus": "Filter by Status", "noCategories": "No categories found", "categoryCreated": "Category created successfully!", "categoryUpdated": "Category updated successfully!", "categoryDeleted": "Category deleted successfully!", "categoryNotFound": "Category not found!", "categoryAlreadyExists": "Category already exists!", "categoryNotValid": "Category is not valid!", "nameRequired": "Name is required", "searchCategories": "Search categories..."}, "keywords": {"keywords": "Keywords", "keyword": "Keyword", "addKeyword": "Add Keyword", "editKeyword": "Edit Keyword", "deleteKeyword": "Delete Keyword", "deleteKeywordConfirm": "Are you sure you want to delete this keyword?", "deleteSelectedKeywords": "Delete Selected Keywords", "deleteSelectedKeywordsConfirm": "Are you sure you want to delete {{count}} selected keywords?", "keywordsSelected": "keywords selected", "value": "Value", "enterKeyword": "Enter keyword...", "keywordRequired": "Keyword is required", "keywordTooLong": "Keyword is too long", "timesUsed": "Times Used", "searchKeywords": "Search keywords..."}, "generalCategories": {"generalCategories": "General Categories", "generalCategory": "General Category", "addGeneralCategory": "Add General Category", "editGeneralCategory": "Edit General Category", "deleteGeneralCategory": "Delete General Category", "deleteGeneralCategoryConfirm": "Are you sure you want to delete this general category?", "deleteSelectedGeneralCategories": "Delete Selected General Categories", "deleteSelectedGeneralCategoriesConfirm": "Are you sure you want to delete {{count}} selected general categories?", "generalCategoriesSelected": "general categories selected", "name": "Name", "enterName": "Enter name...", "nameRequired": "Name is required", "nameTooLong": "Name is too long", "searchGeneralCategories": "Search general categories..."}, "authentication": {"authentication": "Authentication", "signIn": "Sign In", "signInSimple": "Simple", "signInSide": "Side", "signInSplit": "Split", "signUp": "Sign Up", "signUpSimple": "Simple", "signUpSide": "Side", "signUpSplit": "Split", "forgotPassword": "Forgot Password", "forgotPasswordSimple": "Simple", "forgotPasswordSide": "Side", "forgotPasswordSplit": "Split", "resetPassword": "Reset Password", "resetPasswordSimple": "Simple", "resetPasswordSide": "Side", "resetPasswordSplit": "Split", "otpVerification": "Otp Verification", "otpVerificationSimple": "Simple", "otpVerificationSide": "Side", "otpVerificationSplit": "Split", "welcome": "Welcome", "pleaseEnterYourCredentials": "Please enter your credentials to sign in!", "dontHaveAnAccountYet": "Don't have an account yet?", "email": "Email", "password": "Password", "signingIn": "Signing in...", "pleaseEmail": "Please enter your email", "pleasePassword": "Please enter your password", "changePassword": "Change Password", "passwordChangeRequired": "Password Change Required", "defaultPasswordMessage": "Your account is using a default password. Please change it to continue.", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm New Password", "enterCurrentPassword": "Enter your current password", "enterNewPassword": "Enter your new password", "confirmNewPassword": "Confirm your new password", "passwordRequirements": "Password Requirements:", "passwordMinLength": "At least 8 characters long", "passwordUpperLower": "Contains uppercase and lowercase letters", "passwordNumber": "Contains at least one number", "passwordsDoNotMatch": "Passwords do not match", "passwordTooShort": "Password must be at least 8 characters long", "passwordComplexity": "Password must contain at least one uppercase letter, one lowercase letter, and one number", "currentPasswordRequired": "Current password is required", "newPasswordRequired": "New password is required", "confirmPasswordRequired": "Please confirm your new password", "changingPassword": "Changing...", "passwordChangeSuccess": "Password changed successfully! You can now continue using the application.", "passwordChangeFailed": "Failed to change password", "errors": {"userFrozen": "User account has been frozen", "invalidCredentials": "Invalid email or password"}}, "branch": {"selectBranch": "Select Your Branch", "selectBranchDescription": "Choose the branch you want to work with", "availableBranches": "Available Branches", "switchBranchDescription": "Click to switch to a different branch", "continue": "Continue to Dashboard", "connecting": "Connecting to Branch...", "switching": "Switching...", "switchSuccess": "Branch Switched Successfully", "switchMessage": "You are now working in this branch. All your actions will be associated with this location.", "noBranch": "No Branch Selected", "branchSelected": "Branch Selected!", "branchConfirmationMessage": "You are now working in this branch", "redirectingToDashboard": "Redirecting to dashboard...", "selectBranchTocontinue": "Please select a branch to continue", "confirming": "Confirming...", "confirm": "Confirm"}, "guide": {"guide": "Guide", "documentation": "Documentation", "sharedComponentDoc": "Shared Component", "utilsDoc": "Utilities", "changeLog": "Changelog"}, "consumables": {"type": "Type", "selectType": "Select consumable types...", "count": "Count", "countRange": "Count <PERSON>", "warehouse": "Warehouse", "selectWarehouse": "Select warehouse...", "enterCount": "Enter count...", "assignedBy": "Assigned By", "attachments": "Attachments", "consumables": "Consumables", "consumable": "Consumable", "removeConsumables": "Remove these Consumables", "removeConsumable": "Remove this Consumable", "confirmRemoveConsumables": "Are you sure you want to remove these Consumables? This action can't be undo.", "confirmRemoveConsumable": "Are you sure you want to remove this Consumable? This action can't be undo.", "addConsumable": "Add Consumable", "deleteSelected": "Delete Selected", "confirmDeleteSelected": "Are you sure you want to delete the selected consumables?"}, "unitStructure": {"structure": "Unit Structure", "itemName": "Item Name", "description": "Description", "dragToAddNewItem": "Drag to add new item", "enterName": "Enter name...", "enterDescription": "Enter description...", "enterParentId": "Enter parent id...", "enterLevel": "Enter level...", "enterId": "Enter id...", "enterChildrenCount": "Enter children count...", "nodeDetails": "Node Details", "editNode": "Edit", "deleteNode": "Delete", "nodeUpdatedSuccessfully": "Node updated successfully", "failedToUpdateNode": "Failed to update node", "nodeDeletedSuccessfully": "Node deleted successfully", "failedToDeleteNode": "Failed to delete node", "areYouSureYouWantToDeleteThisNode": "Are you sure you want to delete this node?", "enterTheNodeIdToConfirmDeletion": "Enter the node ID to confirm deletion", "confirmDelete": "Confirm Delete", "name": "Name", "id": "ID", "level": "Level", "parentId": "Main Branch ID", "childrenCount": "Branches Count", "saveChanges": "Save Changes", "cancel": "Cancel", "collapseAll": "Collapse All", "expandAll": "Expand All", "selectParent": "Select Level...", "createNewRoot": "Create New", "nodeAddedSuccessfully": "Unit added successfully", "success": "Unit Structure added successfully!", "deleteSuccess": "Unit Structure deleted successfully!", "failedToAddNode": "Failed to add unit", "toggleCollapseExpand": "Toggle Collapse/Expand", "dragHint": "Drag to add new item", "dragFeedback": "Drop to create organizational unit", "pageHint": "Manage your organizational architecture", "addNewUnit": "Add New Unit", "noOptionsFound": "No options found", "primaryInfo": "Primary Info", "hierarchy": "Hierarchy", "rootLevel": "Root Level"}, "buildings": {"name": "Name", "village": "Village", "selectVillage": "Select village...", "city": "City", "selectCity": "Select city...", "governorate": "Governorate", "selectGovernorate": "Select governorate...", "country": "Country", "selectCountry": "Select country...", "status": "Status", "buildings": "Buildings", "building": "Building", "removeBuildings": "Remove these Buildings", "removeBuilding": "Remove this Building", "confirmRemoveBuildings": "Are you sure you want to remove these Buildings? This action can't be undo.", "confirmRemoveBuilding": "Are you sure you want to remove this Building? This action can't be undo.", "enterName": "Enter name...", "enterCity": "Enter city...", "enterGovernorate": "Enter governorate...", "enterCountry": "Enter country...", "buildingCreated": "Building created successfully!", "buildingUpdated": "Building updated successfully!", "enterVillage": "Enter village...", "villageRequired": "Village is required", "address": "Address", "enterAddress": "Enter address...", "addBuilding": "Add Building", "deleteSelected": "Delete Selected", "confirmDeleteSelected": "Are you sure you want to delete the selected buildings?", "filterByName": "Filter by Name", "selectName": "Select name...", "filterByVillage": "Filter by Village", "filterByCity": "Filter by City", "filterByGovernorate": "Filter by Governorate", "filterByCountry": "Filter by Country"}, "stocks": {"filterByName": "Filter by Name", "selectName": "Select name...", "filterByVillage": "Filter by Village", "selectVillage": "Select village...", "filterByCity": "Filter by City", "selectCity": "Select city...", "filterByGovernorate": "Filter by Governorate", "selectGovernorate": "Select governorate...", "filterByCountry": "Filter by Country", "selectCountry": "Select country...", "filterByManager": "Filter by Manager", "selectManager": "Select manager...", "addStock": "Add Stock"}, "Stock": {"stocks": "Stocks", "stockName": "Stock Name", "stock": "Stock", "stockManager": "Stock Manager", "stockAddress": "Stock Address", "stockMaintenancePhone": "Stock Maintenance Phone", "stockSecurityPhone": "Stock Security Phone", "stockEmail": "Stock Email", "stockDescription": "Stock Description", "stockPhones": "Stock Phones", "enterStockName": "Enter stock name...", "enterStockManager": "Enter stock manager...", "enterStockAddress": "Enter stock address...", "enterStockMaintenancePhone": "Enter stock maintenance phone...", "enterStockSecurityPhone": "Enter stock security phone...", "enterStockEmail": "Enter stock email...", "enterStockDescription": "Enter stock description...", "enterStockPhones": "Enter stock phones...", "enterStockPhone": "Enter stock phone...", "address": "Address", "maintenancePhone": "Maintenance Phone", "securityPhone": "Security Phone", "email": "Email", "description": "Description", "enterStockNotes": "Enter stock notes...", "stockCreated": "Stock created successfully!", "stockUpdated": "Stock updated successfully!", "stockDeleted": "Stock deleted successfully!", "stockNotFound": "Stock not found!", "stockAlreadyExists": "Stock already exists!", "stockNotValid": "Stock is not valid!", "addStock": "Add Stock", "filterByStock": "Filter by Stock", "selectStock": "Select Stock...", "filterByType": "Filter by Type", "selectedStocks": "Selected Stocks", "deleteSelected": "Delete Selected", "confirmDeleteSelected": "Are you sure you want to delete the selected stocks?", "addStockDescription": "Fill in the details to create a new stock", "editStockDescription": "Update the stock information below"}, "systemSecurity": {"users": "Users", "user": "User", "addUser": "Add User", "editUser": "Edit User", "roles": "Roles", "systemSecurity": "System Security", "roleAndPermissions": "Role&Permission", "userCreated": "User created successfully", "userUpdated": "User updated successfully", "userStatusUpdated": "User status updated successfully", "emailAlreadyExists": "Email already exists", "phoneNumberAlreadyInUse": "Phone number already in use", "failedToCreateUser": "Failed to create user", "failedToUpdateUser": "Failed to update user", "unexpectedError": "An unexpected error occurred", "userForm": {"personalInformation": "Personal Information", "contactInformation": "Contact Information", "securityAndAccess": "Security & Access", "firstName": "First Name", "enterFirstName": "Enter first name", "lastName": "Last Name", "enterLastName": "Enter last name", "emailAddress": "Email Address", "enterEmailAddress": "Enter email address", "phoneNumber": "Phone Number", "enterPhoneNumber": "Enter phone number", "organizationalNodes": "Organizational Nodes", "selectOrganizationalNodes": "Select organizational nodes", "errors": {"firstNameRequired": "First name is required", "lastNameRequired": "Last name is required", "invalidEmail": "Please enter a valid email address", "invalidPhone": "Invalid phone number", "selectAtLeastOneOrganization": "Please select at least one organizational node"}}}, "shared": {"clear": "Clear All", "cancel": "Cancel", "selected": "selected", "clearSelection": "Clear Selection", "willBeDeleted": "will be deleted", "willBeDeactivated": "will be deactivated", "confirm": "Confirm", "edit": "Edit", "delete": "Delete", "fromFileNumber": "From File Number", "toFileNumber": "To File Number", "rangePreview": "Range Preview", "fieldRequired": "This field is required", "minValue": "Minimum value is {{value}}", "fromFileMustBeLessThanToFile": "From file must be less than or equal to To file", "toFileMustBeGreaterThanFromFile": "To file must be greater than or equal to From file", "enterFileNumber": "Enter file number", "selectUsers": "Select users", "update": "Update", "create": "Create", "add": "Add", "export": "Export", "search": "Search", "filter": "Filter", "status": "Status", "selectStatus": "Select status...", "customizeColumns": "Customize Columns", "description": "Description", "enterDescription": "Enter description...", "assignedBy": "Assigned By", "createdAt": "Created At", "themeConfig": "Theme Config", "darkMode": "Dark Mode", "switchThemeToDarkMode": "Switch theme to dark mode", "direction": "Direction", "selectDirection": "Select a direction", "theme": "Theme", "layout": "Layout", "errorOccurred": "An error occurred. Please try again.", "id": "ID", "name": "Name", "all": "All", "active": "Approved", "inactive": "UnApproved", "frozen": "Frozen", "clearFilters": "Clear Filters", "manager": "Manager", "actions": "Actions", "total": "Total", "currentView": "Current View", "exportCSV": "Export CSV", "exportJSON": "Export JSON", "deleteStock": "Delete Stock", "confirmDeleteStock": "Are you sure you want to delete this stock?", "editStock": "Edit Stock", "addStock": "Add Stock", "nameRequired": "Name is required", "managerRequired": "Manager is required", "villageRequired": "Village is required", "enterName": "Enter name...", "enterManager": "Enter manager...", "enterAddress": "Enter address...", "enterMaintenancePhone": "Enter maintenance phone...", "enterSecurityPhone": "Enter security phone...", "enterEmail": "Enter email...", "invalidEmail": "Invalid email format", "address": "Address", "maintenancePhone": "Maintenance Phone", "securityPhone": "Security Phone", "email": "Email", "updateStatus": "Update Status", "confirmStatusChange": "Are you sure you want to change the status of {count} stocks to {status}?", "back": "Back", "remove": "Remove", "approve": "Approve", "location": "Location", "loading": "Loading", "phoneNumber": "Phone Number", "notes": "Notes", "addPhone": "Add Phone", "filterByStatus": "Filter by Status", "resetPasswordConfirm": "Reset Password", "confirmResetPassword": "Are you sure you want to reset the password for this user?", "resetPasswordSuccess": "Password has been reset successfully", "willBeReset": "password will be reset", "selectAll": "Select All", "selectOrganizations": "Select Organizations", "noUsersFound": "No users found", "files": "Files", "rangePreviewFormat": "from {{from}} to {{to}} ({{count}} {{files}})", "save": "Save"}, "Form": {"name": "Name", "enterName": "Enter name...", "nameRequired": "Name is required!", "email": "Email", "enterEmail": "Enter email...", "invalidEmail": "Invalid email format!", "address": "Address", "enterAddress": "Enter address...", "addressRequired": "Address is required!", "phone": "Phone", "enterPhone": "Enter phone...", "phoneRequired": "Phone is required!", "place": "Place", "enterPlace": "Enter place...", "placeRequired": "Place is required!", "villageRequired": "Village is required!", "managerRequired": "Manager is required!"}, "GlobalActions": {"edit": "Edit", "delete": "Delete", "remove": "Remove", "view": "View", "create": "Create", "add": "Add", "export": "Export", "back": "Back", "search": "Search", "approve": "Approve", "frozen": "Freeze"}, "assets": {"title": "Coming Soon .....", "description": "This feature will be added soon"}, "warehouses": {"warehouse": "Warehouse", "warehouses": "Warehouses", "organization": "Organization", "addWarehouse": "Add Warehouse", "area": "Area", "areas": "Areas", "unit": "Unit", "units": "Units", "roof": "<PERSON><PERSON>", "roofs": "Roofs", "shelves": "<PERSON><PERSON>", "createWarehouse": "Create Warehouse", "createWarehouseDesc": "Fill in the details to create a new warehouse", "warehouseInformation": "Warehouse Information", "fillWarehouseDetails": "Fill in the warehouse details", "basicInformation": "Basic Information", "organizationalNodeCode": "Organizational Node Code", "enterOrgCode": "Enter organizational code...", "warehouseName": "Warehouse Name", "enterWarehouseName": "Enter warehouse name...", "districtOrVillageCode": "District or Village Code", "enterDistrictCode": "Enter district code...", "responsiblePerson": "Responsible Person", "enterResponsiblePerson": "Enter responsible person...", "locationInformation": "Location Information", "detailedAddress": "Detailed Address", "enterDetailedAddress": "Enter detailed address...", "contactInformation": "Contact Information", "securityPhone": "Security Phone", "enterSecurityPhone": "Enter security phone...", "maintenancePhone": "Maintenance Phone", "enterMaintenancePhone": "Enter maintenance phone...", "additionalInformation": "Additional Information", "notes": "Notes", "enterNotes": "Enter notes...", "updateWarehouseDetails": "Update Warehouse Details", "updateSuccess": "Warehouse details updated successfully", "manageAreas": "Manage Areas", "manageAreasDesc": "Organize and manage warehouse areas efficiently", "totalAreas": "Total Areas", "totalUnits": "Total Units", "totalRoofs": "Total Roofs", "totalBoxes": "Total Boxes", "availableBoxes": "Available Boxes", "dimensions": "Dimensions", "areasList": "Areas List", "areasListDesc": "Click on any area to manage its units and roofs", "addArea": "Add Area", "noAreas": "No Areas Found", "noAreasDesc": "Start by adding your first area to organize this warehouse", "addFirstArea": "Add First Area", "manageUnits": "Manage Units", "manageUnitsInArea": "Manage and organize units within this area", "manageShelvesInUnit": "Manage and organize shelves within this unit", "manageShelves": "Manage Shelves", "manageBoxes": "Manage Boxes", "shelf": "<PERSON><PERSON>", "totalShelves": "Total Shelves", "occupiedBoxes": "Occupied Boxes", "utilized": "Utilized", "capacity": "Capacity", "addShelf": "<PERSON><PERSON>", "editShelf": "<PERSON>", "editShelfDescription": "Update the shelf dimensions and configuration", "deleteShelf": "Delete Shelf", "confirmDeleteShelf": "Are you sure you want to delete this shelf? This action cannot be undone.", "noShelves": "No Shelves Found", "noShelvesDesc": "Start by adding your first shelf to organize this unit", "addFirstShelf": "Add First Shelf", "shelfDetails": "<PERSON><PERSON>", "unitDetails": "Unit Details", "backToUnits": "Back to Units", "backToShelves": "Back to Shelves", "editUnit": "Edit Unit", "editUnitDescription": "Update the unit dimensions", "deleteUnit": "Delete Unit", "confirmDeleteUnit": "Are you sure you want to delete this unit? This action cannot be undone.", "addUnit": "Add Unit", "noUnits": "No Units Found", "noUnitsDesc": "Start by adding your first unit to organize this area", "addFirstUnit": "Add First Unit", "shelfAddedSuccessfully": "<PERSON><PERSON> added successfully", "errorAddingShelf": "Error adding shelf", "shelfUpdatedSuccessfully": "<PERSON><PERSON> updated successfully", "errorUpdatingShelf": "Error updating shelf", "shelfDeletedSuccessfully": "<PERSON><PERSON> deleted successfully", "errorDeletingShelf": "Error deleting shelf", "deleteArea": "Delete Area", "deleteAreaConfirm": "Are you sure you want to delete this {{name}}", "deleteAreaWarning": "This action cannot be undone and will remove all associated units and shelves.", "deleteWarehouse": "Delete Warehouse", "confirmDeleteWarehouse": "Are you sure you want to delete this warehouse? This action cannot be undone.", "deleteSelectedWarehouses": "Delete Selected Warehouses", "confirmDeleteSelectedWarehouses": "Are you sure you want to delete {{count}} selected warehouses? This action cannot be undone.", "configureWarehouseLayout": "Configure Warehouse Layout", "configureWarehouseLayoutDesc": "Set up areas, units, shelves and boxes configuration", "areasConfiguration": "Areas Configuration", "unitsConfiguration": "Units Configuration", "shelvesConfiguration": "Shelves Configuration", "numberOfAreas": "Number of Areas", "areaLength": "Area Length (m)", "areaWidth": "Area Width (m)", "numberOfUnitsPerArea": "Units per Area", "unitLength": "Unit Length (m)", "unitWidth": "Unit Width (m)", "numberOfShelvesPerUnit": "Shelves per Unit", "shelfLength": "Shelf Length (m)", "shelfWidth": "<PERSON><PERSON> (m)", "numberOfBoxesPerShelf": "Boxes per Shelf", "numberOfAreasRequired": "Number of areas is required", "areaLengthRequired": "Area length is required", "areaWidthRequired": "Area width is required", "numberOfUnitsPerAreaRequired": "Number of units per area is required", "unitLengthRequired": "Unit length is required", "unitWidthRequired": "Unit width is required", "numberOfShelvesPerUnitRequired": "Number of shelves per unit is required", "shelfLengthRequired": "Shelf length is required", "shelfWidthRequired": "Shelf width is required", "numberOfBoxesPerShelfRequired": "Number of boxes per shelf is required", "configureUnits": "Configure Units", "configureUnitsDesc": "Set up units and shelves configuration", "configureShelves": "Configure Shelves", "configureShelvesDesc": "Set up shelves and boxes configuration", "numberOfUnits": "Number of Units", "numberOfShelves": "Number of Shelves", "numberOfBoxes": "Number of Boxes", "shelfHeight": "Shelf Height (m)", "maxBoxes": "Maximum Boxes", "editArea": "Edit Area", "editAreaDesc": "Update area dimensions", "editUnitDesc": "Update unit dimensions", "editShelfDesc": "Update shelf configuration", "areaDimensions": "Area Dimensions", "unitDimensions": "Unit Dimensions", "shelfConfiguration": "Shelf Configuration", "numberOfUnitsRequired": "Number of units is required", "numberOfShelvesRequired": "Number of shelves is required", "numberOfBoxesRequired": "Number of boxes is required", "shelfHeightRequired": "Shelf height is required", "maxBoxesRequired": "Maximum boxes is required"}, "digitization": {"digitization": "Digitization", "daily": "Daily", "station": "Station"}, "File": {"fileId": "File ID", "fileTitle": "File Title", "Confidentiality": "Confidentiality", "digitizationStation": "Digitization Station", "category": "Category", "organizationalNode": "Organizational Node", "digitizationType": {"daily": "Daily", "backlog": "Backlog"}, "status": "Status", "MediumType": "Medium Type", "usersWithAccess": "Users with Access", "createdBy": "Created By", "createdAt": "Created At", "file": "File", "files": "Files", "addFile": "Add File", "confidentiality": {"nonConfidential": "Non-Confidential", "confidential": "Confidential", "highlyConfidential": "Highly Confidential"}, "mediumType": {"paperBased": "Paper Based", "electronicWithoutPhysical": "Electronic Without Physical Copy", "electronicWithPhysical": "Electronic With Physical Copy"}}, "files": {"file": "File", "files": "Files", "filesList": "Files List", "filesListDesc": "Manage and organize your digitized files", "addFile": "Add File", "addFirstFile": "Add First File", "noFiles": "No Files Found", "noFilesDesc": "Start by adding your first file to this station", "fileTitle": "File Title", "confidentiality": "Confidentiality", "digitizationStation": "Digitization Station", "category": "Category", "organizationalNode": "Organizational Node", "digitizationType": "Digitization Type", "mediumType": "Medium Type", "fileStatus": "File Status", "createdBy": "Created By", "createdAt": "Created At", "updatedBy": "Updated By", "updatedAt": "Updated At", "draft": "Draft", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "archived": "Archived", "public": "Public", "confidential": "Confidential", "secret": "Secret", "topSecret": "Top Secret", "scan": "<PERSON><PERSON>", "photo": "Photo", "digital": "Digital", "paper": "Paper", "microfilm": "Microfilm", "cdDvd": "CD/DVD", "usb": "USB", "unknown": "Unknown", "station": "Station"}, "dailyDigitization": {"dailyDigitization": "Daily Digitization", "approvedDevices": "Approved Devices", "digitizationStations": "Digitization Stations", "calculationOfDailyDigitizationRequirements": "Calculation of Daily Digitization Requirements", "numberOfFiles": "Number of Files", "numberOfDocuments": "Number of Documents", "numberOfPages": "Number of Pages", "searchByFileCode": "Search by File Code", "enterFileCode": "Enter file code...", "searchByFileTitle": "Search by File Title", "enterFileTitle": "Enter file title", "fileCode": "File Code", "administration": "Administration", "fileTitle": "File Title", "document": "Document", "documents": "Documents", "page": "Page", "pages": "Pages", "files": "Files", "mediaType": "Media Type", "patchCode": "Patch Code", "fileStatus": "File Status", "control": "Control", "addFile": "Add File", "printBarcode": "Print Barcode", "closeFiles": "Close Files", "changePermissions": "Change Permissions", "deleteFiles": "Delete Files", "closeFile": "Close File", "viewDocument": "View Document", "print": "Print", "searchResults": "Search Results", "noResultsFound": "No results found", "showingResults": "Showing {{count}} results", "addFileDescription": "Add a new file to the system with proper classification and permissions", "confidentiality": "Confidentiality", "selectConfidentiality": "Select confidentiality level", "classification": "Classification", "selectClassification": "Select classification", "selectMediaType": "Select media type...", "filePermissions": "File Permissions", "searchPeople": "Search people...", "noPeopleFound": "No people found", "documentsInFile": "Documents in File", "addDocument": "Add Document", "addDocumentDescription": "Add a new document to the file with proper classification", "generalCategory": "General Category", "selectGeneralCategory": "Select general category...", "date": "Date", "day": "Day", "month": "Month", "year": "Year", "selectDay": "Day", "selectMonth": "Month", "selectYear": "Year", "documentType": "Document Type", "selectDocumentType": "Select document type...", "enterDocumentTitle": "Enter document title...", "documentCode": "Document Code", "boxCode": "Box Code", "documentStatus": "Document Status", "searchDocuments": "Search Documents", "enterDocumentSearchTerm": "Search by code, title, administration, or status...", "searchDocumentsHint": "Search across document code, title, administration, box code, or status", "showingDocuments": "Showing {{count}} documents", "noDocumentsFound": "No documents found", "selectedDocuments": "{{count}} documents selected", "printBarcodeForDocuments": "Print Barcode for Documents", "printDigitizationForm": "Print Digitization Form", "settings": "Settings", "delete": "Delete", "review": "Review", "filter": "Filter", "clearFilters": "Clear Filters", "applyFilters": "Apply Filters", "visibleColumns": "Visible Columns", "all": "All", "total": "Total", "confirmDelete": "Confirm Delete", "back": "Back", "search": "Search", "documentTitle": "Document Title", "viewDocuments": "View Documents", "filterByAdministration": "Filter by Administration", "selectAdministration": "Select Administration", "filterByMediaType": "Filter by Media Type", "filterByFileStatus": "Filter by File Status", "selectFileStatus": "Select File Status", "filterByDocumentStatus": "Filter by Document Status", "selectDocumentStatus": "Select Document Status", "confirmCloseFile": "Are you sure you want to close this file?", "confirmDeleteDocument": "Are you sure you want to delete this document?", "basicInfo": "Basic Information", "documentDetails": "Document Details", "adminInfo": "Administrative Information", "additionalInfo": "Additional Information", "documentBarcode": "Document Barcode", "enterDocumentBarcode": "Enter document barcode", "fileBarcode": "File Barcode", "enterFileBarcode": "Enter file barcode", "fileReference": "File Reference", "enterFileReference": "Enter file reference", "fileConfidentiality": "File Confidentiality", "selectDate": "Select date", "serialNumber": "Serial Number", "enterSerialNumber": "Enter serial number", "keywords": "Keywords", "enterKeywords": "Enter keywords (separated by commas)", "submissionDate": "Submission Date", "selectSubmissionDate": "Select submission date", "responsibleDepartment": "Responsible Department", "selectDepartment": "Select department", "attachments": "Attachments", "uploadTip": "Maximum 5 files, each up to 10MB", "dragDropFiles": "Drag and drop files here or click to browse", "supportedFormats": "Supported formats: PDF, DOC, DOCX, JPG, JPEG, PNG", "notes": "Notes", "enterNotes": "Enter additional notes or comments", "requiredFieldsNote": "Fields marked with * are required", "viewDocumentInfo": "View comprehensive document information and details", "noAttachments": "No attachments available", "printSelected": "Print Selected", "deleteSelected": "Delete Selected", "exportSelected": "Export Selected", "printDocument": "Print Document", "printMultipleDocuments": "Print Multiple Documents", "printPreview": "Print Preview", "printPreviewMultiple": "Multiple Print Preview", "printPreviewDesc": "Preview document before printing", "printPreviewMultipleDesc": "Preview {{count}} documents before printing", "printPreviewNote": "Print Preview - Check settings before printing", "printDirect": "Print Direct", "printAllDocuments": "Print All Documents", "andMoreDocuments": "and {{count}} more documents...", "documentPrintTitle": "Document Print Sheet", "documentPrintSubtitle": "Electronic Archiving System", "documentNumber": "Document Number", "issuingEntity": "Issuing Entity", "printedOn": "Printed on", "systemName": "Electronic Archiving System", "confidentiality.public": "Public", "confidentiality.internal": "Internal", "confidentiality.confidential": "Confidential", "confidentiality.restricted": "Restricted", "confidentiality.top-secret": "Top Secret", "classification.normal": "Normal", "classification.important": "Important", "classification.urgent": "<PERSON><PERSON>", "classification.critical": "Critical", "department.finance": "Finance Department", "department.hr": "Human Resources", "department.legal": "Legal Affairs", "department.it": "Information Technology", "department.admin": "General Administration", "department.operations": "Operations", "printFile": "Print File", "printMultipleFiles": "Print Multiple Files", "filePrintTitle": "File Print Sheet", "filePrintSubtitle": "Electronic Archiving System", "fileNumber": "File Number", "filePrintPreview": "File Print Preview", "filePrintPreviewMultiple": "Multiple File Print Preview", "filePrintPreviewDesc": "Preview file before printing", "filePrintPreviewMultipleDesc": "Preview {{count}} files before printing", "printAllFiles": "Print All Files", "andMoreFiles": "and {{count}} more files..."}, "digitizationStations": {"stations": "Digitization Stations", "station": "Digitization Station", "addStation": "Add Digitization Station", "editStation": "Edit Digitization Station", "deleteStation": "Delete Digitization Station", "confirmDeleteStation": "Are you sure you want to delete this digitization station?", "addDigitizationStationDescription": "Fill in the details to create a new digitization station", "editDigitizationStationDescription": "Update the digitization station information below", "organization": "Organizations", "organizations": "Organizations", "users": "Users", "numberOfOrganizations": "Number of Organizations", "numberOfUsers": "Number of Users", "creationDate": "Creation Date", "buildingName": "Building Name", "buildingAddress": "Building Address", "date": "Date", "project": "Project", "building": "Building", "address": "Address", "status": "Status", "dailyTargetsMustNotBeEmpty": "Daily targets must not be empty", "paperSizeSelectionRequired": "Paper size selection is required", "mediumTypeSelectionRequired": "Medium type selection is required", "expectedCountMustBeGreaterOrEqual": "Expected count must be 0 or greater", "pleaseSelectBuilding": "Please select a building", "selectAtLeastOneOrganization": "Select at least one organization", "expectedDailyTarget": "Expected Daily Target", "paperSize": "Paper Size", "mediumType": "Medium Type", "expectedDailyCount": "Expected Daily Count", "addTarget": "Add Target", "selectBuilding": "Select building...", "paperBased": "Paper-based", "electronicWithoutPhysicalCopy": "Electronic without physical copy", "electronicWithPhysicalCopy": "Electronic with physical copy"}}, "common": {"cancel": "Cancel", "confirm": "Confirm", "save": "Save", "close": "Close", "ok": "OK", "select": "Select..."}}