import authRoute from './authRoute'
import menusRoute from './menusRoute'

import type { Routes } from '@/@types/routes'
import systemSecurityRoute from './systemSecurityRoute'
import warehouseRoute from './warehouseItemsRoute'
import digitizationRoute from './digitizationRoute'
import othersRoute from './othersRoute'
import boxesRoute from './boxesRoute'

export const publicRoutes: Routes = [...authRoute]

export const protectedRoutes: Routes = [
    ...menusRoute,
    ...systemSecurityRoute,
    ...warehouseRoute,
    ...digitizationRoute,
    ...boxesRoute,
    ...othersRoute,
]
