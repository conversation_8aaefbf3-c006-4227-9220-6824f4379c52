import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import Button from '@/components/ui/Button'
import ConfirmDialog from '@/components/shared/ConfirmDialog'
import useTranslation from '@/utils/hooks/useTranslation'
import { TbEdit, TbTrash, TbStack3, TbBox } from 'react-icons/tb'
import type { Unit } from '@/@types/warehouse'

interface UnitCardProps {
    unit: Unit
    warehouseId: string
    areaId: string
    onEdit: (unitId: string) => void
    onDelete: (unitId: string) => void
    onManageShelves: (unitId: string) => void
}

const UnitCard = ({
    unit,
    warehouseId,
    areaId,
    onEdit,
    onDelete,
    // onManageShelves,
}: UnitCardProps) => {
    const { t } = useTranslation()
    const navigate = useNavigate()
    const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false)

    const utilizationPercentage =
        unit.numberOfBoxes > 0
            ? Math.round(
                  ((unit.numberOfBoxes - unit.availableBoxes) /
                      unit.numberOfBoxes) *
                      100,
              )
            : 0

    const getUtilizationColor = (percentage: number) => {
        if (percentage >= 90) return 'text-red-600 bg-red-50 dark:bg-red-900/20'
        if (percentage >= 70)
            return 'text-yellow-600 bg-yellow-50 dark:bg-yellow-900/20'
        return 'text-green-600 bg-green-50 dark:bg-green-900/20'
    }

    const handleManageShelves = () => {
        navigate(
            `/warehouses/${warehouseId}/areas/${areaId}/units/${unit.id}/shelves`,
            {
                state: { unitName: `Unit ${unit.id}` },
            },
        )
    }

    return (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-200 overflow-hidden">
            {/* Header */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-4 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        <div className="flex items-center justify-center w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                            <TbBox className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                        </div>
                        <div>
                            <h3 className="font-semibold text-gray-900 dark:text-gray-100">
                                Unit {unit.id}
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                                {unit.width}m × {unit.height}m
                            </p>
                        </div>
                    </div>
                    <div
                        className={`px-3 py-1 rounded-full text-xs font-medium ${getUtilizationColor(utilizationPercentage)}`}
                        dir="auto"
                        style={{ fontFamily: 'inherit' }}
                    >
                        {utilizationPercentage}% {t('nav.warehouses.utilized')}
                    </div>
                </div>
            </div>

            {/* Content */}
            <div className="p-4">
                {/* Stats Grid */}
                <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="text-center p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                        <div className="flex items-center justify-center w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-lg mx-auto mb-2">
                            <TbStack3 className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                        </div>
                        <div className="text-lg font-bold text-gray-900 dark:text-gray-100">
                            {unit.numberOfShelves}
                        </div>
                        <div
                            className="text-xs text-gray-600 dark:text-gray-400"
                            dir="auto"
                        >
                            {t('nav.warehouses.shelves')}
                        </div>
                    </div>
                    <div className="text-center p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                        <div className="flex items-center justify-center w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-lg mx-auto mb-2">
                            <TbBox className="w-4 h-4 text-green-600 dark:text-green-400" />
                        </div>
                        <div className="text-lg font-bold text-gray-900 dark:text-gray-100">
                            {unit.availableBoxes}/{unit.numberOfBoxes}
                        </div>
                        <div
                            className="text-xs text-gray-600 dark:text-gray-400"
                            dir="auto"
                        >
                            {t('nav.warehouses.availableBoxes')}
                        </div>
                    </div>
                </div>

                {/* Capacity Bar */}
                <div className="mb-4">
                    <div className="flex justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
                        <span dir="auto">{t('nav.warehouses.capacity')}</span>
                        <span dir="auto">{utilizationPercentage}%</span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div
                            className={`h-2 rounded-full transition-all duration-300 ${
                                utilizationPercentage >= 90
                                    ? 'bg-red-500'
                                    : utilizationPercentage >= 70
                                      ? 'bg-yellow-500'
                                      : 'bg-green-500'
                            }`}
                            style={{ width: `${utilizationPercentage}%` }}
                        />
                    </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2">
                    <Button
                        size="sm"
                        variant="solid"
                        className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
                        icon={<TbStack3 />}
                        onClick={handleManageShelves}
                    >
                        {t('nav.warehouses.manageShelves')}
                    </Button>
                    <Button
                        size="sm"
                        className="text-gray-600 hover:text-gray-700"
                        icon={<TbEdit />}
                        onClick={() => onEdit(unit.id)}
                    >
                        {t('nav.shared.edit')}
                    </Button>
                    <Button
                        size="sm"
                        className="text-red-600 hover:text-red-700"
                        icon={<TbTrash />}
                        onClick={() => setDeleteConfirmOpen(true)}
                    >
                        {t('nav.shared.delete')}
                    </Button>
                </div>
            </div>

            {/* Delete Confirmation Dialog */}
            <ConfirmDialog
                isOpen={deleteConfirmOpen}
                type="danger"
                title={t('nav.warehouses.deleteUnit')}
                onClose={() => setDeleteConfirmOpen(false)}
                onRequestClose={() => setDeleteConfirmOpen(false)}
                onCancel={() => setDeleteConfirmOpen(false)}
                onConfirm={() => {
                    onDelete(unit.id)
                    setDeleteConfirmOpen(false)
                }}
            >
                <p className="text-gray-600 dark:text-gray-400">
                    {t('nav.warehouses.confirmDeleteUnit')}
                </p>
            </ConfirmDialog>
        </div>
    )
}

export default UnitCard
