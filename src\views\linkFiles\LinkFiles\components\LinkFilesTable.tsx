import { useMemo, useState } from 'react'
import { useSearchParams } from 'react-router-dom'
import DataTable from '@/components/shared/DataTable'
import type { ColumnDef } from '@/components/shared/DataTable'
import useTranslation from '@/utils/hooks/useTranslation'
import { File } from '@/@types/file'
import { useGetFilesForLinking } from '@/hooks/files'
import { useLinkFilesToBox } from '@/hooks/boxes'
import { formatDateDDMMYY } from '@/utils/dateUtils'
import { toast } from '@/components/ui'
import LinkFilesTableTools from './LinkFilesTableTools'

const LinkFilesTable = ({
    organizationalNodeId,
}: {
    organizationalNodeId: string
}) => {
    const { t } = useTranslation()
    const [searchParams, setSearchParams] = useSearchParams()
    const [selectedRows, setSelectedRows] = useState<string[]>([])

    const pageNumber = parseInt(searchParams.get('pageNumber') || '1')
    const pageSize = parseInt(searchParams.get('pageSize') || '10')

    const { Files, isLoading, pagination } = useGetFilesForLinking()
    const linkFilesToBoxMutation = useLinkFilesToBox()

    const handlePaginationChange = (page: number) => {
        const newSearchParams = new URLSearchParams(searchParams)
        newSearchParams.set('pageNumber', page.toString())
        setSearchParams(newSearchParams)
    }

    const handlePageSizeChange = (pageSize: number) => {
        const newSearchParams = new URLSearchParams(searchParams)
        newSearchParams.set('pageSize', pageSize.toString())
        newSearchParams.set('pageNumber', '1')
        setSearchParams(newSearchParams)
    }

    const handleLinkFiles = (boxId: string, fileIds: string[]) => {
        linkFilesToBoxMutation.mutate(
            { boxId, fileIds },
            {
                onSuccess: () => {
                    toast.push(
                        {
                            title: t('nav.linkFiles.success'),
                            message: t('nav.linkFiles.filesLinkedSuccessfully'),
                            type: 'success',
                        },
                        {
                            placement: 'top-center',
                        }
                    )
                    setSelectedRows([])
                },
                onError: (error) => {
                    toast.push(
                        {
                            title: t('nav.shared.error'),
                            message: error.message || t('nav.linkFiles.linkingFailed'),
                            type: 'danger',
                        },
                        {
                            placement: 'top-center',
                        }
                    )
                },
            }
        )
    }

    const getFileStatusLabel = (status: number) => {
        switch (status) {
            case 0:
                return {
                    label: t('nav.files.status.draft'),
                    className: 'bg-gray-100 text-gray-800 border-gray-200',
                }
            case 1:
                return {
                    label: t('nav.files.status.active'),
                    className: 'bg-green-100 text-green-800 border-green-200',
                }
            case 2:
                return {
                    label: t('nav.files.status.closed'),
                    className: 'bg-red-100 text-red-800 border-red-200',
                }
            default:
                return {
                    label: t('nav.files.status.unknown'),
                    className: 'bg-gray-100 text-gray-800 border-gray-200',
                }
        }
    }

    const tableColumns: ColumnDef<File>[] = useMemo(() => {
        const cols: ColumnDef<File>[] = [
            {
                header: t('nav.files.fileId'),
                accessorKey: 'fileId',
                cell: ({ row }) => (
                    <div className="font-mono text-sm">
                        {row.original.fileId}
                    </div>
                ),
            },
            {
                header: t('nav.files.title'),
                accessorKey: 'title',
                cell: ({ row }) => (
                    <div className="font-medium">
                        {row.original.title}
                    </div>
                ),
            },
            {
                header: t('nav.files.category'),
                accessorKey: 'categoryName',
                cell: ({ row }) => (
                    <div className="text-sm">
                        {row.original.categoryName || 'N/A'}
                    </div>
                ),
            },
            {
                header: t('nav.files.status'),
                accessorKey: 'fileStatus',
                cell: ({ row }) => {
                    const status = getFileStatusLabel(row.original.fileStatus)
                    return (
                        <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${status?.className}`}
                        >
                            {status?.label}
                        </span>
                    )
                },
                enableSorting: false,
            },
            {
                header: t('nav.files.confidentiality'),
                accessorKey: 'confidentiality',
                cell: ({ row }) => (
                    <div className="text-sm">
                        {row.original.confidentiality}
                    </div>
                ),
            },
            {
                header: t('nav.shared.createdBy'),
                accessorKey: 'createdByUsername',
                cell: ({ row }) => (
                    <div className="text-sm">
                        {row.original.createdByUsername}
                    </div>
                ),
            },
            {
                header: t('nav.shared.createdAt'),
                accessorKey: 'createdAt',
                cell: ({ row }) => (
                    <div className="text-sm text-gray-600">
                        {formatDateDDMMYY(row.original.createdAt)}
                    </div>
                ),
            },
        ]

        return cols
    }, [t])

    return (
        <>
            <LinkFilesTableTools
                selectedCount={selectedRows.length}
                selectedFileIds={selectedRows}
                onLinkFiles={handleLinkFiles}
            />
            <DataTable
                selectable
                columns={tableColumns}
                data={Files || []}
                loading={isLoading}
                skeletonAvatarColumns={[0]}
                skeletonAvatarProps={{ width: 14, height: 14 }}
                cellBorder={true}
                pagingData={{
                    total: pagination?.totalCount || 0,
                    pageIndex: pagination?.pageNumber || 1,
                    pageSize: pagination?.pageSize || 10,
                }}
                onPaginationChange={handlePaginationChange}
                onSelectChange={handlePageSizeChange}
                checkboxSelection
                onSelectionChange={setSelectedRows}
                selectedRows={selectedRows}
            />
        </>
    )
}

export default LinkFilesTable