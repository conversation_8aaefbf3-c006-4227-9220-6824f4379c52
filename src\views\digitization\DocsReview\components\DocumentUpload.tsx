import React, { useState, useCallback } from 'react'
import { useTranslation } from 'react-i18next'
import { <PERSON>, Button } from '@/components/ui'
import { TbUpload, TbFile, TbX, TbCheck, TbAlertCircle } from 'react-icons/tb'

interface DocumentUploadProps {
    onFileUpload?: (file: File) => void
    onFileRemove?: () => void
    acceptedTypes?: string[]
    maxSize?: number // in MB
    disabled?: boolean
}

export default function DocumentUpload({
    onFileUpload,
    onFileRemove,
    acceptedTypes = ['.pdf', '.doc', '.docx', '.txt', '.jpg', '.jpeg', '.png'],
    maxSize = 10,
    disabled = false
}: DocumentUploadProps) {
    const { t } = useTranslation()
    const [dragActive, setDragActive] = useState(false)
    const [uploadedFile, setUploadedFile] = useState<File | null>(null)
    const [uploadStatus, setUploadStatus] = useState<'idle' | 'uploading' | 'success' | 'error'>('idle')
    const [errorMessage, setErrorMessage] = useState<string>('')

    const validateFile = (file: File): boolean => {
        // Check file size
        if (file.size > maxSize * 1024 * 1024) {
            setErrorMessage(t('upload.fileTooLarge', { maxSize }))
            return false
        }

        // Check file type
        const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase()
        if (!acceptedTypes.includes(fileExtension)) {
            setErrorMessage(t('upload.invalidFileType', { types: acceptedTypes.join(', ') }))
            return false
        }

        return true
    }

    const handleFile = useCallback((file: File) => {
        setErrorMessage('')
        
        if (!validateFile(file)) {
            setUploadStatus('error')
            return
        }

        setUploadedFile(file)
        setUploadStatus('uploading')

        // Simulate upload process
        setTimeout(() => {
            setUploadStatus('success')
            onFileUpload?.(file)
        }, 1500)
    }, [onFileUpload, maxSize, acceptedTypes])

    const handleDrag = useCallback((e: React.DragEvent) => {
        e.preventDefault()
        e.stopPropagation()
        if (e.type === 'dragenter' || e.type === 'dragover') {
            setDragActive(true)
        } else if (e.type === 'dragleave') {
            setDragActive(false)
        }
    }, [])

    const handleDrop = useCallback((e: React.DragEvent) => {
        e.preventDefault()
        e.stopPropagation()
        setDragActive(false)

        if (disabled) return

        const files = Array.from(e.dataTransfer.files)
        if (files.length > 0) {
            handleFile(files[0])
        }
    }, [handleFile, disabled])

    const handleFileInput = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(e.target.files || [])
        if (files.length > 0) {
            handleFile(files[0])
        }
    }, [handleFile])

    const handleRemoveFile = () => {
        setUploadedFile(null)
        setUploadStatus('idle')
        setErrorMessage('')
        onFileRemove?.()
    }

    const formatFileSize = (bytes: number): string => {
        if (bytes === 0) return '0 Bytes'
        const k = 1024
        const sizes = ['Bytes', 'KB', 'MB', 'GB']
        const i = Math.floor(Math.log(bytes) / Math.log(k))
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    const getStatusIcon = () => {
        switch (uploadStatus) {
            case 'uploading':
                return <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
            case 'success':
                return <TbCheck className="text-green-600 text-xl" />
            case 'error':
                return <TbAlertCircle className="text-red-600 text-xl" />
            default:
                return <TbFile className="text-gray-400 text-xl" />
        }
    }

    return (
        <Card className="p-6">
            <div className="mb-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {t('upload.documentUpload')}
                </h3>
                <p className="text-sm text-gray-600">
                    {t('upload.supportedFormats', { formats: acceptedTypes.join(', '), maxSize })}
                </p>
            </div>

            {!uploadedFile ? (
                <div
                    className={`
                        relative border-2 border-dashed rounded-lg p-8 text-center transition-colors
                        ${dragActive 
                            ? 'border-blue-400 bg-blue-50' 
                            : 'border-gray-300 hover:border-gray-400'
                        }
                        ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                    `}
                    onDragEnter={handleDrag}
                    onDragLeave={handleDrag}
                    onDragOver={handleDrag}
                    onDrop={handleDrop}
                    onClick={() => !disabled && document.getElementById('file-upload')?.click()}
                >
                    <input
                        id="file-upload"
                        type="file"
                        className="hidden"
                        accept={acceptedTypes.join(',')}
                        onChange={handleFileInput}
                        disabled={disabled}
                    />
                    
                    <TbUpload className="mx-auto text-4xl text-gray-400 mb-4" />
                    <p className="text-lg font-medium text-gray-900 mb-2">
                        {t('upload.dropFiles')}
                    </p>
                    <p className="text-sm text-gray-600 mb-4">
                        {t('upload.orClickToSelect')}
                    </p>
                    
                    <Button
                        variant="solid"
                        size="sm"
                        disabled={disabled}
                        onClick={(e) => {
                            e.stopPropagation()
                            document.getElementById('file-upload')?.click()
                        }}
                    >
                        {t('upload.selectFile')}
                    </Button>
                </div>
            ) : (
                <div className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                            {getStatusIcon()}
                            <div>
                                <p className="font-medium text-gray-900">{uploadedFile.name}</p>
                                <p className="text-sm text-gray-500">
                                    {formatFileSize(uploadedFile.size)}
                                </p>
                            </div>
                        </div>
                        
                        <Button
                            variant="plain"
                            size="sm"
                            onClick={handleRemoveFile}
                            disabled={uploadStatus === 'uploading'}
                        >
                            <TbX className="text-lg" />
                        </Button>
                    </div>

                    {uploadStatus === 'uploading' && (
                        <div className="mt-3">
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div className="bg-blue-600 h-2 rounded-full animate-pulse" style={{ width: '60%' }}></div>
                            </div>
                            <p className="text-sm text-gray-600 mt-1">{t('upload.uploading')}</p>
                        </div>
                    )}

                    {uploadStatus === 'success' && (
                        <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-md">
                            <p className="text-sm text-green-800">{t('upload.uploadSuccess')}</p>
                        </div>
                    )}

                    {uploadStatus === 'error' && (
                        <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-md">
                            <p className="text-sm text-red-800">{errorMessage}</p>
                        </div>
                    )}
                </div>
            )}

            {uploadStatus !== 'error' && uploadedFile && (
                <div className="mt-4 flex justify-end">
                    <Button
                        variant="solid"
                        size="sm"
                        disabled={uploadStatus !== 'success'}
                        onClick={() => {
                            // Handle replace file
                            handleRemoveFile()
                        }}
                    >
                        {t('upload.replaceFile')}
                    </Button>
                </div>
            )}
        </Card>
    )
}
