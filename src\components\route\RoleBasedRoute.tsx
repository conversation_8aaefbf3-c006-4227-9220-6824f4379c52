import { PropsWithChildren } from 'react'
import { Navigate } from 'react-router-dom'
import { USER, ADMIN } from '@/constants/roles.constant'
import appConfig from '@/configs/app.config'

const { authenticatedEntryPath, userAuthenticatedPath } = appConfig

type RoleBasedRouteProps = PropsWithChildren<{
    requiredAuthority?: string[]
}>

const RoleBasedRoute = ({
    children,
    requiredAuthority,
}: RoleBasedRouteProps) => {
    // Get roles from localStorage
    const getRoles = (): string[] => {
        try {
            const roles = localStorage.getItem('roles')
            return roles ? JSON.parse(roles) : []
        } catch {
            return []
        }
    }

    const userRoles = getRoles()

    // If this is for a specific route with authority requirements
    if (requiredAuthority && requiredAuthority.length > 0) {
        const hasAccess = requiredAuthority.some((role) =>
            userRoles.includes(role),
        )

        if (!hasAccess) {
            return <Navigate replace to="/access-denied" />
        }

        return <>{children}</>
    }

    // This is for root route redirection
    const getEntryPath = () => {
        // Check if user has USER role
        if (userRoles.includes(USER)) {
            return userAuthenticatedPath
        }

        // Check if user has ADMIN role
        if (userRoles.includes(ADMIN)) {
            return authenticatedEntryPath
        }

        // Default fallback
        return authenticatedEntryPath
    }

    return <Navigate replace to={getEntryPath()} />
}

export default RoleBasedRoute
