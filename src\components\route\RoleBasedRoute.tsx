import { PropsWithChildren } from 'react'
import { Navigate } from 'react-router-dom'
import { USER } from '@/constants/roles.constant'
import appConfig from '@/configs/app.config'

const { authenticatedEntryPath, userAuthenticatedPath } = appConfig

type RoleBasedRouteProps = PropsWithChildren<{
    requiredAuthority?: string[]
}>

const RoleBasedRoute = ({
    children,
    requiredAuthority,
}: RoleBasedRouteProps) => {
    const roles = ['Employee']

    // If this is for a specific route with authority requirements
    if (requiredAuthority && requiredAuthority.length > 0) {
        const userAuthority = roles
        const hasAccess = requiredAuthority.some((role) =>
            userAuthority.includes(role),
        )

        if (!hasAccess) {
            return <Navigate replace to="/access-denied" />
        }

        return <>{children}</>
    }

    // This is for root route redirection
    const getEntryPath = () => {
        const userAuthority = roles

        if (userAuthority.includes(USER)) {
            return userAuthenticatedPath
        }

        return authenticatedEntryPath
    }

    return <Navigate replace to={getEntryPath()} />
}

export default RoleBasedRoute
