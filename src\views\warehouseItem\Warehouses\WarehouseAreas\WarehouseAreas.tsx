/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import Container from '@/components/shared/Container'
import AdaptiveCard from '@/components/shared/AdaptiveCard'
import Button from '@/components/ui/Button'
import useTranslation from '@/utils/hooks/useTranslation'
import { TbArrowLeft, TbPlus, TbChevronRight, TbBox } from 'react-icons/tb'
import AreaCard from './components/AreaCard'
import AddAreaModal from './components/AddAreaModal'
import EditAreaModal from './components/EditAreaModal'
import {
    useDeleteWarehouseArea,
    useGenerateWarehouseStructure,
    useGetWarehouseById,
    useUpdateWarehouseArea,
} from '@/hooks/warehouses'

const WarehouseAreas = () => {
    const { t } = useTranslation()
    const navigate = useNavigate()
    const { warehouseId } = useParams<{ warehouseId: string }>()

    const [addModalOpen, setAddModalOpen] = useState(false)
    const [editModalOpen, setEditModalOpen] = useState(false)
    const [selectedArea, setSelectedArea] = useState<any>(null)

    const { data: warehouse, isLoading } = useGetWarehouseById(warehouseId!)
    const { mutate: updateWarehouseArea } = useUpdateWarehouseArea()
    const { mutate: deleteWarehouseArea } = useDeleteWarehouseArea()
    const { mutate: generateWarehouseStructure } =
        useGenerateWarehouseStructure()

    const handleBack = () => {
        navigate('/warehouses/warehouses')
    }

    const handleAddArea = async (areaData: any) => {
        try {
            if (!warehouseId) {
                throw new Error('Warehouse ID is required')
            }
            await generateWarehouseStructure({
                id: warehouseId,
                structure: areaData,
            })
            setAddModalOpen(false)
        } catch (error) {
            console.error('Error adding warehouse area:', error)
            // Here you may want to add proper error handling, like showing a notification
        }
    }

    const handleDeleteArea = async (areaId: string) => {
        try {
            if (!warehouseId) {
                throw new Error('Warehouse ID is required')
            }
            await deleteWarehouseArea({ warehouseId, areaId })
        } catch (error) {
            console.error('Error deleting warehouse area:', error)
            // Here you may want to add proper error handling, like showing a notification
        }
    }

    const handleEditArea = (area: any) => {
        setSelectedArea(area)
        setEditModalOpen(true)
    }

    const handleUpdateArea = async (areaData: {
        length: number
        width: number
    }) => {
        try {
            if (!warehouseId || !selectedArea) {
                throw new Error('Warehouse ID and area are required')
            }
            await updateWarehouseArea({
                warehouseId,
                areaId: selectedArea.id,
                area: areaData,
            })
            setEditModalOpen(false)
            setSelectedArea(null)
        } catch (error) {
            console.error('Error updating warehouse area:', error)
            // Here you may want to add proper error handling, like showing a notification
        }
    }

    // Get areas from warehouse data
    const areas = warehouse?.areas || []

    // Calculate total roofs from areas (assuming each unit has roofs)
    const totalRoofs = areas.reduce(
        (total, area) => total + area.numberOfShelves,
        0,
    )

    if (isLoading) {
        return (
            <Container>
                <div className="flex items-center justify-center h-96">
                    <div className="text-center">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                        <p className="text-gray-600 dark:text-gray-400">
                            {t('nav.shared.loading')}
                        </p>
                    </div>
                </div>
            </Container>
        )
    }

    return (
        <Container>
            {/* Beautiful Breadcrumb Navigation */}
            <div className="mb-8">
                {/* Breadcrumb */}
                <div className="flex items-center gap-2 mb-6">
                    <button
                        type="button"
                        className="flex items-center gap-2 text-sm font-medium text-gray-600 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400 transition-colors duration-200 group"
                        onClick={handleBack}
                    >
                        <TbArrowLeft className="w-4 h-4 transition-transform duration-200 group-hover:-translate-x-1" />
                        <span className="hover:underline">
                            {t('nav.warehouses.warehouses')}
                        </span>
                    </button>
                    <TbChevronRight className="w-4 h-4 text-gray-400" />
                    <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        {warehouse?.name || t('nav.shared.loading')}
                    </span>
                    <TbChevronRight className="w-4 h-4 text-gray-400" />
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {t('nav.warehouses.areas')}
                    </span>
                </div>

                {/* Enhanced Header */}
                <div className="bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-2xl p-8 border border-indigo-100 dark:border-indigo-800/30 shadow-sm">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-6">
                            <div className="flex items-center justify-center w-16 h-16 bg-indigo-100 dark:bg-indigo-900/30 rounded-2xl shadow-sm">
                                <TbBox className="w-8 h-8 text-indigo-600 dark:text-indigo-400" />
                            </div>
                            <div>
                                <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                                    {t('nav.warehouses.manageAreas')}
                                </h1>
                                <p className="text-indigo-700 dark:text-indigo-300 text-lg font-medium">
                                    {warehouse?.name || t('nav.shared.loading')}
                                </p>
                                <p className="text-gray-600 dark:text-gray-400">
                                    {t('nav.warehouses.manageAreasDesc')}
                                </p>
                            </div>
                        </div>
                        <div className="flex items-center gap-6">
                            <div className="text-center">
                                <div className="text-2xl font-bold text-indigo-600 dark:text-indigo-400">
                                    {areas.length}
                                </div>
                                <div className="text-sm text-gray-600 dark:text-gray-400">
                                    {t('nav.warehouses.totalAreas')}
                                </div>
                            </div>
                            <div className="text-center">
                                <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                                    {areas.reduce(
                                        (total, area) =>
                                            total + area.numberOfUnits,
                                        0,
                                    )}
                                </div>
                                <div className="text-sm text-gray-600 dark:text-gray-400">
                                    {t('nav.warehouses.totalUnits')}
                                </div>
                            </div>
                            <div className="text-center">
                                <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                                    {totalRoofs}
                                </div>
                                <div className="text-sm text-gray-600 dark:text-gray-400">
                                    {t('nav.warehouses.totalRoofs')}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Action Bar */}
            <div className="flex justify-between items-center mb-6">
                <div>
                    <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                        {t('nav.warehouses.areasList')}
                    </h2>
                    <p className="text-gray-600 dark:text-gray-400">
                        {t('nav.warehouses.areasListDesc')}
                    </p>
                </div>
                <Button
                    variant="solid"
                    icon={<TbPlus />}
                    className="bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                    onClick={() => setAddModalOpen(true)}
                >
                    {t('nav.warehouses.addArea')}
                </Button>
            </div>

            {/* Areas Grid */}
            {areas.length === 0 ? (
                <AdaptiveCard className="text-center py-12">
                    <div className="flex flex-col items-center gap-4">
                        <div className="flex items-center justify-center w-20 h-20 bg-gray-100 dark:bg-gray-700 rounded-full">
                            <TbBox className="w-10 h-10 text-gray-400" />
                        </div>
                        <div>
                            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                                {t('nav.warehouses.noAreas')}
                            </h3>
                            <p className="text-gray-600 dark:text-gray-400 mb-4">
                                {t('nav.warehouses.noAreasDesc')}
                            </p>
                            <Button
                                variant="solid"
                                icon={<TbPlus />}
                                className="bg-indigo-600 hover:bg-indigo-700"
                                onClick={() => setAddModalOpen(true)}
                            >
                                {t('nav.warehouses.addFirstArea')}
                            </Button>
                        </div>
                    </div>
                </AdaptiveCard>
            ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {areas.map((area) => (
                        <AreaCard
                            key={area.id}
                            area={{
                                id: area.id,
                                width: area.width,
                                height: area.height,
                                numberOfUnits: area.numberOfUnits,
                                numberOfShelves: area.numberOfShelves,
                                numberOfBoxes: area.numberOfBoxes,
                                availableBoxes: area.availableBoxes,
                            }}
                            warehouseId={warehouseId!}
                            onEdit={handleEditArea}
                            onDelete={handleDeleteArea}
                        />
                    ))}
                </div>
            )}

            {/* Add Area Modal */}
            <AddAreaModal
                isOpen={addModalOpen}
                onClose={() => setAddModalOpen(false)}
                onSubmit={handleAddArea}
            />

            {/* Edit Area Modal */}
            <EditAreaModal
                isOpen={editModalOpen}
                onClose={() => {
                    setEditModalOpen(false)
                    setSelectedArea(null)
                }}
                onSubmit={handleUpdateArea}
                initialData={
                    selectedArea
                        ? {
                              length: selectedArea.height,
                              width: selectedArea.width,
                          }
                        : null
                }
            />
        </Container>
    )
}

export default WarehouseAreas
