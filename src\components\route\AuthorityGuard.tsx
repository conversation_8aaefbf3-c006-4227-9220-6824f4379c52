// import { PropsWithChildren } from 'react'
// import { Navigate } from 'react-router-dom'

// type AuthorityGuardProps = PropsWithChildren<{
//     userAuthority?: string[]
//     authority?: string[]
// }>

// const AuthorityGuard = (props: AuthorityGuardProps) => {
//     const { userAuthority = [], authority = [], children } = props

//     const roleMatched = false

//     return (
//         <>{roleMatched ? children : <Navigate to="/access-denied" replace />}</>
//     )
// }

// export default AuthorityGuard
