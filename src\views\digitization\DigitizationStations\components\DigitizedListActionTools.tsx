import { useState } from 'react'
import Button from '@/components/ui/Button'
import useTranslation from '@/utils/hooks/useTranslation'
import { TbPlus } from 'react-icons/tb'
import DigitizationStationsModal from './DigitizationStationsModal'

const DigitizedListActionTools = () => {
    const { t } = useTranslation()
    const [addModalOpen, setAddModalOpen] = useState(false)

    const handleAddModalClose = () => {
        setAddModalOpen(false)
    }

    const handleAddSuccess = () => {
        setAddModalOpen(false)
    }

    return (
        <>
            <div className="flex items-center gap-2">
                <Button
                    variant="solid"
                    size="sm"
                    icon={<TbPlus />}
                    className="shadow-sm hover:shadow-md transition-all duration-200 bg-primary-mild hover:bg-primary-deep"
                    onClick={() => setAddModalOpen(true)}
                >
                    {t('nav.digitizationStations.addStation')}
                </Button>
            </div>

            <DigitizationStationsModal
                isOpen={addModalOpen}
                onSuccess={handleAddSuccess}
                onClose={handleAddModalClose}
            />
        </>
    )
}

export default DigitizedListActionTools
