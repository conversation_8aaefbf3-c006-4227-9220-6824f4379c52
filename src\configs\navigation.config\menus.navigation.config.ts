import { MENUS_PREFIX_PATH } from '@/constants/route.constant'
import {
    NAV_ITEM_TYPE_ITEM,
    NAV_ITEM_TYPE_COLLAPSE,
} from '@/constants/navigation.constant'
import { ADMIN } from '@/constants/roles.constant'
import type { NavigationTree } from '@/@types/navigation'

const menusNavigationConfig: NavigationTree[] = [
    {
        key: 'menus',
        path: '',
        title: 'Menus',
        translateKey: 'nav.menus.menus',
        icon: 'menus',
        type: NAV_ITEM_TYPE_COLLAPSE,
        authority: [ADMIN],
        meta: {
            horizontalMenu: {
                layout: 'default',
            },
        },
        subMenu: [
            {
                key: 'menus.unit-structure',
                path: `${MENUS_PREFIX_PATH}/unit-structure`,
                title: 'Unit Structure',
                translateKey: 'nav.menus.unitStructure',
                icon: 'menuUnitStructure',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [ADMIN],
                subMenu: [],
            },
            {
                key: 'menus.consumables',
                path: `${MENUS_PREFIX_PATH}/consumables`,
                title: 'Consumables',
                translateKey: 'nav.menus.consumables',
                icon: 'menuConsumables',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [ADMIN],
                subMenu: [],
            },
            {
                key: 'menus.stocks',
                path: `${MENUS_PREFIX_PATH}/stocks`,
                title: 'Stocks',
                translateKey: 'nav.menus.stocks',
                icon: 'menuStocks',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [ADMIN],
                subMenu: [],
            },
            {
                key: 'menus.buildings',
                path: `${MENUS_PREFIX_PATH}/buildings`,
                title: 'Buildings',
                translateKey: 'nav.menus.buildings',
                icon: 'menuBuildings',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [ADMIN],
                subMenu: [],
            },
            {
                key: 'menus.document-types',
                path: `${MENUS_PREFIX_PATH}/document-types`,
                title: 'Document Types',
                translateKey: 'nav.menus.documentTypes',
                icon: 'menuDocumentTypes',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [ADMIN],
                subMenu: [],
            },
            {
                key: 'menus.categories',
                path: `${MENUS_PREFIX_PATH}/categories`,
                title: 'Categories',
                translateKey: 'nav.menus.categories',
                icon: 'menuCategories',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [ADMIN],
                subMenu: [],
            },
            {
                key: 'menus.keywords',
                path: `${MENUS_PREFIX_PATH}/keywords`,
                title: 'Keywords',
                translateKey: 'nav.menus.keywords',
                icon: 'menuKeywords',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [ADMIN],
                subMenu: [],
            },
            {
                key: 'menus.general-categories',
                path: `${MENUS_PREFIX_PATH}/general-categories`,
                title: 'General Categories',
                translateKey: 'nav.menus.generalCategories',
                icon: 'menuGeneralCategories',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [ADMIN],
                subMenu: [],
            },
            {
                key: 'menus.entities',
                path: `${MENUS_PREFIX_PATH}/entities`,
                title: 'Entities',
                translateKey: 'nav.menus.entities',
                icon: 'menuEntities',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [ADMIN],
                subMenu: [],
            },
            {
                key: 'menus.sender-recipient',
                path: `${MENUS_PREFIX_PATH}/sender-recipient`,
                title: 'Sender Recipient',
                translateKey: 'nav.menus.senderRecipient',
                icon: 'menuSender&Recipient',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [ADMIN],
                subMenu: [],
            },
        ],
    },
]

export default menusNavigationConfig
