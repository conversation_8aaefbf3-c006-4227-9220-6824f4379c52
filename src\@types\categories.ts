import { Status } from './common'

export type Category = {
    id: number
    name: string
    status: Status
}

export type CategoryDetail = {
    id: number
    name: string
    status: Status
    parentId: number
    parentName: string
    childrenCount: number
    createdByID: string
    createdAt: string
    updatedByID: string
    updatedAt: string
}

export type CategoryTree = {
    rootNodes: RootNodes[]
    levelCounts: LevelCounts
    totalNodes: number
}

export interface RootNodes {
    category: Category
    level: number
    children: string[]
    childrenCount: number
}

export interface LevelCounts {
    additionalProp1: number
    additionalProp2: number
    additionalProp3: number
}
