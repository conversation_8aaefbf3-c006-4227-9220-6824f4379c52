/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMemo, useState } from 'react'
import Tooltip from '@/components/ui/Tooltip'
import DataTable from '@/components/shared/DataTable'
import ConfirmDialog from '@/components/shared/ConfirmDialog'
import { TbPencil, TbTrash } from 'react-icons/tb'
import type { ColumnDef } from '@/components/shared/DataTable'
import { Notification, toast } from '@/components/ui'
import BuildingModal from './BuildingModal'
import useTranslation from '@/utils/hooks/useTranslation'
import Status from '@/components/shared/displaying/Status'
import { useDeleteBuilding, useGetBuildings } from '@/hooks/building'
import useLocale from '@/utils/hooks/useLocale'
import { Building } from '@/@types/building'

const ActionColumn = ({
    onEdit,
    onDelete,
}: {
    onEdit: () => void
    onDelete: () => void
}) => {
    const { t } = useTranslation()
    return (
        <div className="flex items-center justify-center gap-3">
            <Tooltip title={t('nav.shared.edit')}>
                <div
                    className="text-xl cursor-pointer select-none font-semibold text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 transition-colors"
                    role="button"
                    onClick={onEdit}
                >
                    <TbPencil />
                </div>
            </Tooltip>
            <Tooltip title={t('nav.shared.delete')}>
                <div
                    className="text-xl cursor-pointer select-none font-semibold text-gray-600 hover:text-red-600 dark:text-gray-300 dark:hover:text-red-400 transition-colors"
                    role="button"
                    onClick={onDelete}
                >
                    <TbTrash />
                </div>
            </Tooltip>
        </div>
    )
}

const BuildingListTable = () => {
    const { locale } = useLocale()
    const { t } = useTranslation()

    const isRTL = locale === 'ar' ? true : false

    const [deleteConfirmationOpen, setDeleteConfirmationOpen] = useState(false)
    const [editModalOpen, setEditModalOpen] = useState(false)
    const [selectedBuilding, setSelectedBuilding] = useState<Building | null>(
        null,
    )

    const handleCancel = () => {
        setDeleteConfirmationOpen(false)
    }

    const handleDelete = (building: Building) => {
        setSelectedBuilding(building)
        setDeleteConfirmationOpen(true)
    }

    const handleEdit = (Building: Building) => {
        setSelectedBuilding(Building)
        setEditModalOpen(true)
    }

    const handleEditModalClose = () => {
        setEditModalOpen(false)
        setSelectedBuilding(null)
    }

    const handleConfirmDelete = async () => {
        if (selectedBuilding) {
            try {
                await deleteBuilding.mutateAsync(selectedBuilding.id)
                toast.push(
                    <Notification title="" type="success" duration={2500}>
                        {selectedBuilding.name} deleted
                    </Notification>,
                    { placement: 'top-center' },
                )
                setDeleteConfirmationOpen(false)
                setSelectedBuilding(null)
            } catch (error: any) {
                const data = error?.errors[0]

                toast.push(
                    <Notification title="" type="danger" duration={2500}>
                        {data?.description ||
                            t('nav.systemSecurity.failedToCreateUser')}
                    </Notification>,
                    { placement: 'top-center' },
                )
            }
        }
    }

    const { data: buildings = [], isLoading } = useGetBuildings()

    const deleteBuilding = useDeleteBuilding()

    const allColumns: ColumnDef<Building>[] = useMemo(() => {
        const columnDefinitions = [
            {
                key: 'name',
                header: t('nav.buildings.name'),
                accessorKey: 'name',
                cell: (props: { row: { original: any } }) => {
                    const row = props.row.original
                    return (
                        <span className="font-medium text-gray-900 dark:text-gray-100">
                            {row.name}
                        </span>
                    )
                },
            },
            {
                key: 'village',
                header: t('nav.buildings.village'),
                accessorKey: 'villageNameAr',
                cell: (props: { row: { original: any } }) => {
                    const row = props.row.original
                    return (
                        <span className="font-medium text-gray-900 dark:text-gray-100">
                            {row.villageNameAr}
                        </span>
                    )
                },
            },
            {
                key: 'city',
                header: t('nav.buildings.city'),
                accessorKey: isRTL
                    ? 'cityOrDistrictNameAr'
                    : 'cityOrDistrictNameEn',
                cell: (props: any) => {
                    const row = props.row.original
                    return (
                        <span className="heading-text">
                            {isRTL
                                ? row.cityOrDistrictNameAr
                                : row.cityOrDistrictNameEn}
                        </span>
                    )
                },
            },
            {
                key: 'governorate',
                header: t('nav.buildings.governorate'),
                accessorKey: isRTL ? 'governorateNameAr' : 'governorateNameEn',
                cell: (props: any) => {
                    const row = props.row.original
                    return (
                        <span className="heading-text">
                            {isRTL
                                ? row.governorateNameAr
                                : row.governorateNameEn}
                        </span>
                    )
                },
            },
            {
                key: 'country',
                header: t('nav.buildings.country'),
                accessorKey: isRTL ? 'countryNameAr' : 'countryNameEn',
                cell: (props: { row: { original: any } }) => {
                    const row = props.row.original
                    return (
                        <span className="font-medium text-gray-900 dark:text-gray-100">
                            {isRTL ? row.countryNameAr : row.countryNameEn}
                        </span>
                    )
                },
            },
            {
                key: 'status',

                header: t('nav.shared.status'),
                accessorKey: 'status',
                cell: (props: any) => <Status row={props.row} />,
            },
            {
                key: 'action',
                header: t('nav.shared.edit'),
                id: 'action',
                cell: (props: any) => (
                    <ActionColumn
                        onEdit={() => handleEdit(props.row.original)}
                        onDelete={() => handleDelete(props.row.original)}
                    />
                ),
            },
        ]

        // Filter columns based on visibleColumns prop
        return columnDefinitions
    }, [t, isRTL])

    return (
        <>
            <DataTable
                selectable
                columns={allColumns}
                data={buildings}
                noData={!isLoading && buildings.length === 0}
                skeletonAvatarColumns={[0]}
                skeletonAvatarProps={{ width: 28, height: 28 }}
                loading={isLoading}
                cellBorder={true}
                checkboxChecked={() => false}
                onCheckBoxChange={() => {}}
                onIndeterminateCheckBoxChange={() => {}}
            />
            <ConfirmDialog
                isOpen={deleteConfirmationOpen}
                type="danger"
                title={t('nav.buildings.removeBuilding')}
                confirmButtonProps={{
                    className: 'bg-red-500 hover:bg-red-600',
                }}
                onClose={handleCancel}
                onRequestClose={handleCancel}
                onCancel={handleCancel}
                onConfirm={handleConfirmDelete}
            >
                <p> {t('nav.buildings.confirmRemoveBuilding')}</p>
            </ConfirmDialog>

            <BuildingModal
                isOpen={editModalOpen}
                selectedBuildingId={selectedBuilding?.id}
                onClose={handleEditModalClose}
            />
        </>
    )
}

export default BuildingListTable
