import { useState } from 'react'
import Button from '@/components/ui/Button'
import Dropdown from '@/components/ui/Dropdown'
import ConfirmDialog from '@/components/shared/ConfirmDialog'
import useTranslation from '@/utils/hooks/useTranslation'
import type { File } from '@/@types/file'
import { TbTrash, TbCheck, TbX, TbChevronDown } from 'react-icons/tb'

interface DailyListSelectedProps {
    selectedFiles: File[]
    onClearSelection: () => void
    onDeleteSelected: () => void
    onStatusUpdate: (status: number) => void
}

const DailyListSelected = ({
    selectedFiles,
    onClearSelection,
    onDeleteSelected,
    onStatusUpdate,
}: DailyListSelectedProps) => {
    const { t } = useTranslation()
    const [deleteConfirmation, setDeleteConfirmation] = useState(false)

    const handleDeleteClick = () => {
        setDeleteConfirmation(true)
    }

    const handleConfirmDelete = () => {
        onDeleteSelected()
        setDeleteConfirmation(false)
    }

    const handleCancelDelete = () => {
        setDeleteConfirmation(false)
    }

    const statusUpdateItems = [
        {
            key: 'activate',
            name: t('nav.shared.activate'),
            icon: <TbCheck className="text-green-600" />,
            onClick: () => onStatusUpdate(1),
        },
        {
            key: 'deactivate',
            name: t('nav.shared.deactivate'),
            icon: <TbX className="text-red-600" />,
            onClick: () => onStatusUpdate(3),
        },
        {
            key: 'pending',
            name: t('nav.shared.setPending'),
            icon: <TbChevronDown className="text-amber-600" />,
            onClick: () => onStatusUpdate(2),
        },
    ]

    if (selectedFiles.length === 0) {
        return null
    }

    return (
        <>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <div className="text-sm font-medium text-blue-900">
                            {selectedFiles.length}{' '}
                            {selectedFiles.length === 1
                                ? t('nav.File.file')
                                : t('nav.File.files')}{' '}
                            {t('nav.shared.selected')}
                        </div>
                        <Button
                            variant="plain"
                            size="sm"
                            onClick={onClearSelection}
                            className="text-blue-700 hover:text-blue-900"
                        >
                            {t('nav.shared.clearSelection')}
                        </Button>
                    </div>

                    <div className="flex items-center gap-2">
                        <Dropdown
                            placement="bottom-end"
                            renderTitle={
                                <Button
                                    variant="solid"
                                    size="sm"
                                    className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
                                >
                                    {t('nav.shared.updateStatus')}
                                    <TbChevronDown />
                                </Button>
                            }
                        >
                            {statusUpdateItems.map((item) => (
                                <Dropdown.Item
                                    key={item.key}
                                    eventKey={item.key}
                                    onClick={item.onClick}
                                >
                                    <div className="flex items-center gap-2">
                                        {item.icon}
                                        <span>{item.name}</span>
                                    </div>
                                </Dropdown.Item>
                            ))}
                        </Dropdown>

                        <Button
                            variant="solid"
                            color="red-600"
                            size="sm"
                            icon={<TbTrash />}
                            onClick={handleDeleteClick}
                            className="flex items-center gap-2"
                        >
                            {t('nav.shared.delete')}
                        </Button>
                    </div>
                </div>

                {/* Selected files preview */}
                <div className="mt-3 flex flex-wrap gap-2">
                    {selectedFiles.slice(0, 5).map((file) => (
                        <div
                            key={file.fileId}
                            className="bg-white border border-blue-200 rounded px-2 py-1 text-xs text-blue-900"
                        >
                            {file.fileTitle}
                        </div>
                    ))}
                    {selectedFiles.length > 5 && (
                        <div className="bg-white border border-blue-200 rounded px-2 py-1 text-xs text-blue-700">
                            +{selectedFiles.length - 5} {t('nav.shared.more')}
                        </div>
                    )}
                </div>
            </div>

            {/* Delete Confirmation Dialog */}
            <ConfirmDialog
                isOpen={deleteConfirmation}
                type="danger"
                title={t('nav.shared.deleteConfirmation')}
                confirmText={t('nav.shared.delete')}
                cancelText={t('nav.shared.cancel')}
                onClose={handleCancelDelete}
                onRequestClose={handleCancelDelete}
                onCancel={handleCancelDelete}
                onConfirm={handleConfirmDelete}
            >
                <p>
                    {t('nav.shared.deleteSelectedConfirmationMessage', {
                        count: selectedFiles.length,
                        item:
                            selectedFiles.length === 1
                                ? t('nav.File.file')
                                : t('nav.File.files'),
                    })}
                </p>
                <div className="mt-3 max-h-32 overflow-y-auto">
                    <ul className="text-sm text-gray-600">
                        {selectedFiles.map((file) => (
                            <li key={file.fileId} className="py-1">
                                • {file.fileTitle}
                            </li>
                        ))}
                    </ul>
                </div>
            </ConfirmDialog>
        </>
    )
}

export default DailyListSelected
