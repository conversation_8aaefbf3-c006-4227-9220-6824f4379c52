/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useCallback, useMemo, useState } from 'react'
import { DataTable } from '@/components/shared'
import useTranslation from '@/utils/hooks/useTranslation'
import { HiOutlinePencil } from 'react-icons/hi'
import { PiKeyholeDuotone } from 'react-icons/pi'
import { Tooltip } from '@/components/ui'
import ConfirmDialog from '@/components/shared/ConfirmDialog'
import { User } from '@/@types/auth'
import Status from '@/components/shared/displaying/Status'
import { useGetUsers, useResetPassword } from '@/hooks/user'

interface UsersTableProps {
    onEdit?: (user: User) => void
}

const UsersTable = ({ onEdit }: UsersTableProps) => {
    const { data: users = [], isLoading } = useGetUsers()
    const { mutate: resetPassword } = useResetPassword()

    const { t } = useTranslation()

    const [tableData, setTableData] = useState({
        pageIndex: 1,
        pageSize: 10,
        sort: {
            order: '',
            key: '',
        },
        query: '',
        total: 0,
    })

    // const [deleteConfirmationOpen, setDeleteConfirmationOpen] = useState(false)
    const [resetPasswordConfirmationOpen, setResetPasswordConfirmationOpen] =
        useState(false)
    const [selectedUser, setSelectedUser] = useState<User | null>(null)
    const [selectedUserId, setSelectedUserId] = useState<string | null>(null)

    // Filter and sort users based on table data
    const filteredUsers = useMemo(() => {
        let filtered = [...users]

        // Apply search filter
        if (tableData.query) {
            filtered = filtered.filter(
                (user) =>
                    user.fullName
                        ?.toLowerCase()
                        .includes(tableData.query.toLowerCase()) ||
                    user.email
                        ?.toLowerCase()
                        .includes(tableData.query.toLowerCase()),
            )
        }

        // Apply sorting
        if (tableData.sort.key && tableData.sort.order) {
            filtered.sort((a, b) => {
                const aValue = (a as any)[tableData.sort.key]
                const bValue = (b as any)[tableData.sort.key]

                if (tableData.sort.order === 'asc') {
                    return aValue > bValue ? 1 : -1
                } else {
                    return aValue < bValue ? 1 : -1
                }
            })
        }

        return filtered
    }, [users, tableData.query, tableData.sort])

    // Get paginated data for current page
    const paginatedUsers = useMemo(() => {
        const startIndex = (tableData.pageIndex - 1) * tableData.pageSize
        const endIndex = startIndex + tableData.pageSize
        return filteredUsers.slice(startIndex, endIndex)
    }, [filteredUsers, tableData.pageIndex, tableData.pageSize])

    // Handle pagination changes
    const handlePaginationChange = (pageIndex: number) => {
        setTableData((prevData) => ({ ...prevData, pageIndex }))
    }

    // Handle sort changes
    const handleSort = (sort: { order: string; key: string }) => {
        setTableData((prevData) => ({ ...prevData, sort, pageIndex: 1 })) // Reset to first page when sorting
    }

    // Handle search changes
    // const handleSearch = (query: string) => {
    //     setTableData((prevData) => ({ ...prevData, query, pageIndex: 1 })) // Reset to first page when searching
    // }

    // Handle row selection
    const handleRowSelect = (checked: boolean, row: User) => {
        if (checked) {
            setSelectedUserId(row.id)
        } else {
            setSelectedUserId(null)
        }
    }

    // Handle reset password confirmation
    const handleResetPasswordConfirmation = useCallback((user: User) => {
        setSelectedUser(user)
        setResetPasswordConfirmationOpen(true)
    }, [])

    // Handle reset password action
    const handleConfirmResetPassword = async () => {
        if (selectedUser) {
            await resetPassword({ userId: selectedUser?.id })
            setResetPasswordConfirmationOpen(false)
            setSelectedUser(null)
        }
    }

    // Handle edit action
    const handleEdit = useCallback(
        (user: User) => {
            if (onEdit) {
                onEdit(user)
            }
        },
        [onEdit],
    )

    // Handle delete confirmation
    // const handleDeleteConfirmation = useCallback((user: User) => {
    //     setSelectedUser(user)
    //     setDeleteConfirmationOpen(true)
    // }, [])

    // Handle delete cancellation
    // const handleCancel = () => {
    //     setDeleteConfirmationOpen(false)
    //     setSelectedUser(null)
    // }

    // Handle reset password cancellation
    const handleResetPasswordCancel = () => {
        setResetPasswordConfirmationOpen(false)
        setSelectedUser(null)
    }

    // Handle status change (activate/deactivate user)
    // const handleConfirmStatusChange = async () => {
    //     if (selectedUser) {
    //         const newStatus = selectedUser.status === 1 ? 0 : 1
    //         await updateUserStatus({
    //             userId: selectedUser.id,
    //             status: newStatus,
    //         })
    //         setDeleteConfirmationOpen(false)
    //         setSelectedUser(null)
    //         getUsers() // Refresh the list
    //     }
    // }

    // Define table columns
    const columns = useMemo(
        () => [
            {
                header: '',
                accessorKey: 'profilePictureUrl',
                cell: (props: any) => {
                    const row = props.row.original
                    return (
                        <div className="w-full flex justify-center ">
                            <span className="w-8 h-8 rounded-full">
                                <img
                                    src={
                                        row.profilePictureUrl ||
                                        'https://www.gravatar.com/avatar/00000000000000000000000000000000?d=mp&f=y'
                                    }
                                    alt={row.fullName}
                                    className="w-8 h-8 rounded-full"
                                />
                            </span>
                        </div>
                    )
                },
            },
            {
                header: t('nav.shared.name') || 'Name',
                accessorKey: 'fullName',
                cell: (props: any) => {
                    const row = props.row.original
                    return <span className="font-semibold">{row.fullName}</span>
                },
            },
            {
                header: t('nav.shared.email'),
                accessorKey: 'email',
                cell: (props: any) => {
                    const row = props.row.original
                    return (
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                            {row.email || '-'}
                        </span>
                    )
                },
            },
            {
                header: t('nav.shared.phoneNumber'),
                accessorKey: 'phoneNumber',
                cell: (props: any) => {
                    const row = props.row.original
                    return (
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                            {row.phoneNumber || '-'}
                        </span>
                    )
                },
            },
            {
                header: t('nav.shared.status') || 'Status',
                accessorKey: 'status',
                cell: (props: any) => <Status row={props.row} />,
            },
            {
                header: t('nav.shared.actions') || 'Actions',
                id: 'actions',
                cell: (props: any) => {
                    const row = props.row.original
                    return (
                        <div className="flex justify-center items-center gap-2 text-lg">
                            <Tooltip
                                title={
                                    t('nav.shared.resetPasswordConfirm') ||
                                    'Reset Password'
                                }
                            >
                                <span
                                    className="cursor-pointer hover:text-blue-500 transition-colors"
                                    onClick={() =>
                                        handleResetPasswordConfirmation(row)
                                    }
                                >
                                    <PiKeyholeDuotone />
                                </span>
                            </Tooltip>
                            <Tooltip title={t('nav.shared.edit') || 'Edit'}>
                                <span
                                    className="cursor-pointer hover:text-blue-500 transition-colors"
                                    onClick={() => handleEdit(row)}
                                >
                                    <HiOutlinePencil />
                                </span>
                            </Tooltip>
                            {/* <Tooltip
                                title={
                                    row.status === 1
                                        ? t('nav.users.deactivate') ||
                                          'Deactivate'
                                        : t('nav.users.activate') || 'Activate'
                                }
                            >
                                <span
                                    className="cursor-pointer hover:text-red-500 transition-colors"
                                    onClick={() =>
                                        handleDeleteConfirmation(row)
                                    }
                                >
                                    <HiOutlineTrash />
                                </span>
                            </Tooltip> */}
                        </div>
                    )
                },
            },
        ],
        [
            t,
            handleEdit,
            // handleDeleteConfirmation,
            handleResetPasswordConfirmation,
        ],
    )

    // Filter columns based on visibleColumns prop
    const visibleColumnsData = useMemo(() => {
        return columns
    }, [columns])

    return (
        <>
            <DataTable
                selectable
                columns={visibleColumnsData}
                data={paginatedUsers}
                noData={!isLoading && filteredUsers.length === 0}
                skeletonAvatarColumns={[0]}
                skeletonAvatarProps={{ width: 28, height: 28 }}
                loading={isLoading}
                pagingData={{
                    total: filteredUsers.length,
                    pageIndex: tableData.pageIndex as number,
                    pageSize: tableData.pageSize as number,
                }}
                checkboxChecked={(row) => selectedUserId === row.id}
                cellBorder={true}
                onPaginationChange={handlePaginationChange}
                onSort={(sort) =>
                    handleSort({ order: sort.order, key: String(sort.key) })
                }
                onCheckBoxChange={handleRowSelect}
            />

            {/* <ConfirmDialog
                isOpen={deleteConfirmationOpen}
                type="warning"
                title={
                    selectedUser?.status === 1
                        ? t('nav.users.deactivateUser') || 'Deactivate User'
                        : t('nav.users.activateUser') || 'Activate User'
                }
                onClose={handleCancel}
                onRequestClose={handleCancel}
                onCancel={handleCancel}
                onConfirm={handleConfirmStatusChange}
                // confirmButtonColor={
                //     selectedUser?.status === 1 ? 'red-600' : 'emerald-600'
                // }
                // loading={isUpdatingUserStatus}
            >
                <p>
                    {selectedUser?.status === 1
                        ? t('nav.users.deactivateConfirmation') ||
                          `Are you sure you want to deactivate user "${selectedUser?.fullName}"?`
                        : t('nav.users.activateConfirmation') ||
                          `Are you sure you want to activate user "${selectedUser?.fullName}"?`}
                </p>
            </ConfirmDialog> */}

            <ConfirmDialog
                isOpen={resetPasswordConfirmationOpen}
                type="warning"
                title={t('nav.users.resetPassword') || 'Reset Password'}
                onClose={handleResetPasswordCancel}
                onRequestClose={handleResetPasswordCancel}
                onCancel={handleResetPasswordCancel}
                onConfirm={handleConfirmResetPassword}
            >
                <p>
                    {t('nav.shared.confirmResetPassword') ||
                        `Are you sure you want to reset the password for user "${selectedUser?.fullName}"? This action will generate a new temporary password.`}
                </p>
            </ConfirmDialog>
        </>
    )
}

export default UsersTable
