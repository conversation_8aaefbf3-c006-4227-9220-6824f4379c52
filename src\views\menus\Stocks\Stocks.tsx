import Container from '@/components/shared/Container'
import AdaptiveCard from '@/components/shared/AdaptiveCard'
import StockListActionTools from './components/StockListActionTools'
import StockListTableTools from './components/StockListTableTools'
import StockListTable from './components/StockListTable'
import StockModal from './components/StockModal'
// import StockSelectionFooter from './components/StockSelectionFooter'
import useTranslation from '@/utils/hooks/useTranslation'
import { useState } from 'react'
import { TbPackage } from 'react-icons/tb'
import CountBadges from '@/components/shared/displaying/CountBadges'
import { useGetStocks } from '@/hooks/stock'

const Stocks = () => {
    const { t } = useTranslation()
    const { isLoading, isError } = useGetStocks()
    const [isEditModalOpen, setIsEditModalOpen] = useState(false)
    const [editingStock_id, setEditingStock] = useState<number | undefined>()

    const handleEdit = (stock_id: number) => {
        setEditingStock(stock_id)
        setIsEditModalOpen(true)
    }

    const handleEditSuccess = () => {
        setIsEditModalOpen(false)
        setEditingStock(undefined)
    }

    const handleEditClose = () => {
        setIsEditModalOpen(false)
        setEditingStock(undefined)
    }

    return (
        <>
            <Container>
                <AdaptiveCard className="shadow-lg ">
                    <div className="flex flex-col gap-6">
                        {/* Enhanced Header Section */}
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 pb-4 border-b border-gray-200">
                            <div className="flex justify-between items-center gap-3 w-full">
                                <div className="flex items-center gap-3">
                                    <div className="flex items-center justify-center w-10 h-10 bg-primary-subtle rounded-lg">
                                        <TbPackage className="w-5 h-5 text-primary-deep" />
                                    </div>
                                    <h3 className="">
                                        {t('nav.menus.stocks')}
                                    </h3>
                                </div>
                                <CountBadges
                                    counts={{
                                        total: 5,
                                    }}
                                    loading={isLoading}
                                    error={isError}
                                />
                            </div>
                            <StockListActionTools />
                        </div>

                        {/* Table Tools Section */}
                        <div className="">
                            <StockListTableTools />
                        </div>

                        {/* Table Section */}
                        <div className="">
                            <StockListTable onEdit={handleEdit} />
                        </div>
                    </div>
                </AdaptiveCard>
            </Container>

            {/* Edit Modal */}
            <StockModal
                isOpen={isEditModalOpen}
                mode="edit"
                stock_id={editingStock_id}
                onSuccess={handleEditSuccess}
                onClose={handleEditClose}
            />

            {/* Selection Footer */}

            {/* <StockSelectionFooter
                selectedCount={0}
                onDeleteSelected={deleteSelectedStocks}
                onClearSelection={clearSelection}
            /> */}
        </>
    )
}

export default Stocks
