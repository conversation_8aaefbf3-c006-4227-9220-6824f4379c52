import appConfig from '@/configs/app.config'
import { Navigate, Outlet } from 'react-router-dom'

const { unAuthenticatedEntryPath } = appConfig

const ProtectedRoute = () => {
    const authenticated = localStorage.getItem('token')
    console.log('token auth :', authenticated)

    if (!authenticated) {
        return <Navigate replace to={`${unAuthenticatedEntryPath}`} />
    }

    return <Outlet />
}

export default ProtectedRoute
