export type AppConfig = {
    apiPrefix: string
    authenticatedEntryPath: string
    unAuthenticatedEntryPath: string
    userAuthenticatedPath: string
    locale: string
    accessTokenPersistStrategy: 'localStorage' | 'sessionStorage' | 'cookies'
    enableMock: boolean
    activeNavTranslation: boolean
}

const appConfig: AppConfig = {
    apiPrefix: 'https://archiving-system.runasp.net/api',
    authenticatedEntryPath: '/menus/unit-structure',
    userAuthenticatedPath: '/digitization/daily',
    unAuthenticatedEntryPath: '/sign-in',
    locale: 'ar',
    accessTokenPersistStrategy: 'localStorage',
    enableMock: false,
    activeNavTranslation: true,
}

export default appConfig
