/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect } from 'react'
import Dialog from '@/components/ui/Dialog'
import { Form, FormContainer, FormItem } from '@/components/ui/Form'
import Input from '@/components/ui/Input'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import useTranslation from '@/utils/hooks/useTranslation'
import { Notification, toast } from '@/components/ui'
import {
    useCreateDocumentType,
    useGetDocumentTypeById,
    useUpdateDocumentType,
    useUpdateDocumentTypeStatus,
} from '@/hooks/document-types'
import BottomStickyBar from '@/components/template/BottomStickyBar'
import FormActions from '@/components/shared/actions/FormActions'

interface DocumentTypeDialogProps {
    isOpen: boolean
    onClose: () => void
    documentTypeId?: number | null
}

const validationSchema = z.object({
    name: z.string().min(1, 'Name is required'),
    status: z.number().default(1),
})

type FormData = z.infer<typeof validationSchema>

const DocumentTypeDialog = ({
    isOpen,
    onClose,
    documentTypeId,
}: DocumentTypeDialogProps) => {
    const { t } = useTranslation()

    const { data: documentType } = useGetDocumentTypeById(documentTypeId!)

    const createDocumentType = useCreateDocumentType()
    const updateDocumentType = useUpdateDocumentType()
    const updateDocumentStatus = useUpdateDocumentTypeStatus()

    const {
        handleSubmit,
        reset,
        setValue,
        formState: { errors },
    } = useForm<FormData>({
        resolver: zodResolver(validationSchema),
        defaultValues: {
            name: '',
            status: 1,
        },
    })

    // Set form values when document type data is loaded
    useEffect(() => {
        if (documentType) {
            setValue('name', documentType.name)
            setValue('status', documentType.status)
        }
    }, [documentType, setValue])

    const onSubmit = async (data: FormData) => {
        try {
            if (documentTypeId) {
                // Update existing document type
                await updateDocumentType.mutateAsync({
                    id: documentTypeId,
                    documentType: data,
                })
            } else {
                // Create new document type
                await createDocumentType.mutateAsync(data)
            }
            onClose()
        } catch (error) {
            console.error('Error saving document type:', error)
        } finally {
            reset()
        }
    }

    const handleClose = () => {
        reset()
        onClose()
    }

    return (
        <Dialog
            isOpen={isOpen}
            onRequestClose={handleClose}
            onClose={handleClose}
        >
            <div className="mb-6">
                <h4 className="text-lg font-semibold">
                    {documentTypeId
                        ? t('nav.documentTypes.editDocumentType')
                        : t('nav.documentTypes.addDocumentType')}
                </h4>
            </div>
            <Form onSubmit={handleSubmit(onSubmit)}>
                <FormContainer>
                    <FormItem
                        label={t('nav.documentTypes.name')}
                        invalid={!!errors.name}
                        errorMessage={errors.name?.message}
                    >
                        <Input
                            placeholder={t('nav.documentTypes.enterName')}
                            defaultValue={documentType?.name || ''}
                            onChange={(e) => {
                                setValue('name', e.target.value)
                            }}
                        />
                    </FormItem>
                </FormContainer>
                <BottomStickyBar>
                    <FormActions
                        isEdit={!!documentTypeId}
                        data={documentType}
                        loading={
                            createDocumentType.isPending ||
                            updateDocumentType.isPending
                        }
                        showStatusButton={true}
                        statusButtonColor={
                            documentType?.status === 1 ? 'red' : 'emerald'
                        }
                        onStatusChange={async () => {
                            if (documentType) {
                                try {
                                    await updateDocumentStatus.mutateAsync({
                                        id: documentTypeId!,
                                        status:
                                            documentType.status === 1 ? 2 : 1,
                                    })
                                    toast.push(
                                        <Notification
                                            title=""
                                            type="success"
                                            duration={2500}
                                        >
                                            {t(
                                                'nav.buildings.buildingStatusUpdated',
                                            )}
                                        </Notification>,
                                        {
                                            placement: 'top-center',
                                        },
                                    )
                                    onClose()
                                } catch (error: any) {
                                    toast.push(
                                        <Notification
                                            title=""
                                            type="danger"
                                            duration={2500}
                                        >
                                            {error?.errors?.[0]?.description ||
                                                t('nav.shared.failed')}
                                        </Notification>,
                                        {
                                            placement: 'top-center',
                                        },
                                    )
                                }
                            }
                        }}
                    />
                </BottomStickyBar>
            </Form>
        </Dialog>
    )
}

export default DocumentTypeDialog
