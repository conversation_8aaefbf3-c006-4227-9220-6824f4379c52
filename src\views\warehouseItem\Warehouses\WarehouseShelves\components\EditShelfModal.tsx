import { useState, useEffect } from 'react'
import { use<PERSON><PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import Dialog from '@/components/ui/Dialog'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import { FormItem, Form } from '@/components/ui/Form'
import useTranslation from '@/utils/hooks/useTranslation'
import { TbCheck, TbStack } from 'react-icons/tb'

interface EditShelfModalProps {
    isOpen: boolean
    onClose: () => void
    onSubmit: (data: EditShelfFormData) => void
    initialData?: EditShelfFormData | null
}

interface EditShelfFormData {
    length: number
    width: number
    maxBoxes: number
}

const EditShelfModal = ({
    isOpen,
    onClose,
    onSubmit,
    initialData,
}: EditShelfModalProps) => {
    const { t } = useTranslation()
    const [loading, setLoading] = useState(false)

    const editShelfSchema = z.object({
        length: z
            .number()
            .min(0.1, { message: t('nav.warehouses.shelfLengthRequired') }),
        width: z
            .number()
            .min(0.1, { message: t('nav.warehouses.shelfWidthRequired') }),
        maxBoxes: z
            .number()
            .min(1, { message: t('nav.warehouses.maxBoxesRequired') }),
    })

    const {
        handleSubmit,
        control,
        reset,
        formState: { errors },
    } = useForm<EditShelfFormData>({
        defaultValues: {
            length: 0,
            width: 0,
            maxBoxes: 0,
        },
        resolver: zodResolver(editShelfSchema),
    })

    // Reset form when initialData changes
    useEffect(() => {
        if (initialData) {
            reset({
                length: initialData.length,
                width: initialData.width,
                maxBoxes: initialData.maxBoxes,
            })
        }
    }, [initialData, reset])

    const handleFormSubmit = async (data: EditShelfFormData) => {
        setLoading(true)
        try {
            // Simulate API call
            await new Promise((resolve) => setTimeout(resolve, 1000))
            onSubmit(data)
            reset()
        } catch (error) {
            console.error('Error updating shelf:', error)
        } finally {
            setLoading(false)
        }
    }

    const handleClose = () => {
        reset()
        onClose()
    }

    return (
        <Dialog
            isOpen={isOpen}
            width={600}
            height={400}
            onClose={handleClose}
            onRequestClose={handleClose}
        >
            <div className="flex flex-col h-full max-w-4xl mx-auto">
                {/* Header */}
                <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                    <div className="flex items-center gap-3">
                        <div className="flex items-center justify-center w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg">
                            <TbStack className="w-5 h-5 text-green-600 dark:text-green-400" />
                        </div>
                        <div>
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                {t('nav.warehouses.editShelf')}
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                                {t('nav.warehouses.editShelfDescription')}
                            </p>
                        </div>
                    </div>
                </div>

                {/* Form */}
                <div className="flex-1 overflow-y-auto p-6">
                    <Form onSubmit={handleSubmit(handleFormSubmit)}>
                        <div className="space-y-6">
                            {/* Shelf Configuration */}
                            <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                                <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-4">
                                    {t('nav.warehouses.shelfConfiguration')}
                                </h4>
                                <div className="grid grid-cols-1 gap-4">
                                    <div className="grid grid-cols-2 gap-4">
                                        <FormItem
                                            label={`${t('nav.warehouses.shelfLength')} *`}
                                            invalid={!!errors.length}
                                            errorMessage={
                                                errors.length?.message
                                            }
                                        >
                                            <Controller
                                                name="length"
                                                control={control}
                                                render={({ field }) => (
                                                    <Input
                                                        {...field}
                                                        type="number"
                                                        min="0.1"
                                                        step="0.1"
                                                        placeholder="0.0"
                                                        onChange={(e) =>
                                                            field.onChange(
                                                                parseFloat(
                                                                    e.target
                                                                        .value,
                                                                ) || 0,
                                                            )
                                                        }
                                                    />
                                                )}
                                            />
                                        </FormItem>
                                        <FormItem
                                            label={`${t('nav.warehouses.shelfWidth')} *`}
                                            invalid={!!errors.width}
                                            errorMessage={errors.width?.message}
                                        >
                                            <Controller
                                                name="width"
                                                control={control}
                                                render={({ field }) => (
                                                    <Input
                                                        {...field}
                                                        type="number"
                                                        min="0.1"
                                                        step="0.1"
                                                        placeholder="0.0"
                                                        onChange={(e) =>
                                                            field.onChange(
                                                                parseFloat(
                                                                    e.target
                                                                        .value,
                                                                ) || 0,
                                                            )
                                                        }
                                                    />
                                                )}
                                            />
                                        </FormItem>
                                    </div>
                                    <FormItem
                                        label={`${t('nav.warehouses.maxBoxes')} *`}
                                        invalid={!!errors.maxBoxes}
                                        errorMessage={errors.maxBoxes?.message}
                                    >
                                        <Controller
                                            name="maxBoxes"
                                            control={control}
                                            render={({ field }) => (
                                                <Input
                                                    {...field}
                                                    type="number"
                                                    min="1"
                                                    placeholder="0"
                                                    onChange={(e) =>
                                                        field.onChange(
                                                            parseInt(
                                                                e.target.value,
                                                            ) || 0,
                                                        )
                                                    }
                                                />
                                            )}
                                        />
                                    </FormItem>
                                </div>
                            </div>
                        </div>
                    </Form>
                </div>

                {/* Action Buttons - Fixed at bottom */}
                <div className="flex justify-end gap-3 p-6 pt-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
                    <Button
                        type="button"
                        variant="plain"
                        disabled={loading}
                        className="min-w-[100px]"
                        onClick={handleClose}
                    >
                        {t('nav.shared.cancel')}
                    </Button>
                    <Button
                        type="submit"
                        variant="solid"
                        icon={<TbCheck />}
                        loading={loading}
                        className="min-w-[100px] bg-green-600 hover:bg-green-700"
                        onClick={handleSubmit(handleFormSubmit)}
                    >
                        {t('nav.shared.update')}
                    </Button>
                </div>
            </div>
        </Dialog>
    )
}

export default EditShelfModal
