export interface OneNode {
    code: string
    name: string
    description: string
    level: number
    parentCode: string | null
    parent?: OneNode | null
}

export interface RootNodes {
    code: string
    name: string
    description: string
    level: number
    children?: RootNodes[]
    childrenCount?: number
    parentCode?: string
}

export interface LevelCounts {
    L1: number
    L2: number
    L3: number
    L4: number
}

export interface OrganizationalStructure {
    rootNodes: RootNodes[]
    levelCounts?: LevelCounts
    totalNodes?: number
    // parent?: string
    // parentCode?: string
}

export type NodeDetails = {
    code: string
    name: string
    description: string
    level: number
    parentCode: string | null
    parent?: OneNode | null
    children?: OneNode[]
    pathToRoot: string[]
    childrenCount: number
    descendantsCount: number
}

export type CreatedNode = {
    name: string
    description?: string
    parentCode: string
}

export type UpdatedNode = {
    name: string
    description?: string
}

export enum OrgLevel {
    L1 = 1,
    L2 = 2,
    L3 = 3,
    L4 = 4,
}
