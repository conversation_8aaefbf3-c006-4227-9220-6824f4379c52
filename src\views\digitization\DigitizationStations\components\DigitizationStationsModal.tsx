/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState } from 'react'
import Dialog from '@/components/ui/Dialog'
import useTranslation from '@/utils/hooks/useTranslation'
import { TbPlus, TbPencil } from 'react-icons/tb'
import DigitizedForm from './DigitizedForm'
import { DigitizationStationRequest } from '@/@types/digitizationStation'
import { Button, Notification, toast } from '@/components/ui'
import {
    useCreateDigitizationStation,
    useGetDigitizationStationById,
    useUpdateDigitizationStation,
    useUpdateDigitizationStationStatus,
} from '@/hooks/digitization-station'

interface DigitizationStationsModalProps {
    isOpen: boolean
    onSuccess: () => void
    onClose: () => void
    station_id?: number
}

const DigitizationStationsModal = ({
    isOpen,
    onSuccess,
    onClose,
    station_id,
}: DigitizationStationsModalProps) => {
    const { t } = useTranslation()

    const { data: selectedStation } = useGetDigitizationStationById(station_id!)
    const { mutate: updateStationStatus } = useUpdateDigitizationStationStatus()
    const { mutate: updateStation, isPending: editPending } =
        useUpdateDigitizationStation()
    const { mutate: createStation } = useCreateDigitizationStation()

    const [isSubmitting, setIsSubmitting] = useState(false)

    const isEdit = station_id !== undefined

    const handleSubmit = async (formData: DigitizationStationRequest) => {
        setIsSubmitting(true)
        try {
            if (isEdit) {
                await updateStation({ id: station_id, station: formData })
            } else {
                await createStation(formData)
            }
            toast.push(
                <Notification type="success">
                    {t('nav.warehouses.createSuccess')}
                </Notification>,
                { placement: 'top-center' },
            )
            onSuccess()
        } catch (error: any) {
            const errorMessage = error?.errors
            toast.push(
                <Notification type="danger">
                    {errorMessage
                        ? errorMessage[0]?.description
                        : error?.status}
                </Notification>,
                { placement: 'top-center' },
            )
        } finally {
            setIsSubmitting(false)
        }
    }

    const handleClose = () => {
        if (!isSubmitting) {
            onClose()
        }
    }

    return (
        <Dialog
            isOpen={isOpen}
            shouldCloseOnEsc={true}
            shouldCloseOnOverlayClick={true}
            width={750}
            height={500}
            className="h-min"
            onClose={handleClose}
            onRequestClose={handleClose}
        >
            <div className="flex flex-col h-full">
                <div className="flex items-center gap-3 pb-2">
                    <div className="flex items-center justify-center w-10 h-10 bg-primary-subtle rounded-lg">
                        {!isEdit ? (
                            <TbPlus className="w-5 h-5 text-primary-deep" />
                        ) : (
                            <TbPencil className="w-5 h-5 text-primary-deep" />
                        )}
                    </div>
                    <div>
                        <h3 className="">
                            {!isEdit
                                ? t('nav.digitizationStations.addStation')
                                : t('nav.digitizationStations.editStation')}
                        </h3>
                        <p className="text-sm text-gray-500">
                            {!isEdit
                                ? t(
                                      'nav.digitizationStations.addDigitizationStationDescription',
                                  )
                                : t('nav.Stock.editStockDescription')}
                        </p>
                    </div>
                </div>

                <div className="flex-1  overflow-y-auto min-h-0">
                    <DigitizedForm
                        isEdit={isEdit}
                        station_id={station_id}
                        onSubmit={handleSubmit}
                    >
                        <div className="flex justify-end gap-4">
                            <Button
                                type="button"
                                variant="plain"
                                size="sm"
                                disabled={isSubmitting}
                                className="px-6 py-2 border border-gray-300 hover:border-gray-400 transition-colors"
                                onClick={handleClose}
                            >
                                {t('nav.shared.cancel')}
                            </Button>
                            {isEdit && (
                                <Button
                                    size="sm"
                                    type="button"
                                    disabled={editPending}
                                    variant="solid"
                                    className={
                                        selectedStation?.status === 1
                                            ? 'bg-blue-500 hover:bg-blue-600 border-blue-500 hover:border-blue-600'
                                            : 'bg-emerald-500 hover:bg-emerald-600 border-emerald-500 hover:border-emerald-600'
                                    }
                                    onClick={() => {
                                        if (selectedStation) {
                                            updateStationStatus({
                                                id: selectedStation.id,
                                                status:
                                                    selectedStation.status === 1
                                                        ? 2
                                                        : 1,
                                            })

                                            toast.push(
                                                <Notification
                                                    title=""
                                                    type="success"
                                                    duration={2500}
                                                >
                                                    {t(
                                                        'nav.systemSecurity.userStatusUpdated',
                                                    )}
                                                </Notification>,
                                                {
                                                    placement: 'top-center',
                                                },
                                            )
                                            handleClose()
                                        }
                                    }}
                                >
                                    {selectedStation?.status === 1
                                        ? t('nav.GlobalActions.frozen')
                                        : t('nav.GlobalActions.approve')}
                                </Button>
                            )}
                            <Button
                                type="submit"
                                variant="solid"
                                size="sm"
                                loading={isSubmitting}
                                className="px-6 py-2 bg-primary-mild hover:bg-primary-deep shadow-sm hover:shadow-md transition-all"
                            >
                                {!isEdit
                                    ? t('nav.shared.create')
                                    : t('nav.shared.update')}
                            </Button>
                        </div>
                    </DigitizedForm>
                </div>
            </div>
        </Dialog>
    )
}

export default DigitizationStationsModal
