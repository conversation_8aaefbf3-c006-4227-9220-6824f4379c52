/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useMemo, useState } from 'react'
import { DataTable } from '@/components/shared'
import { useCategoryList } from '@/utils/hooks/useCategoryList'
import useTranslation from '@/utils/hooks/useTranslation'
import { HiOutlinePencil, HiOutlineTrash } from 'react-icons/hi'
// import { FiEye } from 'react-icons/fi'
import { Tooltip } from '@/components/ui'
import Badge from '@/components/ui/Badge'

import ConfirmDialog from '@/components/shared/ConfirmDialog'

interface CategoriesTableProps {
    onEdit?: (id: number) => void
    visibleColumns?: string[]
}

const CategoriesTable = ({
    onEdit,
    visibleColumns = ['id', 'name', 'description', 'status', 'actions'],
}: CategoriesTableProps) => {
    const {
        filteredCategories,
        loading,
        getCategories,
        // updateStatus,
        setSelectCategoryById,
        selectCategoryById,
    } = useCategoryList()

    const { t } = useTranslation()

    const [tableData, setTableData] = useState({
        pageIndex: 1,
        pageSize: 10,
        sort: {
            order: '',
            key: '',
        },
        query: '',
        total: 0,
    })

    const [deleteConfirmationOpen, setDeleteConfirmationOpen] = useState(false)

    // Fetch categories on component mount
    useEffect(() => {
        getCategories()
    }, [])

    // Handle pagination changes
    const handlePaginationChange = (pageIndex: number) => {
        setTableData((prevData) => ({ ...prevData, pageIndex }))
    }

    // Handle sort changes
    const handleSort = (sort: { order: string; key: string }) => {
        setTableData((prevData) => ({ ...prevData, sort }))
    }

    // Handle row selection
    const handleRowSelect = (row: any) => {
        setSelectCategoryById(row.id)
    }

    // Handle edit action
    const handleEdit = (id: number) => {
        if (onEdit) {
            onEdit(id)
        }
    }

    // Handle delete confirmation
    const handleDeleteConfirmation = (id: number) => {
        setSelectCategoryById(id)
        setDeleteConfirmationOpen(true)
    }

    // Handle delete cancellation
    const handleCancel = () => {
        setDeleteConfirmationOpen(false)
    }

    // Handle delete confirmation
    const handleConfirmDelete = async () => {
        // if (selectedCategoryId !== null) {
        //     await updateStatus(selectedCategoryId.toString(), 0)
        //     setDeleteConfirmationOpen(false)
        //     getCategories() // Refresh the list
        // }
    }

    // // Handle change status
    // const handleChangeStatus = async () => {
    //     if (selectedCategoryId !== null) {
    //         await updateStatus(selectedCategoryId.toString(), 1)
    //         setDeleteConfirmationOpen(false)
    //         getCategories() // Refresh the list
    //     }
    // }

    // Define table columns
    const columns = useMemo(
        () => [
            {
                header: 'ID',
                accessorKey: 'id',
                cell: (props: any) => {
                    const row = props.row.original
                    return <span>{row.id}</span>
                },
            },
            {
                header: t('nav.categories.name'),
                accessorKey: 'name',
                cell: (props: any) => {
                    const row = props.row.original
                    return <span className="font-bold">{row.name}</span>
                },
            },
            {
                header: t('nav.categories.description'),
                accessorKey: 'description',
                cell: (props: any) => {
                    const row = props.row.original
                    return (
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                            {row.description || '-'}
                        </span>
                    )
                },
            },
            {
                header: t('nav.shared.status'),
                accessorKey: 'status',
                cell: (props: any) => {
                    const status = props.row.original.status
                    return (
                        <div className="flex items-center justify-center gap-2">
                            <Badge
                                innerClass={
                                    status ? 'bg-emerald-500' : 'bg-red-500'
                                }
                            />
                            <span>
                                {status
                                    ? t('nav.GlobalActions.approve')
                                    : t('nav.GlobalActions.frozen')}
                            </span>
                        </div>
                    )
                },
            },
            {
                header: t('nav.shared.edit'),
                id: 'actions',
                cell: (props: any) => {
                    const row = props.row.original
                    return (
                        <div className="flex justify-center items-center gap-2 text-lg">
                            <Tooltip title={t('nav.shared.view')}>
                                {/* <span
                                    className="cursor-pointer hover:text-blue-500"
                                    onClick={() =>
                                        handleCategoryView(row.id)
                                    }
                                >
                                    <FiEye />
                                </span> */}
                            </Tooltip>
                            <Tooltip title={t('nav.shared.edit')}>
                                <span
                                    className="cursor-pointer hover:text-blue-500"
                                    onClick={() => handleEdit(row.id)}
                                >
                                    <HiOutlinePencil />
                                </span>
                            </Tooltip>
                            <Tooltip title={t('nav.shared.delete')}>
                                <span
                                    className="cursor-pointer hover:text-red-500"
                                    onClick={() =>
                                        handleDeleteConfirmation(row.id)
                                    }
                                >
                                    <HiOutlineTrash />
                                </span>
                            </Tooltip>
                        </div>
                    )
                },
            },
        ],
        [t],
    )

    // Filter columns based on visibleColumns prop
    const visibleColumnsData = useMemo(() => {
        return columns.filter((column) => {
            return visibleColumns.includes(
                column.accessorKey || column.id || '',
            )
        })
    }, [columns, visibleColumns])

    return (
        <>
            <DataTable
                selectable
                columns={visibleColumnsData}
                data={filteredCategories}
                noData={!loading && filteredCategories.length === 0}
                skeletonAvatarColumns={[0]}
                skeletonAvatarProps={{ width: 28, height: 28 }}
                loading={loading}
                pagingData={{
                    total: filteredCategories.length,
                    pageIndex: tableData.pageIndex as number,
                    pageSize: tableData.pageSize as number,
                }}
                checkboxChecked={(row) => selectCategoryById === row.id}
                cellBorder={true}
                onPaginationChange={handlePaginationChange}
                onSort={(sort) =>
                    handleSort({ order: sort.order, key: String(sort.key) })
                }
                onCheckBoxChange={handleRowSelect}
            />

            <ConfirmDialog
                isOpen={deleteConfirmationOpen}
                type="danger"
                title={t('nav.categories.removeCategory')}
                onClose={handleCancel}
                onRequestClose={handleCancel}
                onCancel={handleCancel}
                onConfirm={handleConfirmDelete}
            >
                <p>{t('nav.categories.deleteConfirmation')}</p>
            </ConfirmDialog>
        </>
    )
}

export default CategoriesTable
