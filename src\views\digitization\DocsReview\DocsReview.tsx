import React, { useState } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { <PERSON><PERSON>, Card } from '@/components/ui'
import { TbArrowLeft, TbEdit, TbDeviceFloppy } from 'react-icons/tb'
import DocumentMetadata from './components/DocumentMetadata'
import DocumentUpload from './components/DocumentUpload'
import DocumentViewer from './components/DocumentViewer'
import { useGetDocumentById } from '@/hooks/documents'

export default function DocsReview() {
    const { documentId, fileId } = useParams()
    const { data: documentDetails, isLoading } = useGetDocumentById(documentId!)

    const navigate = useNavigate()
    const { t } = useTranslation()

    const [editMode, setEditMode] = useState(false)
    const [, setUploadedFile] = useState<File | null>(null)

    const handleFileUpload = (file: File) => {
        setUploadedFile(file)
        // Here you would typically upload the file to your server
        console.log('File uploaded:', file.name)
    }

    const handleFileRemove = () => {
        setUploadedFile(null)
    }

    const handleSave = () => {
        // Save document changes
        setEditMode(false)
        // API call to save changes would go here
    }

    const handleBack = () => {
        navigate(`/digitization/${fileId}/documents`)
    }

    return (
        <Card className="">
            {/* Header */}
            <div className=" border-b border-gray-200 px-6 py-4">
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <Button
                            variant="plain"
                            size="sm"
                            className="flex items-center gap-2"
                            onClick={handleBack}
                        >
                            <TbArrowLeft />
                            {t('nav.shared.back')}
                        </Button>

                        <div className="flex flex-col gap-2 items-start space-x-4">
                            <h1 className="text-2xl font-bold text-gray-900">
                                {documentDetails?.title}
                            </h1>
                            <p className="text-sm text-gray-600">
                                {documentId}
                            </p>
                        </div>
                    </div>

                    <div className="flex items-center gap-3">
                        {editMode ? (
                            <>
                                <Button
                                    variant="default"
                                    size="sm"
                                    onClick={() => setEditMode(false)}
                                >
                                    {t('nav.shared.cancel')}
                                </Button>
                                <Button
                                    variant="solid"
                                    size="sm"
                                    className="flex items-center gap-2"
                                    onClick={handleSave}
                                >
                                    <TbDeviceFloppy />
                                    {t('nav.shared.save')}
                                </Button>
                            </>
                        ) : (
                            <Button
                                variant="solid"
                                size="sm"
                                className="flex items-center gap-2"
                                onClick={() => setEditMode(true)}
                            >
                                <TbEdit />
                                {t('nav.shared.edit')}
                            </Button>
                        )}
                    </div>
                </div>
            </div>

            {/* Main Content */}
            <div className="max-w-7xl mx-auto px-6 py-8">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    {/* Left Column - Metadata and Upload */}
                    <div className="lg:col-span-1 space-y-6">
                        {/* Document Metadata */}
                        <DocumentMetadata
                            document={documentDetails!}
                            loading={isLoading}
                        />

                        {/* Document Upload */}
                        <DocumentUpload
                            disabled={!editMode}
                            onFileUpload={handleFileUpload}
                            onFileRemove={handleFileRemove}
                        />
                    </div>

                    {/* Right Column - Document Viewer */}
                    <div className="lg:col-span-2">
                        <DocumentViewer
                            documentUrl={documentDetails?.url}
                            documentTitle={documentDetails?.title}
                            documentType="pdf"
                            loading={isLoading}
                        />
                    </div>
                </div>

                {/* Attachments Section */}
                {documentDetails?.attachments &&
                    documentDetails.attachments.length > 0 && (
                        <div className="mt-8">
                            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                                    {t('documents.attachments')}
                                </h3>
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                    {documentDetails.attachments.map(
                                        (attachment) => (
                                            <div
                                                key={attachment.id}
                                                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                                            >
                                                <div className="flex items-center justify-between">
                                                    <div>
                                                        <p className="font-medium text-gray-900">
                                                            Attachment{' '}
                                                            {attachment.id}
                                                        </p>
                                                        <p className="text-sm text-gray-500">
                                                            {new Date(
                                                                attachment.createdAt,
                                                            ).toLocaleDateString()}
                                                        </p>
                                                    </div>
                                                    <Button
                                                        variant="default"
                                                        size="sm"
                                                        onClick={() =>
                                                            window.open(
                                                                attachment.url,
                                                                '_blank',
                                                            )
                                                        }
                                                    >
                                                        {t('nav.shared.view')}
                                                    </Button>
                                                </div>
                                            </div>
                                        ),
                                    )}
                                </div>
                            </div>
                        </div>
                    )}
            </div>
        </Card>
    )
}
