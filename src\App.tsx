import { BrowserRouter } from 'react-router-dom'
import Theme from '@/components/template/Theme'
import Layout from '@/components/layouts'
import Views from '@/views'
import useLocale from '@/utils/hooks/useLocale'
import useDocumentTitle from '@/utils/hooks/useDocumentTitle'
import './locales'

function App() {
    useLocale()
    useDocumentTitle()

    return (
        <Theme>
            <BrowserRouter>
                <Layout>
                    <Views />
                </Layout>
            </BrowserRouter>
        </Theme>
    )
}

export default App
