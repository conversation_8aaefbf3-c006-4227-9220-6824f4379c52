import { Form, FormItem } from '@/components/ui/Form'
import Button from '@/components/ui/Button'
import Select from '@/components/ui/Select'
import useTranslation from '@/utils/hooks/useTranslation'
import { Controller, useForm, useWatch } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { Card, Input, Checkbox } from '@/components/ui'
import { useEffect, useMemo, useState } from 'react'
import { TbTrash, TbPlus, TbTarget, TbUsers, TbBuilding } from 'react-icons/tb'
import {
    DigitizationStationRequest,
    MediumType,
    PaperSize,
} from '@/@types/digitizationStation'
import { Container } from '@/components/shared'
import BottomStickyBar from '@/components/template/BottomStickyBar'
import { CommonProps } from '@/@types/common'
import { useGetBuildings } from '@/hooks/building'
import { useGetOrgsStructure } from '@/hooks/orgs'
import { useGetUsersByOrgNode } from '@/hooks/user'
import { useGetDigitizationStationById } from '@/hooks/digitization-station'

type DigitizedFormProps = {
    onSubmit: (data: DigitizationStationRequest) => Promise<void>
    isEdit: boolean
    station_id?: number
} & CommonProps

// Validation schema will be defined inside the component to access translation function

const DigitizedForm: React.FC<DigitizedFormProps> = ({
    onSubmit,
    station_id,
    isEdit,
    children,
}) => {
    const { t } = useTranslation()
    const mode = isEdit ? 'edit' : 'add'

    const [orgCode, setOrgCode] = useState<string | null>()

    // Validation schema with improved error messages
    const validationSchema = z.object({
        buildingId: z.number().min(1, {
            message: t('nav.digitizationStations.pleaseSelectBuilding'),
        }),
        dailyTargets: z
            .array(
                z.object({
                    paperSize: z.number().min(0, {
                        message: t(
                            'nav.digitizationStations.paperSizeSelectionRequired',
                        ),
                    }),
                    mediumType: z.number().min(0, {
                        message: t(
                            'nav.digitizationStations.mediumTypeSelectionRequired',
                        ),
                    }),
                    expectedDailyCount: z.number().min(0, {
                        message: t(
                            'nav.digitizationStations.expectedCountMustBeGreaterOrEqual',
                        ),
                    }),
                }),
            )
            .min(1, {
                message: t(
                    'nav.digitizationStations.dailyTargetsMustNotBeEmpty',
                ),
            }),
        organizationCodes: z.array(z.string()).min(1, {
            message: t('nav.digitizationStations.selectAtLeastOneOrganization'),
        }),
        userIds: z.array(z.string()).optional().default([]),
    })

    const { data: selectedStation, isPending: stationLoading } =
        useGetDigitizationStationById(station_id!)

    const { data: buildings = [], isLoading: buildingsLoading } =
        useGetBuildings()

    const { flattenedNodes, isPending: orgLoading } = useGetOrgsStructure()
    const { usersAsOptions, isPending: loadingUsers } = useGetUsersByOrgNode(
        orgCode!,
    )

    // Transform flattenedNodes to orgOptions format
    const orgOptions =
        flattenedNodes?.map((node) => ({
            value: node.code,
            label: `${node.name} (${node.code})`,
        })) || []

    const [orgUsers, setOrgUsers] = useState<
        Record<string, Array<{ value: string; name: string }>>
    >({})
    const [selectedUsers, setSelectedUsers] = useState<
        Record<string, string[]>
    >({})

    const editData = selectedStation

    const paperSizeOptions = [
        { value: PaperSize.A4, label: 'A4' },
        { value: PaperSize.A3, label: 'A3' },
        { value: PaperSize.A2, label: 'A2' },
        { value: PaperSize.A1, label: 'A1' },
    ]

    const mediaTypeOptions = [
        {
            value: MediumType.PaperBased,
            label: t('nav.digitizationStations.paperBased'),
        },
        {
            value: MediumType.ElectronicWithoutPhysicalCopy,
            label: t('nav.digitizationStations.electronicWithoutPhysicalCopy'),
        },
        {
            value: MediumType.ElectronicWithPhysicalCopy,
            label: t('nav.digitizationStations.electronicWithPhysicalCopy'),
        },
    ]

    const {
        handleSubmit,
        formState: { errors },
        control,
        reset,
        setValue,
    } = useForm<DigitizationStationRequest>({
        defaultValues: {
            buildingId: 0,
            dailyTargets: [
                {
                    paperSize: 1,
                    mediumType: 1,
                    expectedDailyCount: 0,
                },
            ],
            organizationCodes: [],
            userIds: [],
        },
        resolver: zodResolver(validationSchema),
    })

    const watchedOrgs = useWatch({ control, name: 'organizationCodes' })
    const selectedOrgs = useMemo(() => watchedOrgs || [], [watchedOrgs])

    // Track which organization is currently being fetched
    const [fetchingOrgCode, setFetchingOrgCode] = useState<string | null>(null)

    // Handle organization changes and fetch users
    const handleOrganizationChange = async (orgCode: string) => {
        setOrgCode(orgCode)
    }

    // Watch for changes in usersAsOptions and update orgUsers when fetch completes
    useEffect(() => {
        if (fetchingOrgCode && !loadingUsers && usersAsOptions.length > 0) {
            setOrgUsers((prev) => ({
                ...prev,
                [fetchingOrgCode]: usersAsOptions,
            }))

            // In edit mode, select only the users that were previously selected
            // In add mode, select all users by default
            if (mode === 'edit' && editData?.users) {
                const editUserIds = editData.users
                const orgUserIds = usersAsOptions
                    .filter((user) => editUserIds.includes(user.value))
                    .map((user) => user.value)

                setSelectedUsers((prev) => ({
                    ...prev,
                    [fetchingOrgCode]: orgUserIds,
                }))
            } else {
                // Select all users by default for add mode
                setSelectedUsers((prev) => ({
                    ...prev,
                    [fetchingOrgCode]: usersAsOptions.map((user) => user.value),
                }))
            }
            setFetchingOrgCode(null)
        }
    }, [usersAsOptions, loadingUsers, fetchingOrgCode, mode, editData])

    // Handle user selection for an organization
    const handleUserSelection = (
        orgCode: string,
        userId: string,
        checked: boolean,
    ) => {
        setSelectedUsers((prev) => {
            const orgUserIds = prev[orgCode] || []
            if (checked) {
                return {
                    ...prev,
                    [orgCode]: [...orgUserIds, userId],
                }
            } else {
                return {
                    ...prev,
                    [orgCode]: orgUserIds.filter((id) => id !== userId),
                }
            }
        })
    }

    // Handle select all users for an organization
    const handleSelectAllUsers = (orgCode: string, checked: boolean) => {
        const users = orgUsers[orgCode] || []
        setSelectedUsers((prev) => ({
            ...prev,
            [orgCode]: checked ? users.map((user) => user.value) : [],
        }))
    }

    // Update form userIds when selectedUsers changes
    useEffect(() => {
        // Flatten all selected user arrays and remove duplicates
        const allSelectedUsers = [
            ...new Set(
                Object.values(selectedUsers).reduce(
                    (acc, curr) => [...acc, ...curr],
                    [],
                ),
            ),
        ]
        setValue('userIds', allSelectedUsers)
    }, [selectedUsers, setValue])

    // Fetch users when organizations change
    useEffect(() => {
        selectedOrgs.forEach((orgCode) => {
            handleOrganizationChange(orgCode)
        })
    }, [selectedOrgs])

    // Update form data when editing
    useEffect(() => {
        if (editData && !stationLoading && mode === 'edit') {
            reset({
                buildingId: editData.buildingId,
                dailyTargets: editData.dailyTargets,
                organizationCodes: editData.organizations || [],
                userIds: editData.users || [],
            })

            // Fetch users for all organizations in edit mode
            if (editData.organizations && editData.organizations.length > 0) {
                editData.organizations.forEach((orgCode) => {
                    if (!orgUsers[orgCode]) {
                        handleOrganizationChange(orgCode)
                    }
                })
            }
        } else if (mode === 'add') {
            reset({
                buildingId: 0,
                dailyTargets: [
                    {
                        paperSize: 1,
                        mediumType: 1,
                        expectedDailyCount: 0,
                    },
                ],
                organizationCodes: [],
                userIds: [],
            })
        }
    }, [editData, reset, mode, stationLoading, orgUsers])

    const onFormSubmit = async (values: DigitizationStationRequest) => {
        try {
            await onSubmit(values)
        } catch (error) {
            console.error('Form submission failed:', error)
        }
    }

    if (mode === 'edit' && stationLoading) {
        return (
            <div className="flex items-center justify-center p-8">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">
                        {t('nav.shared.loading')}...
                    </p>
                </div>
            </div>
        )
    }

    return (
        <Form
            className="flex w-full gap-4"
            containerClassName="flex flex-col w-full gap-4"
            onSubmit={handleSubmit(onFormSubmit)}
        >
            <Container className="flex flex-col gap-4 ">
                <Card className="w-full p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                    {/* Building Section */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormItem
                            className="mb-4"
                            label={t('nav.buildings.building')}
                            invalid={Boolean(errors.buildingId)}
                            errorMessage={errors.buildingId?.message}
                        >
                            <Controller
                                name="buildingId"
                                control={control}
                                render={({ field }) => (
                                    <Select
                                        options={buildings.map((building) => ({
                                            value: building.id,
                                            label: building.name,
                                        }))}
                                        placeholder={t(
                                            'nav.digitizationStations.selectBuilding',
                                        )}
                                        isLoading={buildingsLoading}
                                        value={buildings
                                            .map((building) => ({
                                                value: building.id,
                                                label: building.name,
                                            }))
                                            .find(
                                                (option) =>
                                                    option.value ===
                                                    field.value,
                                            )}
                                        onChange={(option) =>
                                            field.onChange(option?.value || 0)
                                        }
                                    />
                                )}
                            />
                        </FormItem>
                    </div>

                    {/* Daily Targets Section */}
                    <div className="mt-6">
                        <div className="flex items-center gap-3 mb-4">
                            <div className="flex items-center justify-center w-10 h-10 bg-primary-subtle rounded-lg">
                                <TbTarget className="w-5 h-5 text-primary-deep" />
                            </div>
                            <h4 className="text-lg font-semibold text-gray-900">
                                {t(
                                    'nav.digitizationStations.expectedDailyTarget',
                                )}
                            </h4>
                        </div>

                        <FormItem
                            className="mb-4"
                            invalid={Boolean(errors.dailyTargets)}
                            errorMessage={errors.dailyTargets?.message}
                        >
                            <Controller
                                name="dailyTargets"
                                control={control}
                                render={({ field }) => (
                                    <div className="space-y-4">
                                        <div className="flex justify-start">
                                            <Button
                                                type="button"
                                                size="sm"
                                                variant="solid"
                                                icon={<TbPlus />}
                                                onClick={() => {
                                                    const newTarget = {
                                                        paperSize: 1,
                                                        mediumType: 1,
                                                        expectedDailyCount: 0,
                                                    }
                                                    field.onChange([
                                                        ...(field.value || []),
                                                        newTarget,
                                                    ])
                                                }}
                                            >
                                                {t(
                                                    'nav.digitizationStations.addTarget',
                                                )}
                                            </Button>
                                        </div>

                                        {(!field.value ||
                                            field.value.length === 0) && (
                                            <p className="text-sm text-gray-500 text-center py-2">
                                                {t(
                                                    'nav.digitizationStations.dailyTargetsMustNotBeEmpty',
                                                )}
                                            </p>
                                        )}

                                        {field.value?.map((target, index) => (
                                            <div
                                                key={index}
                                                className="flex flex-col md:flex-row items-center gap-4 border border-gray-200 p-4 rounded-lg relative"
                                            >
                                                <div className="flex-1">
                                                    <label className="block mb-1 text-sm font-medium text-gray-700">
                                                        {t(
                                                            'nav.digitizationStations.paperSize',
                                                        )}
                                                    </label>
                                                    <Select
                                                        options={
                                                            paperSizeOptions
                                                        }
                                                        value={paperSizeOptions.find(
                                                            (option) =>
                                                                option.value ===
                                                                target.paperSize,
                                                        )}
                                                        onChange={(option) => {
                                                            const newTargets = [
                                                                ...field.value,
                                                            ]
                                                            newTargets[index] =
                                                                {
                                                                    ...target,
                                                                    paperSize:
                                                                        option?.value ||
                                                                        1,
                                                                }
                                                            field.onChange(
                                                                newTargets,
                                                            )
                                                        }}
                                                    />
                                                </div>
                                                <div className="flex-1">
                                                    <label className="block mb-1 text-sm font-medium text-gray-700">
                                                        {t(
                                                            'nav.digitizationStations.mediumType',
                                                        )}
                                                    </label>
                                                    <Select
                                                        options={
                                                            mediaTypeOptions
                                                        }
                                                        value={mediaTypeOptions.find(
                                                            (option) =>
                                                                option.value ===
                                                                target.mediumType,
                                                        )}
                                                        onChange={(option) => {
                                                            const newTargets = [
                                                                ...field.value,
                                                            ]
                                                            newTargets[index] =
                                                                {
                                                                    ...target,
                                                                    mediumType:
                                                                        option?.value ||
                                                                        1,
                                                                }
                                                            field.onChange(
                                                                newTargets,
                                                            )
                                                        }}
                                                    />
                                                </div>
                                                <div className="flex-1">
                                                    <label className="block mb-1 text-sm font-medium text-gray-700">
                                                        {t(
                                                            'nav.digitizationStations.expectedDailyCount',
                                                        )}
                                                    </label>
                                                    <Input
                                                        type="number"
                                                        min="0"
                                                        value={
                                                            target.expectedDailyCount
                                                        }
                                                        onChange={(e) => {
                                                            const newTargets = [
                                                                ...field.value,
                                                            ]
                                                            newTargets[index] =
                                                                {
                                                                    ...target,
                                                                    expectedDailyCount:
                                                                        parseInt(
                                                                            e
                                                                                .target
                                                                                .value,
                                                                        ) || 0,
                                                                }
                                                            field.onChange(
                                                                newTargets,
                                                            )
                                                        }}
                                                        className="border rounded px-2 py-1 w-full"
                                                    />
                                                </div>
                                                {field.value &&
                                                    field.value.length > 1 && (
                                                        <Button
                                                            type="button"
                                                            size="sm"
                                                            variant="plain"
                                                            icon={<TbTrash />}
                                                            className="absolute top-2 left-2"
                                                            onClick={() => {
                                                                const newTargets =
                                                                    [
                                                                        ...field.value,
                                                                    ]
                                                                newTargets.splice(
                                                                    index,
                                                                    1,
                                                                )
                                                                field.onChange(
                                                                    newTargets,
                                                                )
                                                            }}
                                                        />
                                                    )}
                                            </div>
                                        ))}
                                    </div>
                                )}
                            />
                        </FormItem>
                    </div>

                    {/* Organizations and Users section */}
                    <div className="mt-6">
                        <div className="flex items-center gap-3 mb-4">
                            <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-lg">
                                <TbBuilding className="w-5 h-5 text-blue-600" />
                            </div>
                            <h4 className="text-lg font-semibold text-gray-900">
                                {t('nav.digitizationStations.organizations')}
                            </h4>
                        </div>

                        <FormItem
                            className="mb-4"
                            label={t('nav.digitizationStations.organizations')}
                            invalid={Boolean(errors.organizationCodes)}
                            errorMessage={errors.organizationCodes?.message}
                        >
                            <Controller
                                name="organizationCodes"
                                control={control}
                                render={({ field }) => (
                                    <Select
                                        options={orgOptions}
                                        placeholder={t(
                                            'nav.shared.selectOrganizations',
                                        )}
                                        isLoading={orgLoading}
                                        isMulti
                                        value={orgOptions.filter((option) =>
                                            field.value?.includes(option.value),
                                        )}
                                        onChange={(options) => {
                                            const newValues =
                                                options?.map((o) => o.value) ||
                                                []
                                            field.onChange(newValues)

                                            // Clean up selectedUsers for removed organizations
                                            setSelectedUsers((prev) => {
                                                const newSelectedUsers = {
                                                    ...prev,
                                                }
                                                Object.keys(
                                                    newSelectedUsers,
                                                ).forEach((orgCode) => {
                                                    if (
                                                        !newValues.includes(
                                                            orgCode,
                                                        )
                                                    ) {
                                                        delete newSelectedUsers[
                                                            orgCode
                                                        ]
                                                    }
                                                })
                                                return newSelectedUsers
                                            })
                                        }}
                                    />
                                )}
                            />
                        </FormItem>

                        {/* Users selection for each organization */}
                        {selectedOrgs.length > 0 && (
                            <div className="mt-6">
                                <div className="flex items-center gap-3 mb-4">
                                    <div className="flex items-center justify-center w-10 h-10 bg-green-100 rounded-lg">
                                        <TbUsers className="w-5 h-5 text-green-600" />
                                    </div>
                                    <h4 className="text-lg font-semibold text-gray-900">
                                        {t('nav.digitizationStations.users')}
                                    </h4>
                                </div>

                                <div className="space-y-6">
                                    {selectedOrgs.map((orgCode) => {
                                        const orgName =
                                            orgOptions.find(
                                                (opt) => opt.value === orgCode,
                                            )?.label ||
                                            `${orgCode} (${orgCode})`
                                        const users = orgUsers[orgCode] || []
                                        const selectedOrgUsers =
                                            selectedUsers[orgCode] || []
                                        const isAllSelected =
                                            users.length > 0 &&
                                            selectedOrgUsers.length ===
                                                users.length

                                        return (
                                            <Card
                                                key={orgCode}
                                                className="p-4 border border-gray-200"
                                            >
                                                <div className="flex items-center justify-between mb-4">
                                                    <h5 className="font-medium text-gray-900">
                                                        {orgName}
                                                    </h5>
                                                    {users.length > 0 && (
                                                        <Checkbox
                                                            checked={
                                                                isAllSelected
                                                            }
                                                            // indeterminate={
                                                            //     selectedOrgUsers.length >
                                                            //         0 &&
                                                            //     !isAllSelected
                                                            // }
                                                            onChange={(
                                                                checked,
                                                            ) =>
                                                                handleSelectAllUsers(
                                                                    orgCode,
                                                                    checked,
                                                                )
                                                            }
                                                        >
                                                            {t(
                                                                'nav.shared.selectAll',
                                                            )}
                                                        </Checkbox>
                                                    )}
                                                </div>

                                                {loadingUsers &&
                                                !users.length ? (
                                                    <div className="flex items-center justify-center py-4">
                                                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                                                        <span className="ml-2 text-gray-600">
                                                            {t(
                                                                'nav.shared.loading',
                                                            )}
                                                            ...
                                                        </span>
                                                    </div>
                                                ) : users.length > 0 ? (
                                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                                        {users.map((user) => (
                                                            <Checkbox
                                                                key={user.value}
                                                                checked={selectedOrgUsers.includes(
                                                                    user.value,
                                                                )}
                                                                className="text-sm"
                                                                onChange={(
                                                                    checked,
                                                                ) =>
                                                                    handleUserSelection(
                                                                        orgCode,
                                                                        user.value,
                                                                        checked,
                                                                    )
                                                                }
                                                            >
                                                                {user.name}
                                                            </Checkbox>
                                                        ))}
                                                    </div>
                                                ) : (
                                                    <p className="text-gray-500 text-center py-4">
                                                        {t(
                                                            'nav.shared.noUsersFound',
                                                        )}
                                                    </p>
                                                )}
                                            </Card>
                                        )
                                    })}
                                </div>
                            </div>
                        )}
                    </div>
                </Card>
            </Container>
            <BottomStickyBar>{children}</BottomStickyBar>
        </Form>
    )
}

export default DigitizedForm
