/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback, useMemo, useState } from 'react'
import { DataTable } from '@/components/shared'
import useTranslation from '@/utils/hooks/useTranslation'
import { Tb<PERSON><PERSON>cil, TbTrash } from 'react-icons/tb'
// import { FiEye } from 'react-icons/fi'
import { Tooltip } from '@/components/ui'

import ConfirmDialog from '@/components/shared/ConfirmDialog'
import { useGetDocumentTypes } from '@/hooks/document-types'
import Status from '@/components/shared/displaying/Status'

interface DocumentTypesTableProps {
    onEdit?: (id: number) => void
}

const DocumentTypesTable = ({ onEdit }: DocumentTypesTableProps) => {
    const { data: documentTypes = [], isPending } = useGetDocumentTypes()

    const { t } = useTranslation()

    const [tableData, setTableData] = useState({
        pageIndex: 1,
        pageSize: 10,
        sort: {
            order: '',
            key: '',
        },
        query: '',
        total: 0,
    })

    const [deleteConfirmationOpen, setDeleteConfirmationOpen] = useState(false)

    // Handle pagination changes
    const handlePaginationChange = (pageIndex: number) => {
        setTableData((prevData) => ({ ...prevData, pageIndex }))
    }

    // Handle sort changes
    const handleSort = (sort: { order: string; key: string }) => {
        setTableData((prevData) => ({ ...prevData, sort }))
    }

    // Handle edit action
    const handleEdit = useCallback(
        (id: number) => {
            if (onEdit) {
                onEdit(id)
            }
        },
        [onEdit],
    )

    // Handle delete confirmation
    const handleDeleteConfirmation = useCallback(() => {
        setDeleteConfirmationOpen(true)
    }, [])

    // Handle delete cancellation
    const handleCancel = () => {
        setDeleteConfirmationOpen(false)
    }

    // Handle delete confirmation
    const handleConfirmDelete = async () => {
        // if (selectedDocumentTypeId !== null) {
        //     await updateStatus(selectedDocumentTypeId.toString(), 0)
        //     setDeleteConfirmationOpen(false)
        //     getDocumentTypes() // Refresh the list
        // }
    }

    // Define table columns
    const columns = useMemo(
        () => [
            {
                header: 'ID',
                accessorKey: 'id',
                cell: (props: any) => {
                    const row = props.row.original
                    return (
                        <span className="font-medium text-gray-900 dark:text-gray-100">
                            {row.id}
                        </span>
                    )
                },
            },
            {
                header: t('nav.documentTypes.name'),
                accessorKey: 'name',
                cell: (props: any) => {
                    const row = props.row.original
                    return (
                        <span className="font-bold text-gray-900 dark:text-gray-100">
                            {row.name}
                        </span>
                    )
                },
            },
            {
                header: t('nav.shared.status'),
                accessorKey: 'status',
                cell: (props: any) => <Status row={props.row} />,
            },
            {
                header: t('nav.shared.edit'),
                id: 'actions',
                cell: (props: any) => {
                    const row = props.row.original
                    return (
                        <div className="flex justify-center items-center gap-2 text-lg">
                            <Tooltip title={t('nav.shared.view')}>
                                {/* <span
                                    className="cursor-pointer hover:text-blue-500"
                                    onClick={() =>
                                        handleDocumentTypeView(row.id)
                                    }
                                >
                                    <FiEye />
                                </span> */}
                            </Tooltip>
                            <Tooltip title={t('nav.shared.edit')}>
                                <span
                                    className="text-xl cursor-pointer select-none font-semibold text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 transition-colors"
                                    onClick={() => handleEdit(row.id)}
                                >
                                    <TbPencil />
                                </span>
                            </Tooltip>
                            <Tooltip title={t('nav.shared.delete')}>
                                <span
                                    className="text-xl cursor-pointer select-none font-semibold text-gray-600 hover:text-red-600 dark:text-gray-300 dark:hover:text-red-400 transition-colors"
                                    onClick={() => handleDeleteConfirmation()}
                                >
                                    <TbTrash />
                                </span>
                            </Tooltip>
                        </div>
                    )
                },
            },
        ],
        [handleDeleteConfirmation, handleEdit, t],
    )

    return (
        <>
            <DataTable
                selectable
                columns={columns}
                data={documentTypes}
                noData={!isPending && documentTypes.length === 0}
                skeletonAvatarColumns={[0]}
                skeletonAvatarProps={{ width: 28, height: 28 }}
                loading={isPending}
                pagingData={{
                    total: documentTypes.length,
                    pageIndex: tableData.pageIndex as number,
                    pageSize: tableData.pageSize as number,
                }}
                checkboxChecked={() => false}
                cellBorder={true}
                onPaginationChange={handlePaginationChange}
                onSort={(sort) =>
                    handleSort({ order: sort.order, key: String(sort.key) })
                }
                onCheckBoxChange={(checked: boolean, row: any) => {
                    console.log('🔍 DataTable onCheckBoxChange called:', {
                        checked,
                        rowId: row.id,
                    })
                }}
                onIndeterminateCheckBoxChange={(checked: boolean) => {
                    console.log(
                        '🔍 DataTable onIndeterminateCheckBoxChange called:',
                        { checked },
                    )
                }}
            />

            <ConfirmDialog
                isOpen={deleteConfirmationOpen}
                type="danger"
                title={t('nav.documentTypes.removeDocumentType')}
                onClose={handleCancel}
                onRequestClose={handleCancel}
                onCancel={handleCancel}
                onConfirm={handleConfirmDelete}
            >
                <p>{t('nav.documentTypes.deleteConfirmation')}</p>
            </ConfirmDialog>
        </>
    )
}

export default DocumentTypesTable
