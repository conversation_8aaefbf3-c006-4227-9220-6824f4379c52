/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMemo } from 'react'
import { useSearchParams } from 'react-router-dom'
import DataTable from '@/components/shared/DataTable'
import type { ColumnDef } from '@/components/shared/DataTable'
import useTranslation from '@/utils/hooks/useTranslation'
import { Document } from '@/@types/document'
import { useGetDocumentsByFile } from '@/hooks/documents'
import Status from '@/components/shared/displaying/Status'
import Actions from './Actions'

const DocumentsListTable = ({ fileId }: { fileId: string }) => {
    const { t } = useTranslation()
    const [searchParams, setSearchParams] = useSearchParams()
    const { Documents, isLoading, pagination } = useGetDocumentsByFile(fileId)

    const handlePaginationChange = (page: number) => {
        const newSearchParams = new URLSearchParams(searchParams)
        newSearchParams.set('pageNumber', page.toString())
        setSearchParams(newSearchParams)
    }

    const handlePageSizeChange = (pageSize: number) => {
        const newSearchParams = new URLSearchParams(searchParams)
        newSearchParams.set('pageSize', pageSize.toString())
        newSearchParams.set('pageNumber', '1') // Reset to first page when changing page size
        setSearchParams(newSearchParams)
    }

    const tableColumns: ColumnDef<Document>[] = useMemo(() => {
        const cols: ColumnDef<Document>[] = [
            {
                header: 'ID',
                accessorKey: 'documentId',
                cell: ({ row }) => (
                    <div className="font-mono text-sm">
                        {row.original.documentId}
                    </div>
                ),
            },
            {
                header: 'Title',
                accessorKey: 'title',
                cell: ({ row }) => (
                    <div className="font-medium">{row.original.title}</div>
                ),
            },
            {
                header: 'Types',
                accessorKey: 'documentTypeName',
            },
            {
                header: 'Pages',
                accessorKey: 'pagesCount',
            },
            {
                header: 'Date',
                accessorKey: 'date',
                cell: ({ row }) => (
                    <div className="text-sm">
                        {row.original.date
                            ? new Date(row.original.date).toLocaleDateString()
                            : '-'}
                    </div>
                ),
                enableSorting: false,
            },
            {
                header: 'Status',
                accessorKey: 'status',
                cell: (props: any) => <Status row={props.row} />,
            },
            {
                header: t('nav.shared.actions'),
                accessorKey: 'action',
                cell: ({ row }) => <Actions documentData={row.original} />,
                enableSorting: false,
            },
        ]

        return cols
    }, [t])

    return (
        <>
            <DataTable
                selectable
                columns={tableColumns}
                data={Documents}
                loading={isLoading}
                skeletonAvatarColumns={[0]}
                skeletonAvatarProps={{ width: 14, height: 14 }}
                cellBorder={true}
                pagingData={{
                    total: pagination?.totalCount || 0,
                    pageIndex: pagination?.pageNumber || 1,
                    pageSize: pagination?.pageSize || 10,
                }}
                onPaginationChange={handlePaginationChange}
                onSelectChange={handlePageSizeChange}
            />
        </>
    )
}

export default DocumentsListTable
