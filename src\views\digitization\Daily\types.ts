// Enums
export enum FileStatus {
    Open = 0,
    Close = 1,
}

export enum Confidentiality {
    NonConfidential = 0,
    Confidential = 1,
    HighlyConfidential = 2,
}

export enum DigitizationType {
    Daily = 2,
    Backlog = 4,
}

export enum MediumType {
    PaperBased = 0,
    ElectronicWithoutPhysicalCopy = 1,
    ElectronicWithPhysicalCopy = 2,
}

// Type definitions for UI metadata
export interface StatusConfig {
    value: number
    label: string
    description: string
    className: string
    icon: string
    color: string
}

// File Status configurations
export const FILE_STATUS_CONFIG: Record<FileStatus, StatusConfig> = {
    [FileStatus.Open]: {
        value: 0,
        label: 'Open',
        description: 'File is currently open and available for processing',
        className: 'bg-green-100 text-green-800 border-green-200',
        icon: 'TbFolderOpen',
        color: 'green',
    },
    [FileStatus.Close]: {
        value: 1,
        label: 'Closed',
        description: 'File has been closed and processing is complete',
        className: 'bg-gray-100 text-gray-800 border-gray-200',
        icon: 'TbFolder',
        color: 'gray',
    },
}

// Confidentiality Level configurations
export const CONFIDENTIALITY_CONFIG = {
    [Confidentiality.NonConfidential]: {
        value: 0,
        label: 'Non-Confidential',
        description:
            'Non-confidential files accessible to all authorized users',
        className: 'bg-blue-100 text-blue-800 border-blue-200',
        icon: 'TbEye',
        color: 'blue',
    },
    [Confidentiality.Confidential]: {
        value: 1,
        label: 'Confidential',
        description: 'Confidential files with restricted access',
        className: 'bg-yellow-100 text-yellow-800 border-yellow-200',
        icon: 'TbEyeOff',
        color: 'yellow',
    },
    [Confidentiality.HighlyConfidential]: {
        value: 2,
        label: 'Highly Confidential',
        description: 'Highly confidential files with very restricted access',
        className: 'bg-red-100 text-red-800 border-red-200',
        icon: 'TbShieldLock',
        color: 'red',
    },
}

// Digitization Type configurations
export const DIGITIZATION_TYPE_CONFIG = {
    [DigitizationType.Daily]: {
        value: 2,
        label: 'Daily',
        description: 'Daily digitization files processed on regular schedule',
        className: 'bg-purple-100 text-purple-800 border-purple-200',
        icon: 'TbCalendarEvent',
        color: 'purple',
    },
    [DigitizationType.Backlog]: {
        value: 4,
        label: 'Backlog',
        description: 'Backlog digitization files from previous periods',
        className: 'bg-orange-100 text-orange-800 border-orange-200',
        icon: 'TbArchive',
        color: 'orange',
    },
}

// Medium Type configurations
export const MEDIUM_TYPE_CONFIG = {
    [MediumType.PaperBased]: {
        value: 0,
        label: 'Paper Based',
        description: 'Physical paper documents requiring digitization',
        className: 'bg-amber-100 text-amber-800 border-amber-200',
        icon: 'TbFileText',
        color: 'amber',
    },
    [MediumType.ElectronicWithoutPhysicalCopy]: {
        value: 1,
        label: 'Electronic Only',
        description: 'Born digital without physical copy',
        className: 'bg-cyan-100 text-cyan-800 border-cyan-200',
        icon: 'TbDeviceDesktop',
        color: 'cyan',
    },
    [MediumType.ElectronicWithPhysicalCopy]: {
        value: 2,
        label: 'Electronic + Physical',
        description: 'Born digital with accompanying physical copy',
        className: 'bg-indigo-100 text-indigo-800 border-indigo-200',
        icon: 'TbFiles',
        color: 'indigo',
    },
}
