/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import Button from '@/components/ui/Button'
import Loading from '@/components/shared/Loading'
import { TbP<PERSON>, TbBox, TbStack3, TbChevronRight } from 'react-icons/tb'
import useTranslation from '@/utils/hooks/useTranslation'
import ShelfCard from './components/ShelfCard'
import AddShelfModal from '../WarehouseShelves/components/AddShelfModal'
import EditShelfModal from '../WarehouseShelves/components/EditShelfModal'
import type { Shelf } from '@/@types/warehouse'
import {
    useAddShelvesToUnit,
    useDeleteWarehouseShelf,
    useGetWarehouseUnit,
    useUpdateWarehouseShelf,
} from '@/hooks/warehouses'

const UnitShelves = () => {
    const { t } = useTranslation()
    const navigate = useNavigate()
    const { warehouseId, areaId, unitId } = useParams<{
        warehouseId: string
        areaId: string
        unitId: string
    }>()

    const { data: unit, isLoading } = useGetWarehouseUnit(
        warehouseId!,
        areaId!,
        unitId!,
    )
    const { mutate: addShelvesToUnit } = useAddShelvesToUnit()
    const { mutate: updateWarehouseShelf } = useUpdateWarehouseShelf()
    const { mutate: deleteWarehouseShelf } = useDeleteWarehouseShelf()

    const [addModalOpen, setAddModalOpen] = useState(false)
    const [editModalOpen, setEditModalOpen] = useState(false)
    const [selectedShelf, setSelectedShelf] = useState<Shelf | null>(null)

    const handleAddShelf = async (data: any) => {
        if (warehouseId && areaId && unitId) {
            try {
                // Create shelves based on form data
                const shelves = {
                    numberOfShelves: data.numberOfShelves,
                    height: data.height,
                    width: data.width,
                    numberOfBoxes: data.numberOfBoxes,
                }

                await addShelvesToUnit({
                    warehouseId: warehouseId!,
                    areaId: areaId!,
                    unitId: unitId!,
                    shelves,
                })
                setAddModalOpen(false)
            } catch (error) {
                console.error('Error adding shelf:', error)
            }
        }
    }

    const handleEditShelf = (shelfId: string) => {
        const shelf = unit?.shelves?.find((s) => s.id === shelfId)
        if (shelf) {
            setSelectedShelf(shelf)
            setEditModalOpen(true)
        }
    }

    const handleUpdateShelf = async (data: any) => {
        if (warehouseId && areaId && unitId && selectedShelf) {
            try {
                const shelf = {
                    length: data.maxBoxes,
                    width: data.width,
                    height: data.height,
                }
                await updateWarehouseShelf({
                    warehouseId: warehouseId!,
                    areaId: areaId!,
                    unitId: unitId!,
                    shelfId: selectedShelf.id,
                    shelf,
                })
                setEditModalOpen(false)
                setSelectedShelf(null)
            } catch (error) {
                console.error('Error updating shelf:', error)
            }
        }
    }

    const handleDeleteShelf = async (shelfId: string) => {
        try {
            await deleteWarehouseShelf({
                warehouseId: warehouseId!,
                areaId: areaId!,
                unitId: unitId!,
                shelfId,
            })
        } catch (error) {
            console.error('Error deleting shelf:', error)
        }
    }

    const handleManageBoxes = (shelfId: string) => {
        navigate(
            `/warehouses/areas/${warehouseId}/units/${areaId}/shelves/${unitId}/boxes/${shelfId}`,
        )
    }

    // Calculate totals from unit data
    const shelves = unit?.shelves || []
    const totalShelves = unit?.numberOfShelves || shelves.length
    const totalBoxes = unit?.numberOfBoxes || 0
    const availableBoxes = unit?.availableBoxes || 0
    const utilizationPercentage =
        totalBoxes > 0
            ? Math.round(((totalBoxes - availableBoxes) / totalBoxes) * 100)
            : 0

    if (isLoading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <Loading loading={true} />
            </div>
        )
    }

    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
            <div className="container mx-auto px-4 py-6">
                {/* Breadcrumb Navigation */}
                <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-6">
                    <button
                        className="hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                        onClick={() => navigate('/warehouses/warehouses')}
                    >
                        {t('nav.warehouses.warehouses')}
                    </button>
                    <TbChevronRight className="w-4 h-4" />
                    <button
                        className="hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                        onClick={() =>
                            navigate(`/warehouses/${warehouseId}/areas`)
                        }
                    >
                        {warehouseId}
                    </button>
                    <TbChevronRight className="w-4 h-4" />
                    <button
                        className="hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                        onClick={() =>
                            navigate(
                                `/warehouses/${warehouseId}/areas/${areaId}/area`,
                            )
                        }
                    >
                        {areaId}
                    </button>
                    <TbChevronRight className="w-4 h-4" />
                    <span className="text-gray-900 dark:text-gray-100 font-medium">
                        {unitId}
                    </span>
                    <TbChevronRight className="w-4 h-4" />
                    <span className="text-gray-900 dark:text-gray-100 font-medium">
                        {t('nav.warehouses.shelves')}
                    </span>
                </div>

                {/* Enhanced Header with Stats */}
                <div className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl p-6 mb-6 border border-purple-200 dark:border-purple-800">
                    <div className="flex items-center justify-between mb-4">
                        <div>
                            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                                {unitId}
                            </h1>
                            <p className="text-gray-600 dark:text-gray-400">
                                {t('nav.warehouses.manageShelvesInUnit')}
                            </p>
                        </div>
                        <Button
                            variant="solid"
                            size="sm"
                            icon={<TbPlus />}
                            onClick={() => setAddModalOpen(true)}
                            className="bg-purple-600 hover:bg-purple-700 text-white"
                        >
                            {t('nav.warehouses.addShelf')}
                        </Button>
                    </div>

                    {/* Stats Grid */}
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <div className="flex items-center gap-3">
                                <div className="flex items-center justify-center w-10 h-10 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                                    <TbStack3 className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                                </div>
                                <div>
                                    <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                                        {totalShelves}
                                    </div>
                                    <div className="text-sm text-gray-600 dark:text-gray-400">
                                        {t('nav.warehouses.totalShelves')}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <div className="flex items-center gap-3">
                                <div className="flex items-center justify-center w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                                    <TbBox className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                                </div>
                                <div>
                                    <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                                        {totalBoxes}
                                    </div>
                                    <div className="text-sm text-gray-600 dark:text-gray-400">
                                        {t('nav.warehouses.totalBoxes')}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <div className="flex items-center gap-3">
                                <div className="flex items-center justify-center w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg">
                                    <TbBox className="w-5 h-5 text-green-600 dark:text-green-400" />
                                </div>
                                <div>
                                    <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                                        {availableBoxes}
                                    </div>
                                    <div
                                        className="text-sm text-gray-600 dark:text-gray-400"
                                        dir="auto"
                                    >
                                        {t('nav.warehouses.availableBoxes')}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <div className="flex items-center gap-3">
                                <div className="flex items-center justify-center w-10 h-10 bg-orange-100 dark:bg-orange-900/30 rounded-lg">
                                    <TbBox className="w-5 h-5 text-orange-600 dark:text-orange-400" />
                                </div>
                                <div>
                                    <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                                        {utilizationPercentage}%
                                    </div>
                                    <div
                                        className="text-sm text-gray-600 dark:text-gray-400"
                                        dir="auto"
                                    >
                                        {t('nav.warehouses.utilized')}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Shelves Grid */}
                {shelves.length === 0 ? (
                    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-12 text-center">
                        <div className="flex items-center justify-center w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full mx-auto mb-4">
                            <TbStack3 className="w-8 h-8 text-gray-400" />
                        </div>
                        <h3
                            className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2"
                            dir="auto"
                        >
                            {t('nav.warehouses.noShelvesFound')}
                        </h3>
                        <p
                            className="text-gray-600 dark:text-gray-400 mb-6"
                            dir="auto"
                        >
                            {t('nav.warehouses.noShelvesDesc')}
                        </p>
                        <Button
                            variant="solid"
                            icon={<TbPlus />}
                            onClick={() => setAddModalOpen(true)}
                            className="bg-purple-600 hover:bg-purple-700 text-white"
                        >
                            {t('nav.warehouses.addFirstShelf')}
                        </Button>
                    </div>
                ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {shelves.map((shelf) => (
                            <ShelfCard
                                key={shelf.id}
                                shelf={shelf}
                                warehouseId={warehouseId!}
                                areaId={areaId!}
                                unitId={unitId!}
                                onEdit={handleEditShelf}
                                onDelete={handleDeleteShelf}
                                onManageBoxes={handleManageBoxes}
                            />
                        ))}
                    </div>
                )}
            </div>

            {/* Add Shelf Modal */}
            <AddShelfModal
                isOpen={addModalOpen}
                onClose={() => setAddModalOpen(false)}
                onSubmit={handleAddShelf}
            />

            {/* Edit Shelf Modal */}
            <EditShelfModal
                isOpen={editModalOpen}
                onClose={() => {
                    setEditModalOpen(false)
                    setSelectedShelf(null)
                }}
                onSubmit={handleUpdateShelf}
                initialData={
                    selectedShelf
                        ? {
                              length: selectedShelf.height,
                              width: selectedShelf.width,
                              maxBoxes: selectedShelf.numberOfBoxes,
                          }
                        : null
                }
            />
        </div>
    )
}

export default UnitShelves
