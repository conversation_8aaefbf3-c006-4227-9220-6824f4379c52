import { Badge } from '@/components/ui'
import { useGetAccount } from '@/hooks/auth'

/**
 * Component to display current user's authority/roles
 * Useful for debugging and development
 */
const UserAuthorityDisplay = () => {
    const { data: user } = useGetAccount()

    const roles = localStorage.getItem('roles')

    if (!user) {
        return (
            <div className="p-4 bg-gray-100 rounded-lg">
                <p className="text-sm text-gray-600">No user logged in</p>
            </div>
        )
    }

    return (
        <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h3 className="text-sm font-semibold text-blue-800 mb-2">
                User Authority Debug
            </h3>
            <div className="space-y-2">
                <div>
                    <span className="text-xs text-gray-600">User:</span>
                    <span className="ml-2 text-sm font-medium">
                        {user.firstName} {user.lastName} ({user.email})
                    </span>
                </div>
                <div>
                    <span className="text-xs text-gray-600">Roles:</span>
                    <div className="ml-2 flex flex-wrap gap-1 mt-1">
                        {roles ? (
                            roles.split(',').map((role, index) => (
                                <Badge
                                    key={index}
                                    className="text-xs"
                                    // variant="solid"
                                >
                                    {role}
                                </Badge>
                            ))
                        ) : (
                            <span className="text-xs text-red-500">
                                No roles found
                            </span>
                        )}
                    </div>
                </div>
                <div className="text-xs text-gray-500 mt-2">
                    <details>
                        <summary className="cursor-pointer">
                            Raw user data
                        </summary>
                        <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto">
                            {JSON.stringify(user, null, 2)}
                        </pre>
                    </details>
                </div>
            </div>
        </div>
    )
}

export default UserAuthorityDisplay
