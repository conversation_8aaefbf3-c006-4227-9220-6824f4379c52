import { useState } from 'react'
import Button from '@/components/ui/Button'
import { TbCloudDownload, TbPlus } from 'react-icons/tb'
import { CSVLink } from 'react-csv'
import BuildingModal from './BuildingModal'
import useTranslation from '@/utils/hooks/useTranslation'
import { useGetBuildings } from '@/hooks/building'

const BuildingListActionTools = () => {
    const [isModalOpen, setIsModalOpen] = useState(false)
    const { data: buildings = [] } = useGetBuildings()
    const { t } = useTranslation()

    const handleAddBuilding = () => {
        setIsModalOpen(true)
    }

    const handleModalClose = () => {
        setIsModalOpen(false)
    }

    return (
        <>
            <div className="flex  gap-3">
                {/* todo: handle export */}
                <CSVLink
                    filename="buildings-list.csv"
                    data={buildings.map((building) => ({
                        Name: building.name,
                        Village: building.villageNameAr,
                        City: building.cityOrDistrictNameAr,
                        Governorate: building.governorateNameAr,
                        Country: building.countryNameAr,
                        Status: building.status === 1 ? 'معتمد' : 'غير معتمد',
                    }))}
                >
                    <Button
                        icon={<TbCloudDownload className="text-xl" />}
                        size="sm"
                        className="shadow-sm hover:shadow-md transition-all duration-200 border-gray-300 hover:border-gray-400 dark:border-gray-700 dark:hover:border-gray-600"
                    >
                        {t('nav.shared.export')}
                    </Button>
                </CSVLink>
                <Button
                    variant="solid"
                    size="sm"
                    icon={<TbPlus className="text-xl" />}
                    className="shadow-sm hover:shadow-md transition-all duration-200 bg-primary-mild hover:bg-primary-deep"
                    onClick={handleAddBuilding}
                >
                    {t('nav.buildings.addBuilding')}
                </Button>
            </div>

            <BuildingModal isOpen={isModalOpen} onClose={handleModalClose} />
        </>
    )
}

export default BuildingListActionTools
