/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useState } from 'react'
import Dialog from '@/components/ui/Dialog'
import { Form, FormItem } from '@/components/ui/Form'
import Button from '@/components/ui/Button'
import Alert from '@/components/ui/Alert'
import PasswordInput from '@/components/shared/PasswordInput'
import { useForm, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useTranslation } from 'react-i18next'
import { HiLockClosed } from 'react-icons/hi'
import type { ZodType } from 'zod'
import { useChangePassword, useGetAccount } from '@/hooks/auth'

type ChangePasswordFormSchema = {
    currentPassword: string
    newPassword: string
    confirmPassword: string
}

const ChangePasswordDialog = () => {
    const { t } = useTranslation()
    const { mutate: changePassword, isPending: isChangingPassword } =
        useChangePassword()

    const [isSubmitting, setIsSubmitting] = useState(false)
    const [submitError, setSubmitError] = useState<string | null>(null)
    const [submitSuccess, setSubmitSuccess] = useState(false)
    const [isOpen, setIsOpen] = useState(false)

    useEffect(() => {
        if (localStorage.getItem('token')) {
            const { data: user } = useGetAccount()
            const changedPass = user?.mustChangePassword
            if (changedPass) {
                setIsOpen(true)
            } else {
                setIsOpen(false)
            }
        }
    }, [isChangingPassword])

    const validationSchema: ZodType<ChangePasswordFormSchema> = z
        .object({
            currentPassword: z.string().min(1, {
                message:
                    t('nav.authentication.currentPasswordRequired') ||
                    'Current password is required',
            }),
            newPassword: z
                .string()
                .min(8, {
                    message:
                        t('nav.authentication.passwordTooShort') ||
                        'Password must be at least 8 characters long',
                })
                .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, {
                    message:
                        t('nav.authentication.passwordComplexity') ||
                        'Password must contain at least one uppercase letter, one lowercase letter, and one number',
                }),
            confirmPassword: z.string().min(1, {
                message:
                    t('nav.authentication.confirmPasswordRequired') ||
                    'Please confirm your new password',
            }),
        })
        .refine((data) => data.newPassword === data.confirmPassword, {
            message:
                t('nav.authentication.passwordsDoNotMatch') ||
                'Passwords do not match',
            path: ['confirmPassword'],
        })

    const {
        handleSubmit,
        control,
        formState: { errors },
    } = useForm<ChangePasswordFormSchema>({
        resolver: zodResolver(validationSchema),
        defaultValues: {
            currentPassword: '',
            newPassword: '',
            confirmPassword: '',
        },
    })

    const onSubmit = (values: ChangePasswordFormSchema) => {
        setIsSubmitting(true)
        setSubmitError(null)
        setSubmitSuccess(false)

        changePassword({
            currentPassword: values.currentPassword,
            newPassword: values.newPassword,
        })

        setIsSubmitting(false)
    }

    return (
        <Dialog
            isOpen={isOpen}
            shouldCloseOnEsc={false}
            shouldCloseOnOverlayClick={false}
            width={500}
            closable={false}
            className="change-password-dialog"
        >
            <div className="flex flex-col p-6">
                <div className="flex items-center gap-3 mb-6">
                    <div className="flex items-center justify-center w-12 h-12 bg-amber-100 rounded-full">
                        <HiLockClosed className="w-6 h-6 text-amber-600" />
                    </div>
                    <div>
                        <h3 className="text-lg font-semibold text-gray-900">
                            {t('nav.authentication.passwordChangeRequired')}
                        </h3>
                        <p className="text-sm text-gray-600">
                            {t('nav.authentication.defaultPasswordMessage')}
                        </p>
                    </div>
                </div>

                {submitSuccess && (
                    <Alert showIcon className="mb-4" type="success">
                        {t('nav.authentication.passwordChangeSuccess')}
                    </Alert>
                )}

                {submitError && (
                    <Alert showIcon className="mb-4" type="danger">
                        {submitError || 'An error occurred'}
                    </Alert>
                )}

                <Form onSubmit={handleSubmit(onSubmit)}>
                    <FormItem
                        label={t('nav.authentication.currentPassword')}
                        invalid={Boolean(errors.currentPassword)}
                        errorMessage={errors.currentPassword?.message}
                        className="mb-4"
                    >
                        <Controller
                            name="currentPassword"
                            control={control}
                            render={({ field }) => (
                                <PasswordInput
                                    placeholder={t(
                                        'nav.authentication.enterCurrentPassword',
                                    )}
                                    autoComplete="current-password"
                                    {...field}
                                />
                            )}
                        />
                    </FormItem>

                    <FormItem
                        label={t('nav.authentication.newPassword')}
                        invalid={Boolean(errors.newPassword)}
                        errorMessage={errors.newPassword?.message}
                        className="mb-4"
                    >
                        <Controller
                            name="newPassword"
                            control={control}
                            render={({ field }) => (
                                <PasswordInput
                                    placeholder={t(
                                        'nav.authentication.enterNewPassword',
                                    )}
                                    autoComplete="new-password"
                                    {...field}
                                />
                            )}
                        />
                    </FormItem>

                    <FormItem
                        label={t('nav.authentication.confirmPassword')}
                        invalid={Boolean(errors.confirmPassword)}
                        errorMessage={errors.confirmPassword?.message}
                        className="mb-6"
                    >
                        <Controller
                            name="confirmPassword"
                            control={control}
                            render={({ field }) => (
                                <PasswordInput
                                    placeholder={t(
                                        'nav.authentication.confirmNewPassword',
                                    )}
                                    autoComplete="new-password"
                                    {...field}
                                />
                            )}
                        />
                    </FormItem>

                    <div className="flex justify-end">
                        <Button
                            type="submit"
                            variant="solid"
                            loading={isSubmitting || isChangingPassword}
                            disabled={submitSuccess}
                            className="min-w-[120px]"
                        >
                            {isSubmitting || isChangingPassword
                                ? t('nav.authentication.changingPassword')
                                : submitSuccess
                                  ? 'Success!'
                                  : t('nav.authentication.changePassword')}
                        </Button>
                    </div>
                </Form>

                {/* <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                    <p className="text-xs text-blue-800">
                        <strong>
                            {t('nav.authentication.passwordRequirements')}
                        </strong>
                        <br />• {t('nav.authentication.passwordMinLength')}
                        <br />• {t('nav.authentication.passwordUpperLower')}
                        <br />• {t('nav.authentication.passwordNumber')}
                    </p>
                </div> */}
            </div>
        </Dialog>
    )
}

export default ChangePasswordDialog
