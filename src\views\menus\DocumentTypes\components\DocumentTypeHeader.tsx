import useTranslation from '@/utils/hooks/useTranslation'
import { Button } from '@/components/ui'
import { TbPlus } from 'react-icons/tb'

interface DocumentTypeHeaderProps {
    onAddDocumentType: () => void
    onColumnVisibilityChange?: (columns: string[]) => void
}

const DocumentTypeHeader = ({ onAddDocumentType }: DocumentTypeHeaderProps) => {
    const { t } = useTranslation()

    return (
        <Button
            variant="solid"
            size="sm"
            icon={<TbPlus className="text-xl" />}
            className="shadow-sm hover:shadow-md transition-all duration-200 bg-primary-mild hover:bg-primary-deep"
            onClick={onAddDocumentType}
        >
            {t('nav.documentTypes.addDocumentType')}
        </Button>
    )
}

export default DocumentTypeHeader
