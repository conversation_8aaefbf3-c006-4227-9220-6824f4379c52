/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect } from 'react'
import { Form, FormContainer, FormItem } from '@/components/ui/Form'
import Input from '@/components/ui/Input'
import Card from '@/components/ui/Card'
import Select from '@/components/ui/Select'
import Container from '@/components/shared/Container'
import BottomStickyBar from '@/components/template/BottomStickyBar'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { CreateUser } from '@/@types/auth'
import { HiUser, HiMail } from 'react-icons/hi'
import isEmpty from 'lodash/isEmpty'
import type { CommonProps } from '@/@types/common'
import useTranslation from '@/utils/hooks/useTranslation'
import { useGetOrgsStructure } from '@/hooks/orgs'

type UserFormProps = {
    onFormSubmit: (values: CreateUser) => void
    defaultValues?: Partial<CreateUser>
    newUser?: boolean
    loading?: boolean
} & CommonProps

const phoneRegex = /^[\d\s\-()+ ]*$/

// validation schema will be created inside the component to use translated messages
const validationSchema = () => {
    const { t } = useTranslation()

    return z.object({
        firstName: z
            .string()
            .min(1, t('nav.systemSecurity.userForm.errors.firstNameRequired')),
        lastName: z
            .string()
            .min(1, t('nav.systemSecurity.userForm.errors.lastNameRequired')),
        email: z
            .string()
            .email(t('nav.systemSecurity.userForm.errors.invalidEmail')),
        phoneNumber: z
            .string()
            .min(1, t('*'))
            .refine((val) => phoneRegex.test(val), {
                message: t('nav.systemSecurity.userForm.errors.invalidPhone'),
            }),
        organizationalNodeCodes: z
            .array(z.string())
            .min(
                1,
                t(
                    'nav.systemSecurity.userForm.errors.selectAtLeastOneOrganization',
                ),
            ),
    })
}

type FormData = z.infer<ReturnType<typeof validationSchema>>

const UserForm = (props: UserFormProps) => {
    const {
        onFormSubmit,
        defaultValues = {},
        children,
        loading = false,
    } = props
    const { t } = useTranslation()

    const { flattenedNodes } = useGetOrgsStructure()

    // Transform nestedNodes to options
    const selectOptions = flattenedNodes?.map((node) => ({
        value: node.code,
        label: node.name,
    }))

    const {
        handleSubmit,
        reset,
        register,
        setValue,
        watch,
        formState: { errors },
    } = useForm<FormData>({
        resolver: zodResolver(validationSchema()),
        defaultValues: {
            firstName: '',
            lastName: '',
            email: '',
            phoneNumber: '',
            organizationalNodeCodes: [],
            ...defaultValues,
        },
    })

    useEffect(() => {
        if (!isEmpty(defaultValues)) {
            reset(defaultValues)
        }
    }, [JSON.stringify(defaultValues), reset])

    const onSubmit = (values: FormData) => {
        onFormSubmit?.(values as CreateUser)
    }

    return (
        <Form
            className="flex w-full h-full"
            containerClassName="flex flex-col w-full justify-between gap-4"
            onSubmit={handleSubmit(onSubmit)}
        >
            <Container>
                <div className="flex flex-col gap-6 mt-4">
                    {loading && (
                        <div className="flex justify-center py-8">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                        </div>
                    )}

                    <FormContainer>
                        {/* Personal & Contact Information Section */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            {/* Personal Information */}
                            <Card>
                                <div className="p-4">
                                    <h5 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                                        <HiUser className="text-blue-600" />
                                        {t(
                                            'nav.systemSecurity.userForm.personalInformation',
                                        )}
                                    </h5>

                                    <div className="space-y-4">
                                        <FormItem
                                            label={t(
                                                'nav.systemSecurity.userForm.firstName',
                                            )}
                                            invalid={!!errors.firstName}
                                            errorMessage={
                                                errors.firstName?.message
                                            }
                                        >
                                            <Input
                                                {...register('firstName')}
                                                placeholder={t(
                                                    'nav.systemSecurity.userForm.enterFirstName',
                                                )}
                                                disabled={loading}
                                            />
                                        </FormItem>

                                        <FormItem
                                            label={t(
                                                'nav.systemSecurity.userForm.lastName',
                                            )}
                                            invalid={!!errors.lastName}
                                            errorMessage={
                                                errors.lastName?.message
                                            }
                                        >
                                            <Input
                                                {...register('lastName')}
                                                placeholder={t(
                                                    'nav.systemSecurity.userForm.enterLastName',
                                                )}
                                                disabled={loading}
                                            />
                                        </FormItem>
                                    </div>
                                </div>
                            </Card>

                            {/* Contact Information */}
                            <Card>
                                <div className="p-4">
                                    <h5 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                                        <HiMail className="text-green-600" />
                                        {t(
                                            'nav.systemSecurity.userForm.contactInformation',
                                        )}
                                    </h5>

                                    <div className="space-y-4">
                                        <FormItem
                                            label={t(
                                                'nav.systemSecurity.userForm.emailAddress',
                                            )}
                                            invalid={!!errors.email}
                                            errorMessage={errors.email?.message}
                                        >
                                            <Input
                                                {...register('email')}
                                                type="email"
                                                placeholder={t(
                                                    'nav.systemSecurity.userForm.enterEmailAddress',
                                                )}
                                                disabled={loading}
                                            />
                                        </FormItem>

                                        <FormItem
                                            label={t(
                                                'nav.systemSecurity.userForm.phoneNumber',
                                            )}
                                            invalid={!!errors.phoneNumber}
                                            errorMessage={
                                                errors.phoneNumber?.message
                                            }
                                        >
                                            <Input
                                                {...register('phoneNumber')}
                                                placeholder={t(
                                                    'nav.systemSecurity.userForm.enterPhoneNumber',
                                                )}
                                                disabled={loading}
                                            />
                                        </FormItem>
                                    </div>
                                </div>
                            </Card>
                        </div>

                        {/* Security & Access Section */}
                        <Card className="mb-6">
                            <div className="p-4">
                                <h5 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                                    <HiUser className="text-purple-600" />
                                    {t(
                                        'nav.systemSecurity.userForm.securityAndAccess',
                                    )}
                                </h5>

                                <div className="grid grid-cols-1  gap-4">
                                    <FormItem
                                        label={t(
                                            'nav.systemSecurity.userForm.organizationalNodes',
                                        )}
                                        invalid={
                                            !!errors.organizationalNodeCodes
                                        }
                                        errorMessage={
                                            errors.organizationalNodeCodes
                                                ?.message
                                        }
                                    >
                                        <Select
                                            isMulti
                                            options={selectOptions}
                                            value={selectOptions?.filter(
                                                (option) =>
                                                    watch(
                                                        'organizationalNodeCodes',
                                                    )?.includes(option.value),
                                            )}
                                            onChange={(selectedOptions) => {
                                                const values = selectedOptions
                                                    ? selectedOptions.map(
                                                          (option) =>
                                                              option.value,
                                                      )
                                                    : []
                                                setValue(
                                                    'organizationalNodeCodes',
                                                    values,
                                                )
                                            }}
                                            placeholder={t(
                                                'nav.systemSecurity.userForm.selectOrganizationalNodes',
                                            )}
                                            isDisabled={loading}
                                            className="react-select-container"
                                            classNamePrefix="react-select"
                                        />
                                    </FormItem>
                                </div>
                            </div>
                        </Card>
                    </FormContainer>
                </div>
            </Container>
            <BottomStickyBar>{children}</BottomStickyBar>
        </Form>
    )
}

export default UserForm
