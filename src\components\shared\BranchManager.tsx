import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import BranchSelectionDialog from './BranchSelectionDialog'
import BranchConfirmationPopup from './BranchConfirmationPopup'
import { USER } from '@/constants/roles.constant'
import type { Branch } from '@/@types/auth'
import { useGetAccount } from '@/hooks/auth'

interface BranchManagerProps {
    showDialog?: boolean
    availableBranches?: Branch[]
    onBranchSelected?: (branch: Branch) => void
    onDialogClose?: () => void
}

const BranchManager = ({
    showDialog: externalShowDialog,
    availableBranches: externalAvailableBranches,
    onBranchSelected,
    onDialogClose,
}: BranchManagerProps = {}) => {
    const navigate = useNavigate()
    const { data: user } = useGetAccount()
    //!
    const role = 'Employee'
    const token = localStorage.getItem('token')
    const brancethatstored = null
    const branches = user?.organizationalNodes

    //!
    // Internal state for when used standalone
    const [internalShowDialog, setInternalShowDialog] = useState(false)
    const [internalAvailableBranches, setInternalAvailableBranches] = useState<
        Branch[]
    >([])
    const [showConfirmation, setShowConfirmation] = useState(false)
    const [confirmationBranch, setConfirmationBranch] = useState<Branch | null>(
        null,
    )

    // Use external props if provided, otherwise use internal state
    const showDialog = externalShowDialog ?? internalShowDialog
    const availableBranches =
        externalAvailableBranches ?? internalAvailableBranches

    // Fetch branches when used standalone
    useEffect(() => {
        const fetchBranches = async () => {
            if (!externalAvailableBranches && role === USER) {
                try {
                    if (token && !brancethatstored) {
                        setInternalAvailableBranches(branches!)

                        if (branches!.length > 0) {
                            setInternalShowDialog(true)
                        }
                    }
                } catch (error) {
                    console.error('Failed to fetch user organizations:', error)
                }
            }
        }

        fetchBranches()
    }, [user, externalAvailableBranches, token, branches])

    const handleBranchSelected = (branch: Branch) => {
        // Store the selected branch for confirmation popup
        setConfirmationBranch(branch)

        // Call the callback if provided
        if (onBranchSelected) {
            onBranchSelected(branch)
        }

        // Show confirmation popup
        setShowConfirmation(true)
    }

    const handleDialogClose = () => {
        if (onDialogClose) {
            onDialogClose()
        } else {
            setInternalShowDialog(false)
        }
    }

    const handleConfirmationComplete = () => {
        setShowConfirmation(false)
        setConfirmationBranch(null)

        // Navigate to digitization daily (BranchManager is only used by USER role)
        navigate('/digitization/daily')
    }

    return (
        <>
            <BranchSelectionDialog
                isOpen={showDialog}
                onClose={handleDialogClose}
                onBranchSelected={handleBranchSelected}
                availableBranches={availableBranches}
            />

            <BranchConfirmationPopup
                show={showConfirmation}
                branch={confirmationBranch}
                onComplete={handleConfirmationComplete}
            />
        </>
    )
}

export default BranchManager
