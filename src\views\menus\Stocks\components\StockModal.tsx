/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState } from 'react'
import Dialog from '@/components/ui/Dialog'
import StockForm from './StockForm'
import useTranslation from '@/utils/hooks/useTranslation'
import type { StockRequest } from '@/@types/stocks'
import { TbPlus, TbPencil } from 'react-icons/tb'
import { Notification, toast } from '@/components/ui'
import FormActions from '@/components/shared/actions/FormActions'
import {
    useCreateStock,
    useGetStockById,
    useUpdateStock,
    useUpdateStockStatus,
} from '@/hooks/stock'

interface StockModalProps {
    isOpen: boolean
    onSuccess: () => void
    onClose: () => void
    mode: 'add' | 'edit'
    stock_id?: number
}

const StockModal = ({
    isOpen,
    onSuccess,
    onClose,
    mode,
    stock_id,
}: StockModalProps) => {
    const { t } = useTranslation()

    const { data: stock } = useGetStockById(stock_id!)

    const createStock = useCreateStock()
    const updateStock = useUpdateStock()
    const updateStockStatus = useUpdateStockStatus()

    const [isSubmitting, setIsSubmitting] = useState(false)

    const title =
        mode === 'add' ? t('nav.Stock.addStock') : t('nav.shared.editStock')

    const handleSubmit = async (formData: StockRequest) => {
        setIsSubmitting(true)
        try {
            if (mode === 'add') {
                await createStock.mutateAsync(formData)
            } else if (stock_id) {
                await updateStock.mutateAsync({ id: stock_id, stock: formData })
            }
            toast.push(
                <Notification title="" type="success" duration={2500}>
                    :{t('stockAdded')}
                </Notification>,
                { placement: 'top-center' },
            )
            onSuccess()
        } catch (error: any) {
            const data = error?.errors[0]

            toast.push(
                <Notification title="" type="danger" duration={2500}>
                    {data?.description || t('nav.systemSecurity.failed')}
                </Notification>,
                { placement: 'top-center' },
            )
        } finally {
            setIsSubmitting(false)
        }
    }

    const handleClose = () => {
        if (!isSubmitting) {
            onClose()
        }
    }

    return (
        <Dialog
            isOpen={isOpen}
            shouldCloseOnEsc={true}
            shouldCloseOnOverlayClick={true}
            width={1000}
            height={550}
            className="h-min"
            onClose={handleClose}
            onRequestClose={handleClose}
        >
            <div className="flex flex-col h-full">
                <div className="flex items-center gap-3 pb-4">
                    <div className="flex items-center justify-center w-10 h-10 bg-primary-subtle rounded-lg">
                        {mode === 'add' ? (
                            <TbPlus className="w-5 h-5 text-primary-deep" />
                        ) : (
                            <TbPencil className="w-5 h-5 text-primary-deep" />
                        )}
                    </div>
                    <div>
                        <h3 className="">{title}</h3>
                        <p className="text-sm text-gray-500">
                            {mode === 'add'
                                ? t('nav.Stock.addStockDescription')
                                : t('nav.Stock.editStockDescription')}
                        </p>
                    </div>
                </div>

                <div className="flex-1 overflow-y-auto min-h-0">
                    <StockForm
                        mode={mode}
                        stock_id={stock_id}
                        onSubmit={handleSubmit}
                    >
                        <FormActions
                            isEdit={mode === 'edit'}
                            data={stock}
                            loading={isSubmitting}
                            showStatusButton={true}
                            statusButtonColor={
                                stock?.status === 1 ? 'red' : 'emerald'
                            }
                            onStatusChange={async () => {
                                if (stock) {
                                    try {
                                        await updateStockStatus.mutateAsync({
                                            id: stock?.id,
                                            newStatus:
                                                stock?.status === 1 ? 2 : 1,
                                        })
                                        toast.push(
                                            <Notification
                                                title=""
                                                type="success"
                                                duration={2500}
                                            >
                                                updateStockStatus
                                            </Notification>,
                                            {
                                                placement: 'top-center',
                                            },
                                        )
                                        onClose()
                                    } catch (error: any) {
                                        toast.push(
                                            <Notification
                                                title=""
                                                type="danger"
                                                duration={2500}
                                            >
                                                {error?.errors?.[0]
                                                    ?.description ||
                                                    t('nav.shared.failed')}
                                            </Notification>,
                                            {
                                                placement: 'top-center',
                                            },
                                        )
                                    }
                                }
                            }}
                        />
                    </StockForm>
                </div>
            </div>
        </Dialog>
    )
}

export default StockModal
