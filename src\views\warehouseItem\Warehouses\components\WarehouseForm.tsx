import { use<PERSON><PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import Select, { Option as DefaultOption } from '@/components/ui/Select'
import { FormItem, FormContainer, Form } from '@/components/ui/Form'
import useTranslation from '@/utils/hooks/useTranslation'
import type { OptionProps } from 'react-select'
import {
    TbBuilding,
    TbMapPin,
    TbUser,
    TbPhone,
    TbTrash,
    TbFileText,
    TbPlus,
} from 'react-icons/tb'
import type {
    WarehouseForm as WarehouseFormType,
    WarehouseDetails,
    WarehouseMobile,
} from '@/@types/warehouse'
import LocationSelector from '@/components/shared/LocationSelector'
import { useState } from 'react'
import { useGetOrgsStructure } from '@/hooks/orgs'

// Custom option type for organizational nodes
type OrgNodeOption = {
    value: string
    label: string
    code?: string
    name?: string
}

// Custom Option component to display name with code in low opacity
const CustomOrgOption = (props: OptionProps<OrgNodeOption>) => {
    const { data } = props

    // Handle "No Selection" option
    if (!data.code || !data.name) {
        return (
            <DefaultOption<OrgNodeOption>
                {...props}
                customLabel={() => <span className="ml-2">{data.label}</span>}
            />
        )
    }

    return (
        <DefaultOption<OrgNodeOption>
            {...props}
            customLabel={() => (
                <span className="ml-2">
                    {data.name}{' '}
                    <span className="opacity-50">({data.code})</span>
                </span>
            )}
        />
    )
}

interface WarehouseFormProps {
    mode: 'create' | 'edit'
    initialData?: WarehouseDetails | null
    onSubmit: (data: WarehouseFormType) => void
    onCancel: () => void
    loading?: boolean
}

const warehouseSchema = () => {
    const { t } = useTranslation()
    const phoneRegex = /^[\d\s\-()+ ]*$/

    return z.object({
        name: z.string().min(1, { message: t('*') }),
        organizationalNodeCode: z.string().min(1, { message: t('*') }),
        responsiblePersonName: z.string().min(1, { message: t('*') }),
        districtOrVillageCode: z.string().min(1, { message: t('*') }),
        detailedAddress: z.string().min(1, { message: t('*') }),
        maintenancePhoneNumber: z
            .string()
            .optional()
            .refine((val) => !val || phoneRegex.test(val), {
                message: t('nav.Form.phoneNumbersOnly'),
            }),
        securityPhoneNumber: z
            .string()
            .optional()
            .refine((val) => !val || phoneRegex.test(val), {
                message: t('nav.Form.phoneNumbersOnly'),
            }),
        notes: z.string().optional(),
        warehouseMobiles: z
            .array(
                z.object({
                    mobileNumber: z
                        .string()
                        .min(1, { message: t('nav.Form.phoneRequired') })
                        .refine((val) => phoneRegex.test(val), {
                            message: t('nav.Form.phoneNumbersOnly'),
                        }),
                    note: z.string().optional(),
                }),
            )
            .optional(),
    })
}

const WarehouseForm = ({
    mode,
    onSubmit,
    initialData,
    onCancel,
    loading = false,
}: WarehouseFormProps) => {
    const { t } = useTranslation()

    const { flattenedNodes } = useGetOrgsStructure()

    const {
        handleSubmit,
        control,
        formState: { errors },
    } = useForm<WarehouseFormType>({
        defaultValues: {
            name: initialData?.name || '',
            organizationalNodeCode: initialData?.organizationalNodeCode || '',
            responsiblePersonName: initialData?.responsiblePersonName || '',
            districtOrVillageCode: initialData?.districtOrVillageCode || '',
            detailedAddress: initialData?.detailedAddress || '',
            maintenancePhoneNumber: initialData?.maintenancePhoneNumber || '',
            securityPhoneNumber: initialData?.securityPhoneNumber || '',
            notes: initialData?.notes || '',
            warehouseMobiles: initialData?.warehouseMobiles || [],
        },
        resolver: zodResolver(warehouseSchema()),
    })

    // State for managing phone inputs
    const [phoneInput, setPhoneInput] = useState<WarehouseMobile>({
        mobileNumber: '',
        note: '',
    })

    const handlePhoneInput = (value: string) => {
        return value.replace(/[^\d\s\-()+ ]/g, '')
    }

    return (
        <FormContainer>
            <Form onSubmit={handleSubmit(onSubmit)}>
                {/* Basic Information Section */}
                <div className="mb-8">
                    <div className="flex items-center gap-2 mb-4">
                        <TbBuilding className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                            {t('nav.warehouses.basicInformation')}
                        </h4>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <FormItem
                            label={t('nav.warehouses.warehouseName')}
                            invalid={!!errors.name}
                            errorMessage={errors.name?.message}
                        >
                            <Controller
                                name="name"
                                control={control}
                                render={({ field }) => (
                                    <Input
                                        {...field}
                                        placeholder={t(
                                            'nav.warehouses.enterWarehouseName',
                                        )}
                                    />
                                )}
                            />
                        </FormItem>

                        <FormItem
                            label={t('nav.warehouses.organizationalNodeCode')}
                            invalid={!!errors.organizationalNodeCode}
                            errorMessage={
                                errors.organizationalNodeCode?.message
                            }
                        >
                            <Controller
                                name="organizationalNodeCode"
                                control={control}
                                render={({ field }) => {
                                    const options: OrgNodeOption[] = [
                                        {
                                            value: '',
                                            label: t('common.select'),
                                        },
                                        ...(flattenedNodes?.map((node) => ({
                                            value: node.code,
                                            label: node.name,
                                            code: node.code,
                                            name: node.name,
                                        })) || []),
                                    ]

                                    return (
                                        <Select<OrgNodeOption>
                                            isClearable
                                            {...field}
                                            placeholder={t(
                                                'nav.warehouses.enterOrgCode',
                                            )}
                                            options={options}
                                            isLoading={!flattenedNodes}
                                            components={{
                                                Option: CustomOrgOption,
                                            }}
                                            value={
                                                options.find(
                                                    (option) =>
                                                        option.value ===
                                                        field.value,
                                                ) || null
                                            }
                                            onChange={(option) =>
                                                field.onChange(
                                                    option?.value || '',
                                                )
                                            }
                                        />
                                    )
                                }}
                            />
                        </FormItem>

                        <FormItem
                            label={t('nav.warehouses.responsiblePerson')}
                            invalid={!!errors.responsiblePersonName}
                            errorMessage={errors.responsiblePersonName?.message}
                        >
                            <Controller
                                name="responsiblePersonName"
                                control={control}
                                render={({ field }) => (
                                    <Input
                                        {...field}
                                        placeholder={t(
                                            'nav.warehouses.enterResponsiblePerson',
                                        )}
                                    />
                                )}
                            />
                        </FormItem>
                    </div>
                </div>

                {/* Location Information Section */}
                <div className="mb-8">
                    <div className="flex items-center gap-2 mb-4">
                        <TbMapPin className="w-5 h-5 text-green-600 dark:text-green-400" />
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                            {t('nav.warehouses.locationInformation')}
                        </h4>
                    </div>

                    <LocationSelector
                        control={control}
                        errors={errors}
                        className="mb-2"
                        districtField="districtOrVillageCode"
                    />
                    <div className="grid grid-cols-1 gap-6">
                        <FormItem
                            label={t('nav.warehouses.detailedAddress')}
                            invalid={!!errors.detailedAddress}
                            errorMessage={errors.detailedAddress?.message}
                        >
                            <Controller
                                name="detailedAddress"
                                control={control}
                                render={({ field }) => (
                                    <Input
                                        {...field}
                                        placeholder={t(
                                            'nav.warehouses.enterDetailedAddress',
                                        )}
                                        rows={3}
                                    />
                                )}
                            />
                        </FormItem>
                    </div>
                </div>

                {/* Contact Information Section */}
                <div className="mb-8 flex flex-col">
                    <div className="flex items-center gap-2 mb-4 ">
                        <TbPhone className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                            {t('nav.warehouses.contactInformation')}
                        </h4>
                    </div>

                    <div className="grid grid-cols-1  gap-6">
                        <FormItem
                            className=""
                            invalid={Boolean(errors.warehouseMobiles)}
                            errorMessage={errors.warehouseMobiles?.message}
                        >
                            <Controller
                                name="warehouseMobiles"
                                control={control}
                                render={({ field }) => (
                                    <div className="space-y-4 ">
                                        {/* Display existing phones */}
                                        {field.value &&
                                            field.value.length > 0 && (
                                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                    {field.value.map(
                                                        (
                                                            phone: WarehouseMobile,
                                                            index: number,
                                                        ) => (
                                                            <div
                                                                key={index}
                                                                className="flex justify-between items-center gap-2 p-3 border rounded-lg "
                                                            >
                                                                <div className=" flex items-center gap-4">
                                                                    <div className=" ">
                                                                        {
                                                                            phone.mobileNumber
                                                                        }
                                                                    </div>
                                                                    {phone.note && (
                                                                        <div className=" text-gray-500 ">
                                                                            {
                                                                                phone.note
                                                                            }
                                                                        </div>
                                                                    )}
                                                                </div>
                                                                <Button
                                                                    type="button"
                                                                    size="sm"
                                                                    variant="plain"
                                                                    icon={
                                                                        <TbTrash />
                                                                    }
                                                                    onClick={() => {
                                                                        const newPhones =
                                                                            [
                                                                                ...(field.value ||
                                                                                    []),
                                                                            ]
                                                                        newPhones.splice(
                                                                            index,
                                                                            1,
                                                                        )
                                                                        field.onChange(
                                                                            newPhones,
                                                                        )
                                                                    }}
                                                                >
                                                                    {t(
                                                                        'nav.shared.remove',
                                                                    )}
                                                                </Button>
                                                            </div>
                                                        ),
                                                    )}
                                                </div>
                                            )}

                                        {/* Add new phone form */}
                                        <div className="flex items-end justify-between gap-4 mb-3">
                                            <div className="flex-auto">
                                                <label className="flex items-center gap-2 text-sm font-medium mb-1">
                                                    <TbPhone className="w-4 h-4 text-gray-500" />
                                                    {t(
                                                        'nav.shared.phoneNumber',
                                                    )}
                                                </label>
                                                <div className="relative">
                                                    <TbPhone className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                                                    <Input
                                                        type="tel"
                                                        placeholder={t(
                                                            'nav.Stock.enterStockPhone',
                                                        )}
                                                        value={
                                                            phoneInput.mobileNumber
                                                        }
                                                        className="pl-10"
                                                        onChange={(e) => {
                                                            const filteredValue =
                                                                handlePhoneInput(
                                                                    e.target
                                                                        .value,
                                                                )
                                                            setPhoneInput(
                                                                (prev) => ({
                                                                    ...prev,
                                                                    mobileNumber:
                                                                        filteredValue,
                                                                }),
                                                            )
                                                        }}
                                                    />
                                                </div>
                                            </div>
                                            <div className="flex-auto">
                                                <label className="flex items-center gap-2 text-sm font-medium mb-1">
                                                    <TbFileText className="w-4 h-4 text-gray-500" />
                                                    {t('nav.shared.notes')}
                                                </label>
                                                <div className="relative">
                                                    <TbFileText className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                                                    <Input
                                                        type="text"
                                                        placeholder={t(
                                                            'nav.Stock.enterStockNotes',
                                                        )}
                                                        value={phoneInput.note}
                                                        className="pl-10"
                                                        onChange={(e) =>
                                                            setPhoneInput(
                                                                (prev) => ({
                                                                    ...prev,
                                                                    note: e
                                                                        .target
                                                                        .value,
                                                                }),
                                                            )
                                                        }
                                                    />
                                                </div>
                                            </div>
                                            <Button
                                                className="p-6 flex"
                                                type="button"
                                                size="sm"
                                                variant="solid"
                                                icon={<TbPlus />}
                                                disabled={
                                                    !phoneInput.mobileNumber.trim()
                                                }
                                                onClick={() => {
                                                    if (
                                                        phoneInput.mobileNumber.trim()
                                                    ) {
                                                        const newPhones = [
                                                            ...(field.value ||
                                                                []),
                                                            {
                                                                mobileNumber:
                                                                    phoneInput.mobileNumber.trim(),
                                                                note: phoneInput.note.trim(),
                                                            },
                                                        ]
                                                        field.onChange(
                                                            newPhones,
                                                        )
                                                        setPhoneInput({
                                                            mobileNumber: '',
                                                            note: '',
                                                        })
                                                    }
                                                }}
                                            >
                                                {t('nav.shared.addPhone')}
                                            </Button>
                                        </div>
                                    </div>
                                )}
                            />
                        </FormItem>

                        <div className="flex justify-between items-center gap-4">
                            <FormItem
                                className="w-full"
                                label={t('nav.warehouses.maintenancePhone')}
                                invalid={!!errors.maintenancePhoneNumber}
                                errorMessage={
                                    errors.maintenancePhoneNumber?.message
                                }
                            >
                                <Controller
                                    name="maintenancePhoneNumber"
                                    control={control}
                                    render={({ field }) => (
                                        <Input
                                            {...field}
                                            placeholder={t(
                                                'nav.warehouses.enterMaintenancePhone',
                                            )}
                                            type="tel"
                                        />
                                    )}
                                />
                            </FormItem>

                            <FormItem
                                className="w-full"
                                label={t('nav.warehouses.securityPhone')}
                                invalid={!!errors.securityPhoneNumber}
                                errorMessage={
                                    errors.securityPhoneNumber?.message
                                }
                            >
                                <Controller
                                    name="securityPhoneNumber"
                                    control={control}
                                    render={({ field }) => (
                                        <Input
                                            {...field}
                                            placeholder={t(
                                                'nav.warehouses.enterSecurityPhone',
                                            )}
                                            type="tel"
                                        />
                                    )}
                                />
                            </FormItem>
                        </div>
                    </div>
                </div>

                {/* Additional Information Section */}
                <div className="mb-8">
                    <div className="flex items-center gap-2 mb-4">
                        <TbUser className="w-5 h-5 text-orange-600 dark:text-orange-400" />
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                            {t('nav.warehouses.additionalInformation')}
                        </h4>
                    </div>

                    <div className="grid grid-cols-1 gap-6">
                        <FormItem
                            label={t('nav.warehouses.notes')}
                            invalid={!!errors.notes}
                            errorMessage={errors.notes?.message}
                        >
                            <Controller
                                name="notes"
                                control={control}
                                render={({ field }) => (
                                    <Input
                                        {...field}
                                        placeholder={t(
                                            'nav.warehouses.enterNotes',
                                        )}
                                        rows={4}
                                    />
                                )}
                            />
                        </FormItem>
                    </div>
                </div>

                {/* Action Buttons */}
                <div className="flex justify-end gap-4 pt-6 border-t border-gray-200 dark:border-gray-700">
                    <Button
                        type="button"
                        variant="plain"
                        disabled={loading}
                        className="min-w-[120px]"
                        onClick={onCancel}
                    >
                        {t('nav.shared.cancel')}
                    </Button>
                    <Button
                        type="submit"
                        variant="solid"
                        // icon={<TbSave />}
                        loading={loading}
                        className="min-w-[120px] bg-primary hover:bg-primary-deep"
                    >
                        {mode === 'create'
                            ? t('nav.shared.create')
                            : t('nav.shared.update')}
                    </Button>
                </div>
            </Form>
        </FormContainer>
    )
}

export default WarehouseForm
