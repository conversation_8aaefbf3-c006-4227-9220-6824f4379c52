// import { useState } from 'react'
// import { use<PERSON><PERSON>, Controller } from 'react-hook-form'
// import { zodResolver } from '@hookform/resolvers/zod'
// import { z, type ZodType } from 'zod'
// import Button from '@/components/ui/Button'
// import Drawer from '@/components/ui/Drawer'
// import Select from '@/components/ui/Select'
// import Checkbox from '@/components/ui/Checkbox'
// import { FormItem, FormContainer, Form } from '@/components/ui/Form'
// import useTranslation from '@/utils/hooks/useTranslation'
// import { TbFilter, TbFilterOff } from 'react-icons/tb'
// import type { WarehouseFilterConfig } from '@/store/selectors/warehouseSelector'

// type FilterFormSchema = {
//     nameFilter?: string
//     villageFilter?: string
//     governorateFilter?: string
//     organizationFilter?: string
//     responsiblePersonFilter?: string
//     statusFilter?: string
//     filterColumns?: string[]
// }

// const validationSchema: ZodType<FilterFormSchema> = z.object({
//     nameFilter: z.string().optional().default(''),
//     villageFilter: z.string().optional().default(''),
//     governorateFilter: z.string().optional().default(''),
//     organizationFilter: z.string().optional().default(''),
//     responsiblePersonFilter: z.string().optional().default(''),
//     statusFilter: z.string().optional().default(''),
//     filterColumns: z.array(z.string()).optional().default([]),
// })

// const WarehouseTableFilter = () => {
//     const { t } = useTranslation()
//     const [filterIsOpen, setFilterIsOpen] = useState(false)

//     // Create status options for the select
//     const statusOptions = [
//         { value: '', label: t('nav.shared.all') },
//         { value: '0', label: t('nav.GlobalActions.frozen') },
//         { value: '1', label: t('nav.shared.active') },
//     ]

//     const {
//         handleSubmit,
//         control,
//         reset,
//         formState: { errors },
//     } = useForm<FilterFormSchema>({
//         defaultValues: {
//             nameFilter: '',
//             villageFilter: '',
//             governorateFilter: '',
//             organizationFilter: '',
//             responsiblePersonFilter: '',
//             statusFilter: '',
//             filterColumns: [
//                 'id',
//                 'name',
//                 'organizationalNodeName',
//                 'villageNameAr',
//                 'governorateNameAr',
//                 'responsiblePersonName',
//                 'numberOfAreas',
//                 'status',
//                 'action',
//             ],
//         },
//         resolver: zodResolver(validationSchema),
//     })

//     const onSubmit = (values: FilterFormSchema) => {
//         console.log('🔥 onSubmit called with values:', values)
//         console.log('🔥 Form errors:', errors)

//         // Build comprehensive filter config
//         const newFilterConfig: Partial<WarehouseFilterConfig> = {}

//         // Apply village filter
//         if (values.villageFilter && values.villageFilter !== '') {
//             newFilterConfig.villages = [values.villageFilter]
//         }

//         // Apply governorate filter
//         if (values.governorateFilter && values.governorateFilter !== '') {
//             newFilterConfig.governorates = [values.governorateFilter]
//         }

//         // Apply organization filter
//         if (values.organizationFilter && values.organizationFilter !== '') {
//             newFilterConfig.organizations = [values.organizationFilter]
//         }

//         // Apply responsible person filter
//         if (values.responsiblePersonFilter && values.responsiblePersonFilter !== '') {
//             newFilterConfig.responsiblePersons = [values.responsiblePersonFilter]
//         }

//         // Apply status filter
//         if (values.statusFilter && values.statusFilter !== '') {
//             newFilterConfig.status = parseInt(values.statusFilter)
//         }

//         // Apply column filter
//         if (values.filterColumns) {
//             setColumnsAction(values.filterColumns)
//         }

//         setFilterIsOpen(false)
//     }

//     const handleClearFilters = () => {
//         // Clear all filters
//         const clearedConfig: Partial<WarehouseFilterConfig> = {
//             names: [],
//             villages: [],
//             governorates: [],
//             organizations: [],
//             responsiblePersons: [],
//             status: undefined,
//         }

//         handleFilterChange(clearedConfig)

//         // Reset form values
//         reset({
//             nameFilter: '',
//             villageFilter: '',
//             governorateFilter: '',
//             organizationFilter: '',
//             responsiblePersonFilter: '',
//             statusFilter: '',
//             filterColumns: defaultColumns.map((col) => col.value),
//         })

//         // Reset columns to default
//         setColumnsAction(defaultColumns.map((col) => col.value))

//         setFilterIsOpen(false)
//     }

//     return (
//         <>
//             <Button
//                 icon={hasActiveFilters ? <TbFilterOff /> : <TbFilter />}
//                 variant={hasActiveFilters ? 'solid' : 'default'}
//                 size="sm"
//                 className={`shadow-sm hover:shadow-md transition-all duration-200 ${
//                     hasActiveFilters
//                         ? 'bg-orange-500 hover:bg-orange-600 text-white'
//                         : 'border-gray-300 hover:border-gray-400'
//                 }`}
//                 onClick={() => setFilterIsOpen(true)}
//             >
//                 {t('nav.shared.filter')}
//                 {hasActiveFilters && (
//                     <span className="ml-1 px-1.5 py-0.5 text-xs bg-white bg-opacity-20 rounded-full">
//                         {Object.values(filterConfig).filter(Boolean).length}
//                     </span>
//                 )}
//             </Button>
//             <Drawer
//                 title={t('nav.shared.filter')}
//                 isOpen={filterIsOpen}
//                 footer={
//                     <div className="flex justify-around items-center w-full ">
//                         <Button
//                             className=""
//                             variant="plain"
//                             onClick={handleClearFilters}
//                         >
//                             {t('nav.shared.clear')}
//                         </Button>
//                         <Button
//                             variant="solid"
//                             onClick={handleSubmit(onSubmit)}
//                         >
//                             {t('nav.shared.confirm')}
//                         </Button>
//                     </div>
//                 }
//                 onClose={() => setFilterIsOpen(false)}
//                 onRequestClose={() => setFilterIsOpen(false)}
//             >
//                 <FormContainer>
//                     <Form onSubmit={handleSubmit(onSubmit)}>
//                         <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//                             {/* Village Filter */}
//                             <FormItem label={t('nav.warehouses.filterByVillage')}>
//                                 <Controller
//                                     name="villageFilter"
//                                     control={control}
//                                     render={({ field }) => (
//                                         <Select
//                                             options={villageOptions}
//                                             placeholder={t(
//                                                 'nav.warehouses.selectVillage',
//                                             )}
//                                             value={villageOptions.find(
//                                                 (option) =>
//                                                     option.value ===
//                                                     field.value,
//                                             )}
//                                             onChange={(option) =>
//                                                 field.onChange(
//                                                     option?.value || '',
//                                                 )
//                                             }
//                                         />
//                                     )}
//                                 />
//                             </FormItem>

//                             {/* Governorate Filter */}
//                             <FormItem
//                                 label={t('nav.warehouses.filterByGovernorate')}
//                             >
//                                 <Controller
//                                     name="governorateFilter"
//                                     control={control}
//                                     render={({ field }) => (
//                                         <Select
//                                             options={governorateOptions}
//                                             placeholder={t(
//                                                 'nav.warehouses.selectGovernorate',
//                                             )}
//                                             value={governorateOptions.find(
//                                                 (option) =>
//                                                     option.value ===
//                                                     field.value,
//                                             )}
//                                             onChange={(option) =>
//                                                 field.onChange(
//                                                     option?.value || '',
//                                                 )
//                                             }
//                                         />
//                                     )}
//                                 />
//                             </FormItem>

//                             {/* Organization Filter */}
//                             <FormItem label={t('nav.warehouses.filterByOrganization')}>
//                                 <Controller
//                                     name="organizationFilter"
//                                     control={control}
//                                     render={({ field }) => (
//                                         <Select
//                                             options={organizationOptions}
//                                             placeholder={t(
//                                                 'nav.warehouses.selectOrganization',
//                                             )}
//                                             value={organizationOptions.find(
//                                                 (option) =>
//                                                     option.value ===
//                                                     field.value,
//                                             )}
//                                             onChange={(option) =>
//                                                 field.onChange(
//                                                     option?.value || '',
//                                                 )
//                                             }
//                                         />
//                                     )}
//                                 />
//                             </FormItem>

//                             {/* Responsible Person Filter */}
//                             <FormItem label={t('nav.warehouses.filterByResponsible')}>
//                                 <Controller
//                                     name="responsiblePersonFilter"
//                                     control={control}
//                                     render={({ field }) => (
//                                         <Select
//                                             options={responsiblePersonOptions}
//                                             placeholder={t(
//                                                 'nav.warehouses.selectResponsible',
//                                             )}
//                                             value={responsiblePersonOptions.find(
//                                                 (option) =>
//                                                     option.value ===
//                                                     field.value,
//                                             )}
//                                             onChange={(option) =>
//                                                 field.onChange(
//                                                     option?.value || '',
//                                                 )
//                                             }
//                                         />
//                                     )}
//                                 />
//                             </FormItem>

//                             {/* Status Filter */}
//                             <FormItem label={t('nav.shared.status')}>
//                                 <Controller
//                                     name="statusFilter"
//                                     control={control}
//                                     render={({ field }) => (
//                                         <Select
//                                             options={statusOptions}
//                                             placeholder={t(
//                                                 'nav.shared.selectStatus',
//                                             )}
//                                             value={statusOptions.find(
//                                                 (option) =>
//                                                     option.value ===
//                                                     field.value,
//                                             )}
//                                             onChange={(option) =>
//                                                 field.onChange(
//                                                     option?.value || '',
//                                                 )
//                                             }
//                                         />
//                                     )}
//                                 />
//                             </FormItem>
//                         </div>

//                         {/* Column Visibility Section */}
//                         <FormItem
//                             label={t('nav.shared.customizeColumns')}
//                             className="mt-6"
//                         >
//                             <div className="mt-4 space-y-2">
//                                 <Controller
//                                     name="filterColumns"
//                                     control={control}
//                                     render={({ field }) => (
//                                         <Checkbox.Group
//                                             className="grid grid-cols-2 gap-6"
//                                             value={field.value}
//                                             onChange={field.onChange}
//                                         >
//                                             {defaultColumns.map((column) => (
//                                                 <Checkbox
//                                                     key={column.value}
//                                                     name={field.name}
//                                                     value={column.value}
//                                                     className="justify-between flex-row-reverse heading-text w-full"
//                                                 >
//                                                     {column.label}
//                                                 </Checkbox>
//                                             ))}
//                                         </Checkbox.Group>
//                                     )}
//                                 />
//                             </div>
//                         </FormItem>
//                     </Form>
//                 </FormContainer>
//             </Drawer>
//         </>
//     )
// }

// export default WarehouseTableFilter
