import ApiService from '@/services/ApiService'
import { File, FileDetails, FileForm } from '@/@types/file'
import { Status } from '@/@types/common'
import { PaginationResponse } from '@/@types/global'

const BASE_ROUTE = '/Files'

type GetFilesResponse = {
    items: File[]
    totalCount: number
} & PaginationResponse

export async function getFilesByStation(
    digitizationStationId: number,
    {
        pageNumber = 1,
        pageSize = 10,
        fileStatus = undefined,
    }: {
        pageNumber?: number
        pageSize?: number
        fileStatus?: Status | undefined
    },
) {
    return await ApiService.get<GetFilesResponse>(
        `${BASE_ROUTE}/station/${digitizationStationId}`,
        {
            params: { pageNumber, pageSize, fileStatus },
        },
    )
}

// POST /api/Files - Creates a new file
export async function createFile(file: FileForm) {
    return ApiService.post(BASE_ROUTE, file)
}

// GET /api/Files/{fileId} - Gets a file by its ID
export async function getFileById(fileId: string) {
    return ApiService.get<FileDetails>(`${BASE_ROUTE}/${fileId}`)
}

// DELETE /api/Files/{fileId} - Deletes a file (soft delete)
export async function deleteFile(fileId: string) {
    return ApiService.delete(`${BASE_ROUTE}/${fileId}`)
}

// PATCH /api/Files/{fileId}/status - Updates the status of a file
export async function closeFile(fileId: string) {
    return ApiService.patch(`${BASE_ROUTE}/${fileId}/close`)
}
