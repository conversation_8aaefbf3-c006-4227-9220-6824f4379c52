import { useState } from 'react'
import Button from '@/components/ui/Button'
import useTranslation from '@/utils/hooks/useTranslation'
import DailyModal from './DailyModal'
import { TbPlus } from 'react-icons/tb'

const DailyListActionTools = () => {
    const { t } = useTranslation()
    const [isAddModalOpen, setIsAddModalOpen] = useState(false)

    return (
        <>
            <div className="flex items-center gap-2">
                <Button
                    variant="solid"
                    size="sm"
                    icon={<TbPlus />}
                    className="flex items-center gap-2"
                    onClick={() => {
                        setIsAddModalOpen(true)
                    }}
                >
                    {t('nav.File.addFile')}
                </Button>
            </div>

            {/* Add Modal */}
            <DailyModal
                isOpen={isAddModalOpen}
                mode="add"
                onSuccess={() => {
                    setIsAddModalOpen(false)
                }}
                onClose={() => {
                    setIsAddModalOpen(false)
                }}
            />
        </>
    )
}

export default DailyListActionTools
