import ApiService from './ApiService'
import type {
    CreatedNode,
    NodeDetails,
    OneNode,
    OrganizationalStructure,
    OrgLevel,
    UpdatedNode,
} from '@/@types/organizationalStructure'

export async function getOrganizeStructure() {
    return ApiService.get<OrganizationalStructure>(
        '/OrganizationalStructure/tree',
    )
}

export async function getOrganizeStructureByCode(code: string) {
    return ApiService.get<NodeDetails>(`/OrganizationalStructure/${code}`)
}

export async function updateOrganizeStructure(code: string, data: UpdatedNode) {
    return ApiService.put(`/OrganizationalStructure/${code}`, data)
}

export async function deleteOrganizeStructure(code: string) {
    return ApiService.delete(`/OrganizationalStructure/${code}`)
}

export async function createOrganizeStructure(data: CreatedNode) {
    return ApiService.post<OneNode>(`/OrganizationalStructure`, data)
}

export async function getOrganizationsByLevels(level: OrgLevel) {
    return ApiService.get<OneNode>(`/OrganizationalStructure/levels/${level}`)
}

export async function orgPathToRoot(code: string) {
    return ApiService.get<string[]>(
        `OrganizationalStructure/${code}/path-to-root`,
    )
}
