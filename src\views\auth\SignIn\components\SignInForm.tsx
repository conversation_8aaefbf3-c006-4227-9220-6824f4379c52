/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState } from 'react'
import Input from '@/components/ui/Input'
import Button from '@/components/ui/Button'
import { FormItem, Form } from '@/components/ui/Form'
import PasswordInput from '@/components/shared/PasswordInput'
import classNames from '@/utils/classNames'
import { useForm, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import toast from '@/components/ui/toast'
import type { ZodType } from 'zod'
import type { CommonProps } from '@/@types/common'
import type { ReactNode } from 'react'
import useTranslation from '@/utils/hooks/useTranslation'
import { Notification } from '@/components/ui'
import { useSignIn } from '@/hooks/auth'

interface SignInFormProps extends CommonProps {
    disableSubmit?: boolean
    passwordHint?: string | ReactNode
    setMessage?: (message: string) => void
}

type SignInFormSchema = {
    email: string
    password: string
}

const SignInForm = (props: SignInFormProps) => {
    const { t } = useTranslation()
    const [isSubmitting, setSubmitting] = useState<boolean>(false)

    const { disableSubmit = false, className } = props

    const validationSchema: ZodType<SignInFormSchema> = z.object({
        email: z
            .string({ required_error: t('nav.authentication.pleaseEmail') })
            .min(1, { message: t('nav.authentication.pleaseEmail') }),
        password: z
            .string({ required_error: t('nav.authentication.pleasePassword') })
            .min(1, { message: t('nav.authentication.pleasePassword') }),
    })

    const {
        handleSubmit,
        formState: { errors },
        control,
    } = useForm<SignInFormSchema>({
        resolver: zodResolver(validationSchema),
    })

    const { mutate: signIn } = useSignIn()

    const onSignIn = async (values: SignInFormSchema) => {
        const { email, password } = values

        if (!disableSubmit) {
            setSubmitting(true)

            try {
                signIn(
                    { email, password },
                    {
                        onSuccess: () => {
                            toast.push(
                                <Notification
                                    title=""
                                    type="success"
                                    duration={2500}
                                >
                                    {t('nav.authentication.success')}
                                </Notification>,
                                { placement: 'top-center' },
                            )
                        },
                    },
                )
            } catch (error: any) {
                const data = error?.errors[0]

                toast.push(
                    <Notification title="" type="danger" duration={2500}>
                        {data?.code === 'User.Frozen'
                            ? t('nav.authentication.errors.userFrozen')
                            : data?.code === 'User.InvalidCredentials'
                              ? t(
                                    'nav.authentication.errors.invalidCredentials',
                                )
                              : data?.description}
                    </Notification>,
                    { placement: 'top-center' },
                )
            }
        }

        setSubmitting(false)
    }

    return (
        <div className={className}>
            <Form onSubmit={handleSubmit(onSignIn)}>
                <FormItem
                    label={t('nav.authentication.email')}
                    invalid={Boolean(errors.email)}
                    errorMessage={errors.email?.message}
                >
                    <Controller
                        name="email"
                        control={control}
                        render={({ field }) => (
                            <Input
                                type="email"
                                placeholder={t('nav.authentication.email')}
                                autoComplete="off"
                                {...field}
                            />
                        )}
                    />
                </FormItem>
                <FormItem
                    label={t('nav.authentication.password')}
                    invalid={Boolean(errors.password)}
                    errorMessage={errors.password?.message}
                    className={classNames(
                        errors.password?.message ? 'mb-8' : '',
                    )}
                >
                    <Controller
                        name="password"
                        control={control}
                        rules={{ required: true }}
                        render={({ field }) => (
                            <PasswordInput
                                type="text"
                                placeholder={t('nav.authentication.password')}
                                autoComplete="off"
                                {...field}
                            />
                        )}
                    />
                </FormItem>
                <Button
                    block
                    loading={isSubmitting}
                    variant="solid"
                    type="submit"
                >
                    {isSubmitting
                        ? t('nav.authentication.signingIn')
                        : t('nav.authentication.signIn')}
                </Button>
            </Form>
        </div>
    )
}

export default SignInForm
