import Container from '@/components/shared/Container'
import AdaptiveCard from '@/components/shared/AdaptiveCard'
import BuildingListActionTools from './components/BuildingListActionTools'
import BuildingListTableTools from './components/BuildingListTableTools'
import BuildingListTable from './components/BuildingListTable'
// import BuildingSelectionFooter from './components/BuildingSelectionFooter'
import useTranslation from '@/utils/hooks/useTranslation'
import { TbBuilding } from 'react-icons/tb'
import CountBadges from '../../../components/shared/displaying/CountBadges'
import { useGetBuildings } from '@/hooks/building'

const BuildingList = () => {
    const { t } = useTranslation()

    const { data: buildings, isLoading, isError } = useGetBuildings()

    return (
        <>
            <Container>
                <AdaptiveCard className="shadow-lg">
                    <div className="flex flex-col gap-6">
                        {/* Enhanced Header Section */}
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 pb-4 border-b border-gray-200 dark:border-gray-700">
                            <div className="flex justify-between items-center gap-3 w-full">
                                <div className="flex items-center gap-3">
                                    <div className="flex items-center justify-center w-10 h-10 bg-primary-subtle rounded-lg">
                                        <TbBuilding className="w-5 h-5 text-primary-deep" />
                                    </div>
                                    <h3 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                                        {t('nav.buildings.buildings')}
                                    </h3>
                                </div>
                                <CountBadges
                                    counts={{
                                        total: buildings?.length,
                                    }}
                                    loading={isLoading}
                                    error={isError}
                                />
                            </div>
                            <BuildingListActionTools />
                        </div>

                        {/* Table Tools Section */}
                        <div className="">
                            <BuildingListTableTools />
                        </div>

                        {/* Table Section */}
                        <div className="">
                            <BuildingListTable />
                        </div>
                    </div>
                </AdaptiveCard>
            </Container>

            {/* Selection Footer */}
            {/* <BuildingSelectionFooter
                selectedCount={selectedBuildings?.length || 0}
                onDeleteSelected={deleteSelectedBuildings}
                onClearSelection={clearSelection}
            /> */}
        </>
    )
}

export default BuildingList
