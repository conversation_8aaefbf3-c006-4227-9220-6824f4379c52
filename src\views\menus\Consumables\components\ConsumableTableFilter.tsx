import { useState } from 'react'
import Button from '@/components/ui/Button'
import Drawer from '@/components/ui/Drawer'
import Select from '@/components/ui/Select'
import { Form, FormItem } from '@/components/ui/Form'
import { TbFilter, TbFilterOff } from 'react-icons/tb'
import { useForm, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import type { ZodType } from 'zod'
import useTranslation from '@/utils/hooks/useTranslation'
import { FilterFormSchema } from '../types'
import { useGetStocks } from '@/hooks/stock'

const validationSchema: ZodType<FilterFormSchema> = z.object({
    stockFilter: z.string(),
    typeFilter: z.string(),
    statusFilter: z.string(),
    dateRangeFilter: z.object({
        startDate: z.string(),
        endDate: z.string(),
    }),
    filterColumns: z.array(z.string()),
})

const ConsumableTableFilter = () => {
    const { t } = useTranslation()
    const { data: stocks = [] } = useGetStocks()
    const [filterIsOpen, setFilterIsOpen] = useState(false)

    // Create stock options for the select
    const stockOptions = [
        { value: 'all', label: t('nav.shared.all') },
        ...stocks.map((stock) => ({
            value: stock.id,
            label: stock.name,
        })),
    ]

    const { handleSubmit, control, reset, watch } = useForm<FilterFormSchema>({
        defaultValues: {
            stockFilter: '',
        },
        resolver: zodResolver(validationSchema),
    })

    const onSubmit = (values: FilterFormSchema) => {
        console.log('Filter form submitted with values:', values)

        setFilterIsOpen(false)
    }

    const handleClearFilters = () => {
        reset({
            stockFilter: '',
        })
    }

    const handleClose = () => {
        setFilterIsOpen(false)
    }

    // Check if any filters are active
    const hasActiveFilters = Boolean(
        watch('stockFilter') ||
            watch('typeFilter') ||
            watch('statusFilter') ||
            (watch('dateRangeFilter')?.startDate &&
                watch('dateRangeFilter')?.endDate),
    )

    return (
        <>
            <Button
                icon={hasActiveFilters ? <TbFilterOff /> : <TbFilter />}
                variant={hasActiveFilters ? 'solid' : 'default'}
                size="sm"
                className={`shadow-sm hover:shadow-md transition-all duration-200 ${
                    hasActiveFilters
                        ? 'bg-orange-500 hover:bg-orange-600 text-white'
                        : 'border-gray-300 hover:border-gray-400'
                }`}
                onClick={() => setFilterIsOpen(true)}
            >
                {t('nav.shared.filter')}
                {hasActiveFilters && (
                    <span className="ml-1 px-1.5 py-0.5 text-xs bg-white bg-opacity-20 rounded-full">
                        {[watch('stockFilter')].filter(Boolean).length}
                    </span>
                )}
            </Button>

            <Drawer
                title={t('nav.shared.filter')}
                isOpen={filterIsOpen}
                footer={
                    <div className="flex justify-around items-center w-full ">
                        <Button
                            className=""
                            variant="plain"
                            onClick={handleClearFilters}
                        >
                            {t('nav.shared.clear')}
                        </Button>
                        <Button
                            variant="solid"
                            onClick={handleSubmit(onSubmit)}
                        >
                            {t('nav.shared.confirm')}
                        </Button>
                    </div>
                }
                width={400}
                onClose={handleClose}
                onRequestClose={handleClose}
            >
                <Form
                    className="h-full"
                    containerClassName="flex flex-col justify-between h-full"
                    onSubmit={handleSubmit(onSubmit)}
                >
                    <div className="space-y-4">
                        {/* Stock Filter */}
                        <FormItem label={t('nav.Stock.filterByStock')}>
                            <Controller
                                name="stockFilter"
                                control={control}
                                render={({ field }) => (
                                    <Select
                                        options={stockOptions}
                                        placeholder={t('nav.Stock.selectStock')}
                                        value={stockOptions.find(
                                            (option) =>
                                                option.value === field.value,
                                        )}
                                        onChange={(option) =>
                                            field.onChange(option?.value || '')
                                        }
                                    />
                                )}
                            />
                        </FormItem>
                    </div>
                </Form>
            </Drawer>
        </>
    )
}

export default ConsumableTableFilter
