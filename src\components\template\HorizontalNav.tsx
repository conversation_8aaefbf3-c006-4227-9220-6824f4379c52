import HorizontalMenuContent from './HorizontalMenuContent'
import { useSelector } from 'react-redux'
import appConfig from '@/configs/app.config'
import navigationConfig from '@/configs/navigation.config'
import { RootState } from '@/store/store'

const HorizontalNav = ({
    translationSetup = appConfig.activeNavTranslation,
}: {
    translationSetup?: boolean
}) => {
    const currentRouteKey = useSelector(
        (state: RootState) => state.routeKey.currentRouteKey,
    )

    const userAuthority = localStorage.getItem('roles')

    return (
        <HorizontalMenuContent
            navigationTree={navigationConfig}
            routeKey={currentRouteKey}
            userAuthority={userAuthority?.split(',') as string[]}
            translationSetup={translationSetup}
        />
    )
}

export default HorizontalNav
