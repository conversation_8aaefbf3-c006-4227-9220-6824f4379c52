import ApiService from '@/services/ApiService'
import type { SignInCredential, SignInResponse } from '@/@types/auth'

export interface OrganizationalNode {
    code: string
    name: string
}

export interface User {
    id: string
    firstName: string
    lastName: string
    fullName: string
    email: string
    phoneNumber: string
    profilePictureUrl: string
    status: number
    mustChangePassword: boolean
    userName: string
    organizationalNodes: OrganizationalNode[]
}

export async function signInApi(data: SignInCredential) {
    return ApiService.post<SignInResponse, SignInCredential>('/Auth', data)
}

export async function signOutApi() {
    const token = localStorage.getItem('token')
    const refreshToken = localStorage.getItem('refreshToken')
    return ApiService.post('/Auth/revoke-token', { token, refreshToken })
}

export async function refreshTokenApi() {
    const token = localStorage.getItem('token')
    const refreshToken = localStorage.getItem('refreshToken')
    return ApiService.post<void>('/Auth/refresh-token', {
        token,
        refreshToken,
    })
}

export async function getAccountApi() {
    return ApiService.get<User>('/Users/<USER>')
}

export async function changePasswordApi(data: {
    currentPassword: string
    newPassword: string
}) {
    return ApiService.post<void>('/Users/<USER>', data)
}
