/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { useState, useEffect } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import Container from '@/components/shared/Container'
import AdaptiveCard from '@/components/shared/AdaptiveCard'
import Button from '@/components/ui/Button'
import Notification from '@/components/ui/Notification'
import toast from '@/components/ui/toast'
import Loading from '@/components/shared/Loading'
import WarehouseForm from './components/WarehouseForm'
import useTranslation from '@/utils/hooks/useTranslation'
import { TbArrowLeft, TbBuilding } from 'react-icons/tb'
import type { WarehouseForm as WarehouseFormType } from '@/@types/warehouse'
import {
    useGetWarehouseById,
    useUpdateWarehouse,
    useUpdateWarehouseStatus,
} from '@/hooks/warehouses'

const WarehouseEdit = () => {
    const { t } = useTranslation()
    const navigate = useNavigate()
    const { warehouseId } = useParams<{ warehouseId: string }>()

    const { data: warehouse, isLoading } = useGetWarehouseById(warehouseId!)
    const { mutate: updateWarehouseStatus } = useUpdateWarehouseStatus()
    const { mutate: updateWarehouse } = useUpdateWarehouse()

    const [loading, setLoading] = useState(false)

    const handleBack = () => {
        navigate('/warehouses/warehouses')
    }

    const handleSubmit = async (data: WarehouseFormType) => {
        if (!warehouseId) return

        setLoading(true)
        try {
            await updateWarehouse({ id: warehouseId, warehouse: data })
            toast.push(
                <Notification type="success">
                    {t('nav.warehouses.updateSuccess')}
                </Notification>,
                { placement: 'top-center' },
            )
            navigate('/warehouses/warehouses')
        } catch (error: any) {
            const errorMessage = error?.errors
            toast.push(
                <Notification type="danger">
                    {errorMessage
                        ? errorMessage[0]?.description
                        : error?.status}
                </Notification>,
                { placement: 'top-center' },
            )
        } finally {
            setLoading(false)
        }
    }

    const handleCancel = () => {
        navigate('/warehouses/warehouses')
    }

    if (isLoading) {
        return (
            <Container>
                <div className="flex items-center justify-center h-96">
                    <Loading loading={true} />
                </div>
            </Container>
        )
    }

    if (!warehouse) {
        return (
            <Container>
                <div className="text-center py-12">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                        {t('nav.warehouses.warehouseNotFound')}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400 mb-4">
                        {t('nav.warehouses.warehouseNotFoundDesc')}
                    </p>
                    <Button onClick={handleBack}>
                        {t('nav.shared.backToList')}
                    </Button>
                </div>
            </Container>
        )
    }

    return (
        <Container>
            {/* Beautiful Breadcrumb Navigation */}
            <div className="mb-8">
                {/* Breadcrumb */}
                <div className="flex items-center gap-2 mb-6">
                    <button
                        type="button"
                        className="flex items-center gap-2 text-sm font-medium text-gray-600 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400 transition-colors duration-200 group"
                        onClick={handleBack}
                    >
                        <TbArrowLeft className="w-4 h-4 transition-transform duration-200 group-hover:-translate-x-1" />
                        <span className="hover:underline">
                            {t('nav.warehouses.warehouses')}
                        </span>
                    </button>
                </div>
            </div>

            <AdaptiveCard className="shadow-lg">
                <div className="p-6">
                    <div className="flex justify-between items-end gap-3 mb-6 pb-4 border-b border-gray-200 dark:border-gray-700">
                        <div className="flex items-center gap-3">
                            <div className="flex items-center justify-center w-10 h-10 bg-amber-100 dark:bg-amber-900/30 rounded-lg">
                                <TbBuilding className="w-5 h-5 text-amber-600 dark:text-amber-400" />
                            </div>
                            <div>
                                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                    {t('nav.warehouses.warehouseInformation')}
                                </h3>
                                <p className="text-sm text-gray-600 dark:text-gray-400">
                                    {t('nav.warehouses.updateWarehouseDetails')}
                                </p>
                            </div>
                        </div>
                        <Button
                            size="sm"
                            type="button"
                            disabled={loading}
                            variant="solid"
                            className={
                                warehouse?.status === 1
                                    ? 'bg-blue-500 hover:bg-blue-600 border-blue-500 hover:border-blue-600'
                                    : 'bg-emerald-500 hover:bg-emerald-600 border-emerald-500 hover:border-emerald-600'
                            }
                            onClick={() => {
                                if (warehouse) {
                                    updateWarehouseStatus({
                                        id: warehouse.id,
                                        statusUpdate: {
                                            newStatus:
                                                warehouse.status === 1 ? 2 : 1,
                                        },
                                    })

                                    toast.push(
                                        <Notification
                                            title=""
                                            type="success"
                                            duration={2500}
                                        >
                                            {t('nav.shared.updateStatus')}
                                        </Notification>,
                                        {
                                            placement: 'top-center',
                                        },
                                    )
                                    handleBack()
                                }
                            }}
                        >
                            {warehouse?.status === 1
                                ? t('nav.GlobalActions.frozen')
                                : t('nav.GlobalActions.approve')}
                        </Button>
                    </div>

                    <WarehouseForm
                        mode="edit"
                        initialData={warehouse}
                        loading={loading}
                        onSubmit={handleSubmit}
                        onCancel={handleCancel}
                    />
                </div>
            </AdaptiveCard>
        </Container>
    )
}

export default WarehouseEdit
